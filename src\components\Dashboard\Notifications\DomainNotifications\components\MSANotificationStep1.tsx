import { Box, Select } from '@contentful/f36-components'
import React from 'react'
import {
  DomainNotiState,
  UpdateNotiStateFunction,
} from '../../utils/notifications.interface'

import FormControlComp from '../../../../Shared/FormControlComp'
import DetailsSection from '../../components/DetailsSection'
import SourceSection from '../../components/SourceSection'
import {
  StickyNotificationTemplates,
  TypeOfNotifications,
} from '../../utils/constants'

/**
 * This component renders the 1st step of the Multi-Step Notification (MSN) creation form.
 * It displays a dropdown for selecting the type of notification (Sticky or Realtime).
 * If the type of notification is Sticky, it also renders a dropdown for selecting the template.
 * The component takes in the current notification state and an update function to update the state.
 * When the user selects a different type of notification or template, the component updates the state accordingly.
 * The component also renders the SourceSection and DetailsSection components.
 */
function MSANotificationStep1({
  pageSelector,
  CTASelector,
  carouselSelector,
  carouselRemove,
  handleFileChange,
  selectSingleAsset,
  state,
  updateState,
}: {
  pageSelector: any
  CTASelector: any
  carouselRemove: any
  carouselSelector: any
  handleFileChange: any
  selectSingleAsset: any
  state: DomainNotiState
  updateState: UpdateNotiStateFunction<DomainNotiState>
}) {
  return (
    <Box className='flex justify-start items-start w-full h-full flex-col'>
      <Box className='flex w-full justify-start items-center gap-5 pt-5'>
        <FormControlComp
          label='Type of Notification'
          tooltip='Select either Sticky Notification or Realtime Notification'
          isRequired
          className='smallSelect'
        >
          <Select
            value={state?.typeOfNotification}
            onChange={(e: any) => {
              updateState({
                typeOfNotification: e?.target?.value,
                stickyNotificationTemplate: '',
                selectedPage: null,
                selectedCTA: null,
                url: '',
                externalLink: '',
                carouselData: [],
                thumbnail: {
                  url: '',
                  status: '',
                },
              })
            }}
          >
            <Select.Option value='' isDisabled>
              Select
            </Select.Option>
            {TypeOfNotifications?.map((option) => (
              <Select.Option value={option?.value}>
                {option?.title}
              </Select.Option>
            ))}
          </Select>
        </FormControlComp>

        {state?.typeOfNotification === '1' && (
          <FormControlComp
            label='Template'
            tooltip='Select template for Sticky notification'
            isRequired
            className='smallSelect'
          >
            <Select
              value={state?.stickyNotificationTemplate}
              onChange={(e: any) => {
                updateState({
                  specificPages: [],
                  categoriesPages: [],
                  selectedCTA: null,
                  selectedPage: null,
                  carouselData: [],
                  stickyNotificationTemplate: e?.target?.value,
                })
              }}
            >
              <Select.Option value='' isDisabled>
                Select
              </Select.Option>
              {StickyNotificationTemplates?.map((option) => (
                <Select.Option value={option?.value}>
                  {option?.title}
                </Select.Option>
              ))}
            </Select>
          </FormControlComp>
        )}
      </Box>
      <Box className={'flex justify-start items-start gap-5 w-full h-full '}>
        <SourceSection
          state={state}
          updateState={updateState}
          carouselRemove={carouselRemove}
          carouselSelector={carouselSelector}
          CTASelector={CTASelector}
          pageSelector={pageSelector}
          fromMsa={true}
        />
        <DetailsSection
          selectSingleAsset={selectSingleAsset}
          handleFileChange={handleFileChange}
          state={state}
          updateState={updateState}
          fromMsa={true}
        />
      </Box>
    </Box>
  )
}

export default MSANotificationStep1
