import { Box, Button } from '@contentful/f36-components'
import { Divider } from 'antd'
import React from 'react'
import { Tooltip } from '../../../atoms'
import Info from '../../../../assets/icons/Info'

interface Props {
  pageSettingCategories: any
  activeCategory: string | number
  setActiveCategory: any
  actionHandler: any
}

function ModalSidebar({
  activeCategory,
  pageSettingCategories,
  setActiveCategory,
  actionHandler,
}: Props) {
  return (
    <aside className='customiserSidebar'>
      <div className={'listRoot'}>
        <div className={'categoryRoot'}>
          <ul>
            {pageSettingCategories.map(
              (
                {
                  title,
                  isEnabled,
                  type,
                  action,
                  isDisabled = false,
                  disabledtooltip,
                  isLoading = false,
                  loadingText,
                }: {
                  title: string
                  isEnabled: boolean
                  isDisabled?: boolean
                  type: string
                  action: any
                  disabledtooltip?: string
                  isLoading?: boolean
                  loadingText?: string
                },
                index: number
              ) => {
                if (type === 'divider') {
                  return (
                    <Divider
                      style={{
                        marginBlock: 6,
                      }}
                      key={`modalsidebar-${index}`}
                    />
                  )
                }
                if (type === 'button') {
                  return (
                    <Box className='ml-2 py-2' key={`modalsidebar-${index}`}>
                      <Button
                        variant='primary'
                        onClick={actionHandler?.[action]}
                        className='rounded-md'
                        size='small'
                        isLoading={isLoading}
                        isDisabled={isLoading}
                      >
                        {isLoading && loadingText ? loadingText : title}
                      </Button>
                    </Box>
                  )
                }
                if (type === 'category' && isEnabled) {
                  return (
                    <li
                      className={`${activeCategory === title ? 'active' : ''} ${
                        isDisabled ? 'disabled' : 'pointer'
                      } catList categoryListItem py-2 pl-4`}
                      onClick={() => {
                        if (isDisabled) {
                          return
                        }
                        setActiveCategory(title)
                      }}
                      key={`modalsidebar-${index}`}
                    >
                      {isDisabled && disabledtooltip ? (
                        <Tooltip
                          title={disabledtooltip}
                          className=' !flex !flex-row items-center gap-2'
                        >
                          {title}
                          <Info className='ml-1 text-gray-500' />
                        </Tooltip>
                      ) : (
                        title
                      )}
                    </li>
                  )
                }
              }
            )}
          </ul>
        </div>
      </div>
    </aside>
  )
}

export default ModalSidebar
