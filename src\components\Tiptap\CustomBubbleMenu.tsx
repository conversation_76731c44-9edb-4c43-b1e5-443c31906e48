/**
 * CustomBubbleMenu Component - Contextual Editing Menu
 *
 * This component provides a contextual bubble menu that appears when text is selected
 * in the Tiptap editor. It offers quick access to formatting options directly at the
 * point of selection, enhancing the user experience with contextual controls.
 *
 * Key Features:
 * - Appears automatically when text is selected
 * - Basic text formatting (bold, italic, underline)
 * - Contextual positioning near selected text
 * - Active state indication for applied formats
 * - Responsive design that adapts to selection
 * - Real-time editor state synchronization
 * - Seamless integration with editor workflow
 *
 * Architecture:
 * - Built on Tiptap's BubbleMenu component
 * - Uses useCurrentEditor hook for editor state
 * - Conditional rendering based on format state
 * - CSS class-based active state management
 *
 * The buttons are conditionally rendered based on the current state of the editor.
 * If the current selection is bold, the bold button is rendered with the class 'is-active'.
 * The same applies for the italic and underline buttons.
 */

import { BubbleMenu, useCurrentEditor } from '@tiptap/react'
import React from 'react'
 *
 * When a button is clicked, the corresponding formatting action is executed on the current selection.
 *
 * The bubble menu is rendered if and only if the current editor is defined.
 */
const CustomBubbleMenu = () => {
  const { editor } = useCurrentEditor()
  return (
    <>
      {editor && (
        <BubbleMenu
          className='bubble-menu'
          tippyOptions={{ duration: 100 }}
          editor={editor}
        >
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={editor.isActive('bold') ? 'is-active' : ''}
          >
            Bold
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive('italic') ? 'is-active' : ''}
          >
            Italic
          </button>
          <button
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            className={editor.isActive('underline') ? 'is-active' : ''}
          >
            Underline
          </button>
        </BubbleMenu>
      )}
    </>
  )
}

export default CustomBubbleMenu
