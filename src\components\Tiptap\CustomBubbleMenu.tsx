import { BubbleMenu, useCurrentEditor } from '@tiptap/react'
import React, { useState } from 'react'
import CustomiserV2 from '../CustomiserV2'

const CustomBubbleMenu = ({ fieldId }: { fieldId: string }) => {
  const { editor } = useCurrentEditor()
  const [isCustomiserOpen, setIsCustomiserOpen] = useState(false)

  const handleCustomiserClose = () => setIsCustomiserOpen(false)

  return (
    <>
      {editor && (
        <BubbleMenu
          className='bubble-menu'
          tippyOptions={{ duration: 100, offset: [50, -70] }}
          editor={editor}
        >
          {/*  {!isCustomiserOpen && ( */}
          <>
            <button
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={editor.isActive('bold') ? 'is-active' : ''}
            >
              Bold
            </button>
            <button
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={editor.isActive('italic') ? 'is-active' : ''}
            >
              Italic
            </button>
            <button
              onClick={() => editor.chain().focus().toggleUnderline().run()}
              className={editor.isActive('underline') ? 'is-active' : ''}
            >
              Underline
            </button>

            <button onClick={() => setIsCustomiserOpen(!isCustomiserOpen)}>
              Stylr
            </button>
          </>
          {/*   )} */}
          {isCustomiserOpen && (
            <CustomiserV2
              handleClose={handleCustomiserClose}
              fieldId={fieldId}
              isCustomiserOpen={isCustomiserOpen}
            />
          )}
        </BubbleMenu>
      )}
    </>
  )
}

export default CustomBubbleMenu
