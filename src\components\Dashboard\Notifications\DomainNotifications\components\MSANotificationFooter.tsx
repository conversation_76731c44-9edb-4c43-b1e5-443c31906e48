import { Box, Button, ModalControls, Text, Tooltip } from '@contentful/f36-components'
import React from 'react'
import NextButton from '../../../../Buttons/NextButton'
import PrevButton from '../../../../Buttons/PrevButton'
import SaveButton from '../../../../Buttons/SaveButton'
import { DomainNotiState, UpdateNotiStateFunction } from '../../utils/notifications.interface'
import { setIsAddNewNotification } from '../../../../../redux/slices/dashboard/notificationsSlice'
import { useAppDispatch } from '../../../../../redux/store'

function MSANotificationFooter({
                                 checkFirstStepDisable,
                                 checkSecondStepDisable,
                                 state,
                                 updateState,
                                 saveDisable,
                                 clearAllStates,
                               }: {
  checkFirstStepDisable: Function
  checkSecondStepDisable: Function
  state: DomainNotiState
  saveDisable: boolean
  updateState: UpdateNotiStateFunction<DomainNotiState>
  clearAllStates: () => void
}) {
  const { status, isLoading, activeStep, typeOfNotification } = state

  const dispatch = useAppDispatch()


  return (
    <Box className="w-full flex flex-col justify-end items-center">
      <hr className="endHorizontalLine w-full" />
      <ModalControls className="pt-4">
        {status === 'success' ? (
          <Text fontColor="green500"> Notification created successfully</Text>
        ) : status === 'error' ? (
          <Text fontColor="red500"> Notification creation failed</Text>
        ) : (
          <></>
        )}
        {activeStep === 0 ? (

          <>
            <Tooltip content={'Cancel'} placement={'top'}>
              <Button
                variant="secondary"
                size={'small'}
                onClick={() => {
                  clearAllStates()
                  dispatch(setIsAddNewNotification(false))
                }}>
                Cancel
              </Button>
            </Tooltip>
            <NextButton
              helpText={
                !checkSecondStepDisable() ? 'Please fill all the fields' : 'Next'
              }
              onClick={() => updateState({ activeStep: typeOfNotification === '3' ? 1 : 2 })}
              isDisabled={!checkSecondStepDisable()}
            />
          </>
        ) : activeStep === 1 ? (
          <>
            <PrevButton
              helpText="Amend/Update"
              onClick={() => updateState({ activeStep: 0 })}
            />
            <NextButton
              helpText="Next"
              onClick={() => updateState({ activeStep: 3 })}
              isDisabled={!checkSecondStepDisable()}
            />
          </>
        ) : activeStep === 2 ? (
            <>
              <PrevButton
                helpText="Amend/Update"
                onClick={() => updateState({ activeStep: typeOfNotification === '3' ? 1 : 0 })}
              />
              <NextButton
                helpText="Next"
                onClick={() => updateState({ activeStep: 3 })}
                isDisabled={!checkSecondStepDisable()}
              />
            </>
          ) :
          <>
            <PrevButton
              helpText="Amend/Update"
              onClick={() =>
                updateState({
                  activeStep: typeOfNotification === '3' ? 1 : 2,
                })
              }
              isDisabled={saveDisable || isLoading}
            />
            <SaveButton
              helpText="Create Notification"
              onClick={() => updateState({ showConfirmBox: true })}
              isLoading={isLoading}
              isDisabled={saveDisable || isLoading}
            />
          </>}
      </ModalControls>
    </Box>
  )
}

export default MSANotificationFooter
