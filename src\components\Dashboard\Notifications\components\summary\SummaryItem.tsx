import { AssetCard, AssetStatus } from '@contentful/f36-components'
import React from 'react'
import { Thumbnail } from '../../utils/notifications.interface'

/**
 * SummaryItem component renders a single item in the summary section of the notification settings.
 * It renders a label with the given title and a value based on the type.
 * If the type is "String", it renders the value as a plain text.
 * If the type is "Asset", it renders the value as an AssetCard.
 * If the type is "Links", it renders the value as a list of links.
 * If the type is "Boolean", it renders the value as a Yes/No text.
 * It also removes the item from the summary if the value is not provided or is empty.
 * @param {Object} props - The props object.
 * @prop {string} title - The title of the item.
 * @prop {'String' | 'Asset' | 'Links' | 'Boolean'} type - The type of the item.
 * @prop {string | boolean | Thumbnail | any[]} value - The value of the item.
 * @prop {Thumbnail} thumbnail - The thumbnail object for the item with type "Asset".
 * @prop {any[]} links - The links array for the item with type "Links".
 * @prop {boolean} isSmall - Whether the item should be rendered in a smaller size.
 */
function SummaryItem({
  title,
  value,
  type,
  thumbnail,
  links,
  isSmall,
}: {
  title: string
  type: 'String' | 'Asset' | 'Links' | 'Boolean'
  value?: string | boolean
  thumbnail?: Thumbnail
  links?: any[]
  isSmall?: boolean
}) {
  if (type === 'String' && !value) return null
  if (type === 'Boolean' && value === 'undefined') return null
  if (type === 'Asset' && !thumbnail?.url) return null
  if (type === 'Links' && (!links || links?.length === 0)) return null

  return (
    <div className='flex justify-start items-start w-full'>
      <span
        className={`domainName font-sans  text-base font-bold text-gray-500 ${
          isSmall ? 'min-w-[200px]' : 'min-w-[200px]'
        } `}
      >
        {title}:
      </span>
      <span className='SlugName font-medium text-base max-w-[90%] w-full flex flex-col gap-3'>
        {type === 'String' && <>{value}</>}
        {type === 'Asset' && thumbnail?.url && (
          <AssetCard
            status={thumbnail?.status as AssetStatus}
            type='image'
            src={thumbnail?.url}
            size='small'
          />
        )}
        {type === 'Links' && links && (
          <>
            {links?.map((link: any) => (
              <a
                className='SlugName font-medium text-base max-w-[90%] w-auto underline cursor-pointer text-[#414D63]'
                target='_blank'
                href={handleUrlClick(link)}
                rel='noreferrer'
              >
                {link?.fields?.internalName?.['en-CA']}
              </a>
            ))}
          </>
        )}
        {type === 'Boolean' && <>{value ? 'Yes' : 'No'}</>}
      </span>
    </div>
  )
}

export default SummaryItem

const handleUrlClick = (data: any) => {
  let url = data?.sys?.urn?.split(':::content:')[1]

  return 'https://app.contentful.com/' + url
}
