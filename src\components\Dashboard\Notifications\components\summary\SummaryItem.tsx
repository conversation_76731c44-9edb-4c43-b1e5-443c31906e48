import React from 'react'
import { AssetCard, AssetStatus } from '@contentful/f36-components'
import { Thumbnail } from '../../utils/notifications.interface'

function SummaryItem({ title, value, type, thumbnail, links, isSmall }: {
  title: string,
  type: 'String' | 'Asset' | 'Links' | 'Boolean',
  value?: string | boolean,
  thumbnail?: Thumbnail,
  links?: any[]
  isSmall?: boolean
}) {
  if (type === 'String' && !value) return null
  if (type === 'Boolean' && value === 'undefined') return null
  if (type === 'Asset' && !thumbnail?.url) return null
  if (type === 'Links' && (!links || links?.length === 0)) return null

  return <div className="flex justify-start items-start w-full">
          <span
            className={`domainName font-sans  text-base font-bold text-gray-500 ${isSmall ? 'min-w-[200px]' : 'min-w-[200px]'} `}>
            {title}:
          </span>
    <span className="SlugName font-medium text-base max-w-[90%] w-full flex flex-col gap-3">
    {type === 'String' && <>{value}</>}
      {type === 'Asset' && thumbnail?.url && <AssetCard
        status={thumbnail?.status as AssetStatus}
        type="image"
        src={thumbnail?.url}
        size="small"
      />}
      {type === 'Links' && links && <>
        {links?.map((link: any) => (
          <a
            className="SlugName font-medium text-base max-w-[90%] w-auto underline cursor-pointer text-[#414D63]"
            target="_blank"
            href={handleUrlClick(link)}
            rel="noreferrer"
          >
            {link?.fields?.internalName?.['en-CA']}
          </a>
        ))}
      </>}
      {type === 'Boolean' && <>{value ? 'Yes' : 'No'}</>}
    </span>
  </div>
}

export default SummaryItem

const handleUrlClick = (data: any) => {
  let url = data?.sys?.urn?.split(':::content:')[1]

  return 'https://app.contentful.com/' + url
}