import {
  Checkbox,
  FormControl,
  Select,
  TextInput
} from '@contentful/f36-components'
import React, { useState } from 'react'
import ColorsInputV2 from '../../ColorsInputV2'
import { getColorStyles } from '../../ColorsInputV2/utils'
import '../index.scss'
import { dataUnits } from '../utils'
const AxisFields = (props: any) => {
  const {
    axis,
    index,
    axesCount,
    axisFieldData,
    handleAFieldsUpdate,
    isAxesLineColor,
    setIsAxesLineColor,
    template
  } = props

  const isMultipleAxis = axesCount > 1
  /* const[isSettingMoadalOpen,setIsSettingMoadalOpen]=useState(false)
    const [fontStyles,setFontStyles]=useState('') */
  const [data, setData] = useState(axisFieldData ?? {})

  const handleAxesFieldsDataChange = (key: string, value: any) => {
    const dataToUpdate = {
      ...data,
      [key]: value,
    }
    setData(dataToUpdate)
    handleAFieldsUpdate(dataToUpdate)
  }

  return <>
      <FormControl
        id={`pri-dv-show-${axis}-axis-${index}`}
        className='w-100 fieldsFormControl'
      >
        <div className='SwithWithTooltip'>
          <Checkbox
            name={`show dv ${axis}-axis-${index}-line`}
            id={`show-dv-${axis}-axis-${index}-line`}
            onChange={() => handleAxesFieldsDataChange('show', !data?.['show'])}
            className='switchRoot'
            isChecked={data?.['show']}
          >
          Enable {axis?.toUpperCase()}-axis {index + 1}
        </Checkbox>
        </div>
      </FormControl>
    {data?.['show'] && (<>
      <FormControl
        id={`dv-${axis}-type`}
        className='w-100 fieldsFormControl'
      > <FormControl.Label>Select axis type</FormControl.Label>
        <Select
          id={`dv-${axis}-axis-${index}-type-controlled`}
          name={`dv-${axis}-axis-${index}-controlled`}
          value={data?.['type'] || ''}
          onChange={(e) => {
            handleAxesFieldsDataChange('type', e.target.value)
          }}
        >
          <Select.Option value='' isDisabled>
            Type
          </Select.Option>
          {['category', 'value'].map((option) => (
            <Select.Option key={option} value={option}>
              {option}
            </Select.Option>
          ))}
        </Select>
      </FormControl>
      <FormControl
        id={`${axis}AxisTitle`}
        className='w-100 fieldsFormControl'
      >
        <FormControl.Label>Axis title</FormControl.Label>
        <TextInput
          value={data?.['title']}
          onChange={(e) =>
            handleAxesFieldsDataChange('title', e.target.value)
          }
          placeholder='Enter axis title name'
        />
      </FormControl>
      <FormControl
        id={`${axis}-axis-${index}-nameLocation`}
        className='w-100 fieldsFormControl'
      >
        <FormControl.Label>Select title alignment</FormControl.Label>
        <Select
          id={`dv-${axis}-${index}-axis-title-Position-controlled`}
          name={`dv-${axis}-${index}-axis-title-Position-controlled`}
          value={data?.nameLocation || ''}
          onChange={(e) =>
            handleAxesFieldsDataChange('nameLocation', e.target.value)
          }
        >
          <Select.Option value='' isDisabled>
            Select title alignment
          </Select.Option>
          {['start', 'center', 'end'].map((option) => (
            <Select.Option key={option} value={option}>
              {option}
            </Select.Option>
          ))}
        </Select>
      </FormControl>

          <FormControl
        id={`dv-${axis}-Axis-${index} nameGap`}
        className='w-100 fieldsFormControl'
      > <FormControl.Label>Specify title gap</FormControl.Label>
        <TextInput
          type='number'
          placeholder='Axis title gap'
          value={data?.['nameGap']}
          onChange={(e) =>
            handleAxesFieldsDataChange('nameGap', e.target.value)
          }
          min={1}
        />
      </FormControl>
      {data?.['type'] === 'value' && (template !== 'BarStackNormalization chart' &&
        <>
          <FormControl
            id={`${axis}-Axis-${index} Min`}
            className='w-100 fieldsFormControl'
          >
          <FormControl.Label>Minimum value</FormControl.Label>
          <TextInput
            type='number'
            placeholder='Minimum value'
            value={data?.['min']}
            onChange={(e) =>
              handleAxesFieldsDataChange('min', e.target.value)
            }
            min={1}
          />
        </FormControl>
        <FormControl
          id={`${axis}-Axis-${index} Max`}
          className='w-100 fieldsFormControl'
        > <FormControl.Label>Maximum value</FormControl.Label>
          <TextInput
            type='number'
            placeholder='Maximum value'
            value={data?.['max']}
            onChange={(e) =>
              handleAxesFieldsDataChange('max', e.target.value)
            }
            min={1}
          />
        </FormControl>
        <FormControl
          id={`${axis}-Axis-${index} interval`}
          className='w-100 fieldsFormControl'
        ><FormControl.Label>Specify interval</FormControl.Label>
          <TextInput
            type='number'
            placeholder='Specify interval'
            value={data?.['interval']}
            onChange={(e) =>
              handleAxesFieldsDataChange('interval', e.target.value)
            }
            min={1}
          />
        </FormControl>
        <FormControl
          id='dv-pri-data-unit'
          className='w-100 fieldsFormControl'
        >
          <FormControl.Label>Select data format</FormControl.Label>
          <Select
            id='dv-pri-data-unit-controlled'
            name='dv-pri-data-unit-controlled'
            value={data?.dataUnit || null}
            onChange={(e) => handleAxesFieldsDataChange('dataUnit', e.target.value || null)}
          >
            <Select.Option value="" isDisabled>
              Format data
            </Select.Option>
            {dataUnits.map(({ value, label }) => (
              <Select.Option key={label} value={value}>
                {label}
              </Select.Option>
            ))}
          </Select>
        </FormControl>
        </>
      )}
      {isMultipleAxis && (
        <>
          <FormControl
            id={`${axis}-axis-${index} placement`}
            className='w-100 fieldsFormControl'
          >
            <FormControl.Label>Select axis position</FormControl.Label>
            <Select
              id={`dv-${axis}-${index}-axis-Position-controlled`}
              name={`dv-pri-${axis}-${index}-axis-Position-controlled`}
              value={data?.axisPosition || ''}
              onChange={(e) =>
                handleAxesFieldsDataChange('axisPosition', e.target.value)
              }
            >
              <Select.Option value='' isDisabled>
                Axis position
              </Select.Option>
              {axis === 'x'
                ? ['top', 'bottom'].map((option) => (
                  <Select.Option value={option}>{option}</Select.Option>
                ))
                : ['left', 'right'].map((option) => (
                  <Select.Option value={option}>{option}</Select.Option>
                ))}
            </Select>
          </FormControl>
          <FormControl
            id={`${axis}-Axis-${index} Offset`}
            className='w-100 fieldsFormControl'
          ><FormControl.Label>Specify axis offset</FormControl.Label>
            <TextInput
              type='number'
              placeholder='Axis Offset'
              value={data?.['offset']}
              onChange={(e) =>
                handleAxesFieldsDataChange('offset', e.target.value)
              }
              min={1}
            />
          </FormControl>
        </>
      )}
      <FormControl
        id={`pri-dv-reverse-${axis}-axis ${index}-nameRotate`}
        className='w-100 fieldsFormControl'
      >
        <div className='SwithWithTooltip'>
          <Checkbox
            name={`dv reverse ${axis}-axis-${index}} nameRotate`}
            id={`dv reverse ${axis}-axis-${index}} nameRotate`}
            onChange={() =>
              handleAxesFieldsDataChange(
                'nameRotate',
                !data?.['nameRotate']
              )
            }
            className='switchRoot'
            isChecked={data?.['nameRotate']}
          >
            Rotate axis title
          </Checkbox>
        </div>
      </FormControl>
      <FormControl
        id={`dv-${axis}-axis-${index}-line`}
        className='w-100 fieldsFormControl'
      >
        <div className='SwithWithTooltip'>
          <Checkbox
            name={`show dv ${axis}-axis-${index}-line`}
            id={`show dv ${axis}-axis-${index}-line`}
            onChange={() =>
              handleAxesFieldsDataChange(
                    'showAxisLine',
                    !data?.['showAxisLine']
                  )
            }
            className='switchRoot'
            isChecked={data?.['showAxisLine']}
          >
            Enable Axis Line
          </Checkbox>
        </div>
      </FormControl>
      <FormControl
        id={`dv-pri-${axis}-axis-${index}-lineColor`}
        className='w-100'
        style={{ display: 'flex', alignItems: 'center', margin: '10px 0 10px -1rem' }}
      >
        <ColorsInputV2
          heading={`${axis}-axis-${index + 1} line`}
          onClose={() => {
            setIsAxesLineColor('')
          }}
          onOpen={() => {
            setIsAxesLineColor(`${axis}${index}AxisLineColor`)
          }}
          activeColor={data?.[`${axis}AxisLineColor`]}
          onChange={(v: any) => {
            const color = getColorStyles(v).background
            handleAxesFieldsDataChange(`${axis}AxisLineColor`, color)

            // setShowColorPicker(false)
          }}
          pickerType='Colorama'
          // pickerType='Button'
          disabled={{
            // Accents: true,
            // Primaries: true,
            Gradients: true,
            // Neutrals: true,
            // Secondaries: true,
          }}
          pickerdisabled={
            isAxesLineColor &&
            isAxesLineColor !== `${axis}${index}AxisLineColor`
          }
        />
        <p className="css-1k16bbs">Axis colour</p>
      </FormControl>
      <FormControl
        id={`pri-dv-reverse-${axis}-axis ${index}-lableRotate`}
        className='w-100 fieldsFormControl'
      >
        <div className='SwithWithTooltip'>
          <Checkbox
            name={`dv reverse ${axis}-axis-${index}} lableRotate`}
            id={`dv reverse ${axis}-axis-${index}} lableRotate`}
            onChange={() =>
              handleAxesFieldsDataChange(
                'lableRotate',
                !data?.['lableRotate']
              )
            }
            className='switchRoot'
            isChecked={data?.['lableRotate']}
          >
            Rotate axis labels
          </Checkbox>
        </div>
      </FormControl>

      <FormControl
        id={`dv-reverse-${axis}-axis ${index}-direction`}
            className='w-100 fieldsFormControl'
          >
            <div className='SwithWithTooltip'>
              <Checkbox
            name={`dv reverse ${axis}-axis-${index}} direction`}
            id={`dv reverse ${axis}-axis-${index}} direction`}
                onChange={() =>
                  handleAxesFieldsDataChange(
                    'reverseAxis',
                    !data?.['reverseAxis']
                  )
                }
                className='switchRoot'
            isChecked={data?.['reverseAxis']}
              >
            Reverse axis sorting
          </Checkbox>
        </div>
      </FormControl>
      {isMultipleAxis && (
        <>
          <FormControl
            id={`dv-reverse-${axis}-axis-${index}-alignTicks`}
            className='w-100 fieldsFormControl'
          >
            <div className='SwithWithTooltip'>
              <Checkbox
                name={`dv reverse ${axis}-axis-${index}} alignTicks`}
                id={`dv reverse ${axis}-axis-${index}} alignTicks`}
                onChange={() =>
                  handleAxesFieldsDataChange(
                    'alignTicks',
                    !data?.['alignTicks']
                  )
                }
                className='switchRoot'
                isChecked={data?.['alignTicks']}
              >
                Align data
              </Checkbox>
            </div>
          </FormControl>
        </>
      )}
    </>
    )}
  </>
}
export default AxisFields
