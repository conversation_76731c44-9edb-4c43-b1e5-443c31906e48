import { Card } from 'antd'
import React, { useEffect, useState } from 'react'
import { sortValues } from '../../../../../globals/utils'
import { Alert, Table } from '../../../../atoms'
import { EntryLink } from '../experiment-const'
import { ExperimentationCRUD } from '../experimentation-helper'

const ExperimentRunning = ({
  initialPageData,
  data,
  ActiveDomain = '/',
}: {
  initialPageData: null | any
  data: ExperimentRootData
  ActiveDomain: string
}) => {
  const [experimentData, setExeprimentData] =
    useState<ExperimentationData | null>(null)
  useEffect(() => {
    if (
      initialPageData?.selected?.fields?.isExperimentation?.['en-CA'] &&
      !!initialPageData?.selected?.fields?.experimentationId?.['en-CA']
    ) {
      const res = ExperimentationCRUD.getOne(
        data,
        initialPageData?.selected?.fields?.experimentationId?.['en-CA'],
        'experimentationId'
      )
      if (res?.status) {
        setExeprimentData(res?.data)
      } else {
        setExeprimentData(null)
      }
    } else {
      setExeprimentData(null)
    }
  }, [initialPageData])

  return experimentData ? (
    <Card
      className='w-full h-full'
      //   style={{
      //     width: '100%',
      //     display: 'flex',
      //     flexDirection: 'column',
      //     justifyContent: 'space-between',
      //     alignItems: 'center',
      //     height: '100%',
      //   }}
    >
      <div className=' mb-6 -mt-1'>
        <h3 className='text-xl font-semibold flex items-center gap-4 justify-between'>
          This page is currently used in experiment "{' '}
          {experimentData?.experimentTitle} "
        </h3>
        <p className='text-gray-500 mt-2'>
          {experimentData?.experimentDescription}
        </p>
        <div className='text-sm text-gray-400 mt-4'>
          Routing URL:{' '}
          <a
            target='_blank'
            rel='noopener noreferrer'
            href={`${ActiveDomain}${experimentData?.masterPage}`}
            className='font-medium text-gray-700'
          >
            {experimentData?.masterPage}{' '}
            {/* <RxExternalLink className='inline ml-1 w-6 h-6' /> */}
          </a>
        </div>
      </div>
      <Table
        dataSource={experimentData?.experimentationPages}
        pagination={{
          // showSizeChanger: true,
          // pageSizeOptions: [10, 20, 50, 100],
          defaultPageSize: 20,
          // showQuickJumper: true,
          // showPrevNextJumpers: true,
        }}
        scroll={{ x: 'auto' }}
        locale={{ emptyText: 'No data For Page To View' }}
        columns={[
          // {
          //   title: 'Title',
          //   dataIndex: 'title',
          //   key: 'title',
          //   sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
          //     sortValues(a, b, 'title'),
          //   width: 150,
          // },
          {
            title: 'Title',
            dataIndex: 'internalName',
            key: 'internalName',
            sorter: (a: ExperimentationPage, b: ExperimentationPage) =>
              sortValues(a, b, 'internalName'),
            width: 250,
            render: (link: string, row) =>
              row?.id ? (
                <a
                  href={`${EntryLink}/${row?.id}`}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='hover:text-blue-400 flex justify-between items-center'
                >
                  {`${row?.internalName ?? 'N/A'}`}
                </a>
              ) : (
                'N/A'
              ),
          },
          // {
          //   title: 'Internal URL',
          //   dataIndex: 'link',
          //   key: 'link',
          //   width: 200,
          //   render: (link: string, row) => {
          //     const slug = row?.slug

          //     return (
          //       <a
          //         href={`${ActiveDomain}${slug}`}
          //         target='_blank'
          //         rel='noopener noreferrer'
          //         className='hover:text-blue-400 flex justify-between items-center'
          //       >
          //         {slug}
          //       </a>
          //     )
          //   },
          // },
          {
            title: 'External URL',
            dataIndex: 'slug',
            key: 'slug',
            width: 200,
            render: (_, row, index) => {
              const { masterPage, historyCount = 0 } = experimentData || {}
              const variant = `${masterPage}?variant=${historyCount + index}`
              return (
                <a
                  href={`${ActiveDomain}${variant}`}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='hover:text-blue-400 flex justify-between items-center'
                >
                  {variant}
                </a>
              )
            },
          },
        ]}
      />
    </Card>
  ) : (
    <div className='flex justify-center items-center w-full h-full'>
      <Alert
        description='No Data Found Related Page or Experiment '
        message='Please Contact Dev Team.'
      />
    </div>
  )
}

export default ExperimentRunning
