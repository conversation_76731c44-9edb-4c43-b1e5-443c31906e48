import type {
  AccessAPI,
  ArchiveableAction,
  CrudAction,
  PublishableAction,
} from '@contentful/app-sdk'

type EntryAction = CrudAction | PublishableAction | ArchiveableAction

type ExtendedAccessAPI = {
  canPerformActionOnEntryOfType: (
    action: EntryAction,
    contentTypeId: string
  ) => Promise<boolean>
}

/**
 * A default implementation of `canPerformActionOnEntryOfType` that always
 * resolves to `true`, effectively allowing all actions on any content type.
 * This can be used as a fallback when the actual permission check is not
 * provided.
 */

const AllowActionsOnContentType: ExtendedAccessAPI['canPerformActionOnEntryOfType'] =
  () => Promise.resolve(true)

/**
 * Provides an API for checking user permissions.
 *
 * @param {AccessAPI & Partial<ExtendedAccessAPI>} accessApi - A partial
 *   implementation of the `AccessAPI` with the `canPerformActionOnEntryOfType`
 *   method. If the `canPerformActionOnEntryOfType` method is not provided,
 *   it defaults to allowing all actions on any content type.
 * @returns {Object} An object with `canPerformAction` and
 *   `canPerformActionOnEntryOfType` methods for checking user permissions.
 */
export function useAccessApi(
  accessApi: AccessAPI & Partial<ExtendedAccessAPI>
) {
  const canPerformAction = accessApi.can
  const canPerformActionOnEntryOfType =
    accessApi.canPerformActionOnEntryOfType ?? AllowActionsOnContentType

  return { canPerformAction, canPerformActionOnEntryOfType }
}
