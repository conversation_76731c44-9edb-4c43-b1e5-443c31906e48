import * as contentful from 'contentful-management'
import { ENVIRONMENT, SPACE_ID, TOKEN } from '../../constant/variables'
import { getEntryDataById } from '../utils'

export type SCOPE = 'MSA' | 'AGL' | 'FIA' | 'O11' | 'REO' | 'VER'

export type EntryType = 'Crosspost' | 'Experimentation' | 'Locale' | 'DV'

export const CreateConfigurationEntry = async ({
  data,
  internalName,
  type,
  scope,
}: {
  data: any
  type: EntryType
  internalName: string
  scope: SCOPE
}) => {
  const fields = {
    internalName: {
      'en-CA': internalName,
    },
    type: {
      'en-CA': type,
    },
    scope: {
      'en-CA': scope,
    },
    data: {
      'en-CA': {
        content: [
          {
            content: [
              {
                data: {},
                marks: [],
                nodeType: 'text',
                value: JSON.stringify(data),
              },
            ],
            data: {},
            nodeType: 'paragraph',
          },
        ],
        data: {},
        nodeType: 'document',
      },
    },
  }

  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  let payload: any = {
    fields: fields,
  }

  const res = await client
    .getSpace(SPACE_ID)
    .then((space) => {
      return space.getEnvironment(ENVIRONMENT).then(async (environment) => {
        const res = await environment.createEntry('configurations', payload)

        return res
      })
    })
    .catch((error) => {
      return error
    })

  const newContentId = res?.sys?.id || ''

  return newContentId
}

export const UpdateConfigurationEntry = async ({
  contentId,
  data,
}: {
  contentId: string
  data: any
}) => {
  if (contentId) {
    const response = await getEntryDataById(contentId).then((res: any) => {
      return res.fields
    })

    let updatedPayload = {
      ...response,
      data: {
        'en-CA': {
          content: [
            {
              content: [
                {
                  data: {},
                  marks: [],
                  nodeType: 'text',
                  value: JSON.stringify(data),
                },
              ],
              data: {},
              nodeType: 'paragraph',
            },
          ],
          data: {},
          nodeType: 'document',
        },
      },
    }

    const client = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN,
    })

    const res = await client
      .getSpace(SPACE_ID)
      .then((space) => {
        return space.getEnvironment(ENVIRONMENT).then((environment) => {
          return environment.getEntry(contentId).then(async (entry) => {
            entry.fields = { ...entry.fields, ...updatedPayload }

            const res = await entry.update()

            return res
          })
        })
      })
      .catch((error) => console.error(error))
    return res
  }
}
