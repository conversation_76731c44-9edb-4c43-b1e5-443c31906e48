import { useCurrentEditor } from '@tiptap/react'
import { EditorAppSDK } from 'contentful-ui-extensions-sdk'
import React, { useEffect } from 'react'
interface TestFuncProps {
  sdk: EditorAppSDK
  fieldId: string
  currentLocale: string
}

const TipTapChangeDetector: React.FC<TestFuncProps> = ({
  sdk,
  fieldId,
  currentLocale,
}) => {
  const { editor } = useCurrentEditor()

  useEffect(() => {
    if (currentLocale === 'en-CA' || !sdk || !fieldId) return

    // Function to update editor content when value changes
    const updateEditorContent = (value: any) => {
      if (value) {
        editor?.commands.setContent(value)
      }
    }

    // Subscribe to value changes for the specific locale and field
    const unsubscribe = sdk.entry.fields[fieldId]
      .getForLocale(currentLocale)
      .onValueChanged(updateEditorContent)

    // Cleanup the subscription on unmount
    return () => {
      unsubscribe()
    }
  }, [currentLocale, fieldId, sdk, editor])

  return <></>
}

export default TipTapChangeDetector
