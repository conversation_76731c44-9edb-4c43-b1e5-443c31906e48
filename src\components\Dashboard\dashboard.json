{"global": {"value": "global", "categories": [{"title": "Content Discovery", "isEnabled": true}, {"title": "Data Visualisation", "subcategories": [{"title": "General", "value": "dv-general", "fields": ["Show Title", "Title Alignment", "Is Graph Zoomable", "Show Legend", "Legend Alignment", "Legend Orientation"]}, {"title": "Chart & Dimensions", "value": "dv-chart", "fields": ["Default Chart", "Dimensions Default Color"]}, {"title": "Styles", "value": "dv-styles", "fields": ["Show Grid", "Chart Bg Color", "Grid Bg Color", "Grid Line Color", "Grid Format", "Grid Line Style", "Border Width", "Border Type", "Border Shadow", "Border Color"]}, {"title": "Axes", "value": "dv-axes", "fields": ["Show Axes", "Reverse Axis Type", "Show X-axis", "Show X-axis Line", "X-axis Line Color", "Reverse X-axis", "Show Y-axis", "Show Y-axis Line", "Y-axis Line Color", "Reverse Y-axis"]}], "isEnabled": true}, {"title": "Documentation", "isEnabled": true}, {"title": "Experimentation", "isEnabled": true}, {"title": "Notifications", "isEnabled": true}]}, "msa": {"value": "msa", "categories": [{"title": "<PERSON><PERSON>", "isEnabled": true}, {"title": "Hosted Files", "isEnabled": true}, {"title": "Experimentation", "isEnabled": true}, {"title": "Languages", "isEnabled": true}, {"title": "Notifications", "isEnabled": true}]}}