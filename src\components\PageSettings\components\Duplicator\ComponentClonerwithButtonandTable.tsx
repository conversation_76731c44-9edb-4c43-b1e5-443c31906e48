// ComponentClonerwithButtonandTable.tsx
import React, { useState } from 'react'
import { useDuplicateComponent } from '../../../../globals/experimentation-util'
import CustomButton from '../../../Buttons/CustomButton'
import GlobalTable from '../../../Table'
import DuplicateModal from './DuplicateModal'

const ComponentClonerwithButtonandTable: React.FC = ({
  entryId,
}: {
  entryId: string
}) => {
  const { isLoading, progress, duplicateComponent } = useDuplicateComponent()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedEntryId, setSelectedEntryId] = useState<string>('')

  const columns = [
    { id: 'contentType', label: 'Content Type' },
    { id: 'internalName', label: 'Internal Name' },
    {
      id: 'id',
      label: 'Original Component',
      render: (value, row) => {
        return (
          <a
            href={`https://app.contentful.com/spaces/8jgyidtgyr4v/environments/${process.env.REACT_APP_CONTENTFUL_ENVIRONMENT ?? 'dev'}/entries/${value}`}
            target='_blank'
            className='hover:scale-105 text-[#3355ad] '
          >
            {value}
          </a>
        )
      },
    },
    {
      id: 'newNestedId',
      label: 'Duplicated Component',
      render: (value, row) => {
        return value ? (
          <a
            href={`https://app.contentful.com/spaces/8jgyidtgyr4v/environments/${process.env.REACT_APP_CONTENTFUL_ENVIRONMENT ?? 'dev'}/entries/${value}`}
            target='_blank'
            className='hover:scale-105 text-[#3355ad] '
          >
            {value}
          </a>
        ) : (
          'In Progress'
        )
      },
    },
  ]

  return (
    <div className='h-full w-full flex gap-10'>
      <div className='flex gap-10 items-center justify-center overflow-auto w-full flex-col'>
        <div className='w-full flex justify-start'>
          {/* <button
          onClick={() => {
            setSelectedEntryId(entryId) // Replace with actual entry ID
            setIsModalOpen(true)
          }}
          className='px-4 py-2 bg-blue-500 text-white rounded'
        >
          Open Duplicate Modal
        </button> */}
        </div>
        <GlobalTable
          columns={columns}
          data={progress?.status}
          defaultPageSize={5}
          pageSizeOptions={[5, 10, 15]}
          searchableColumns={['internalName', 'contentType']}
          height='400px'
          // paginationremove
          extraConetntAfterSearch={
            <>
              <div className='w-2/3 flex justify-end gap-5 py-4'>
                {isLoading && (
                  <div className='flex justify-center items-center'>
                    <div className='animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#56e5a9] mb-4'></div>
                  </div>
                )}
                {progress && (
                  <div className='flex justify-center items-center'>
                    {/* <p className='text-xl text-[#339bbc]'>
                      Total Components to Create:{' '}
                      <span className='font-extrabold'>
                        {progress?.status?.length || 0}
                      </span>
                    </p>
                    <p className='text-xl text-[#56e5a9]'>
                      Components Completed:
                      <span className='font-extrabold'>
                        {' '}
                        {progress?.status?.filter((el) => el?.newNestedId)
                          ?.length || 0}
                      </span>
                    </p> */}
                    <p className=' flex items-center justify-center mb-2 subtitleHeading'>
                      Components Progress:
                      <span className='font-extrabold  ml-2 flex gap-1 justify-center items-center'>
                        <span className='text-[#339bbc] text-xl'>
                          {' '}
                          {progress?.status?.filter((el) => el?.newNestedId)
                            ?.length || 0}
                        </span>
                        /{' '}
                        <span className=''>
                          {progress?.status?.length || 0}
                        </span>
                      </span>
                    </p>
                  </div>
                )}
                <CustomButton
                  onClick={() => {
                    setSelectedEntryId(entryId) // Replace with actual entry ID
                    setIsModalOpen(true)
                  }}

                  // className='px-4 py-0 h-auto bg-[#3355ad] text-white rounded'
                >
                  Clone Component
                </CustomButton>
              </div>
            </>
          }
        />
      </div>
      <DuplicateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        entryId={selectedEntryId}
        isLoading={isLoading}
        progress={progress}
        duplicateComponent={duplicateComponent}
      />
    </div>
  )
}

export default ComponentClonerwithButtonandTable
