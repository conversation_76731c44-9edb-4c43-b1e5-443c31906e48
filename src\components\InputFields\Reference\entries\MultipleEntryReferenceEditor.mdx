import { <PERSON><PERSON>, <PERSON>a, <PERSON><PERSON>, <PERSON>, <PERSON>s } from '@storybook/blocks';
import * as EditorStories from '../../stories/MultipleEntryReferenceEditor.stories';

import Readme from '../../README.md?raw';

<Markdown>{Readme}</Markdown>

```jsx
import { MultipleEntryReferenceEditor } from '@contentful/field-editor-reference';
```

<Meta of={EditorStories} />

## In Action

<Story of={EditorStories.Default} />

## With custom card

Click _Link existing entries_ to insert some references with custom layout rendered via `renderCustomCard` prop. Click again to showcase inserting
another reference rendered by the stardard card renderer.

<Story of={EditorStories.CustomCard} />

## With custom actions

Note the alternative link actions injected via the `renderCustomActions` prop.

<Story of={EditorStories.CustomActions} />

## With custom actions and custom label

Note the alternative link actions injected via the `renderCustomActions` prop.

<Story of={EditorStories.CustomActionsAndCustomLabel} />

## With custom onCreate, onLinkExisting actions

In this case we override standard `onCreate` to create a custom entry, then tell the field editor about it using `onCreated` and then open it.

Same applies to `onLinkExisting` property that can be overriden to customize the linking process, at the end the `onLinkedExisting` callback should be called.

<Story of={EditorStories.CustomOnCreateOnLinkExistingActions} />

## With custom card relying on default card

<Story of={EditorStories.CustomCardRelyingOnDefaultCard} />

## Props

<Controls />
