import { Box } from '@contentful/f36-components'
import React from 'react'
import { GlobalNotiState } from '../../utils/notifications.interface'
import SummaryItem from '../../components/summary/SummaryItem'
import { AllowedNotificationType, CTATargets, NotificationDuration, NotificationType } from '../../utils/constants'

function GlobalNotificationStep2({ state }: { state: GlobalNotiState }) {

  const {
    url,
    title,
    thumbnail,
    description,
    externalLink,
    selectedPage,
    notificationDuration,
    notificationType,
    ctaTarget,
    ctaText,
    notificationCategory,
    isSystemNotification,
    badge,
    attentionRequired,
    renotify,
    groupingCategory,
    isSystemNotificationDismissible
  } = state

  const getNotificationCategoryNames = () => {
    return notificationCategory.map(cat => AllowedNotificationType.find(item => item.value === cat)?.title || '').join(', ')
  }

  return (
    <Box className="summaryRoot formRoot w-full ">
      <div className="flex flex-col gap-4 pt-4 w-full">
        <SummaryItem title={'Title'} value={title} type={'String'} />
        <SummaryItem title={'Description'} value={description} type={'String'} />
        <SummaryItem
          title={'URL'}
          value={url || externalLink || selectedPage?.fields?.slug?.['en-CA']}
          type={'String'} />
        <SummaryItem title={'Page Resource'} type={'Links'} links={selectedPage ? [selectedPage] : []} />
        <SummaryItem
          title={'Notification Type'}
          value={NotificationType.find(item => item.value === notificationType)?.title} type={'String'} />
        <SummaryItem title={'Notification Category'} value={getNotificationCategoryNames()} type={'String'} />
        <SummaryItem title={'CTA Text'} value={ctaText} type={'String'} />
        <SummaryItem
          title={'CTA Target'}
          value={CTATargets.find(item => item.value === ctaTarget)?.title}
          type={'String'} />
        <SummaryItem
          title={'Duration'}
          type={'String'}
          value={NotificationDuration.find(item => item.value === notificationDuration)?.title} />
        {/*<SummaryItem*/}
        {/*  title={'Is System Notification'}*/}
        {/*  value={isSystemNotification}*/}
        {/*  type={'Boolean'} isSmall={true} />*/}
        {/*{isSystemNotification && (*/}
        {/*  <SummaryItem*/}
        {/*    title={'Is System Notification Dismissible'}*/}
        {/*    value={isSystemNotificationDismissible}*/}
        {/*    type={'Boolean'} isSmall={true} />*/}
        {/*)}*/}
        <SummaryItem
          title={'Is Attention Required'}
          value={attentionRequired}
          type={'Boolean'} isSmall={true} />
        <SummaryItem
          title={'Renotify'}
          value={renotify}
          type={'Boolean'} isSmall={true} />
        <SummaryItem
          title={'Grouping Category'}
          value={AllowedNotificationType.find(item => item.value === groupingCategory)?.title}
          type={'String'} />
        <SummaryItem title={'Thumbnail'} type={'Asset'} thumbnail={thumbnail} />
        <SummaryItem title={'Badge'} type={'Asset'} thumbnail={badge} />
      </div>
    </Box>
  )
}

export default GlobalNotificationStep2

