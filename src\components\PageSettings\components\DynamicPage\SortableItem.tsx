import React from 'react'
import { useSortable } from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { BsGripVertical } from 'react-icons/bs'
import EntryPreviewById from '../../../EntryPreview/EntryPreviewById'

const SortableItem = ({ id }: { id: string }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id })
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    padding: 8,
    border: '1px solid #d9d9d9',
    marginBottom: 8,
    background: '#fff',
    borderRadius: 4,
    display: 'flex',
    alignItems: 'center',
    gap: 8,
  }

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <span
        {...listeners}
        style={{ cursor: 'grab', padding: 4, display: 'flex' }}
      >
        <BsGripVertical size={20} />
      </span>
      <EntryPreviewById entryId={id} />
    </div>
  )
}
export default SortableItem
