.dashboardItemContainer {
  height: 100%;
  overflow-y: auto;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 15px 0px 15px;
  flex-direction: column;
}

/* Webkit-based browsers (Chrome, Safari) */
.dashboardItemContainer::-webkit-scrollbar {
  width: 6px;
  /* Set the width of the scrollbar */
}

.dashboardItemContainer::-webkit-scrollbar-track {
  background: #f1f1f1;
  /* Track color */
}

.dashboardItemContainer::-webkit-scrollbar-thumb {
  background: #888;
  /* Thumb color */
  border-radius: 10px;
  /* Round the corners */
}

.dashboardItemContainer::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* Thumb color when hovered */
}

/* Firefox */
.dashboardItemContainer {
  scrollbar-width: thin;
  /* Set the width to thin */
  scrollbar-color: #888 #f1f1f1;
  /* Thumb and track color */
}

.row .css-s8xoeu {
  padding: 10px !important;
}

.emptyPreview {
  height: 400px;
  background: #888;
  text-align: center;

  p {
    margin-top: 200px;
  }
}

.verticalLine {
  position: absolute;
  border-left: 1px solid rgb(207, 217, 224);
  height: calc(100% - 40px);
  top: 50px;
  left: 45%;
}

.previewCol {
  width: 55%;
  margin-left: 5px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 45px;
}

.box {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 30px;
  background: #efefef;
  padding: 15px;
  border-radius: 5px;
}