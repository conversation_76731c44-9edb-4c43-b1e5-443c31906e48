import { Box } from '@contentful/f36-components'
import React from 'react'

import ComponentClonerwithButtonandTable from './ComponentClonerwithButtonandTable'
interface Props {
  // sdk: EditorAppSDK
  entryId: string
}

const ComponentCloner = (props: Props) => {
  const { entryId } = props

  return (
    <Box className='w-full'>
      <ComponentClonerwithButtonandTable entryId={entryId} />
    </Box>
  )
}

export default ComponentCloner
