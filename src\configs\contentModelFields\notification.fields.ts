export const notificationFields = {
  Small: [
    'backgroundColor',
    'categoriesPage',
    'cta',
    'description',
    'isGlobal',
    'isEnabled',
    'SpecificPage',
    'isPersistent',
  ],
  FormFloating: [
    'formFloating',
    'isGlobal',
    'isEnabled',
    'categoriesPage',
    'SpecificPage',
  ],
  Medium: [
    'heading',
    'backgroundColor',
    'categoriesPage',
    'cta',
    'description',
    'isGlobal',
    'isEnabled',
    'SpecificPage',
    'isPersistent',
  ],
  Carousels: ['carouselData',
    'isEnabled',
    'isGlobal',
    'isPersistent',
    'SpecificPage',
    'categoriesPage',
  ],
  ToastSpinner: ['toastMessage', 'isPersistent', 'autoHide'],
  ToastSuccess: ['toastMessage', 'isPersistent', 'autoHide'],
  ToastWarning: ['toastMessage', 'isPersistent', 'autoHide'],
  ToastError: ['toastMessage', 'isPersistent', 'autoHide'],
  ToastInfo: ['toastMessage', 'isPersistent', 'autoHide'],
  ExitIntentPopup: [
    'formFloating',
    'image',
    'isGlobal',
    'triggerEvent',
    'triggerDelay',
    'categoriesPage',
    'SpecificPage',
  ],
}
