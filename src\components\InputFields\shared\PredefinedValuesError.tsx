import * as React from 'react'

import { Note } from '@contentful/f36-note'

/**
 * Displays a warning message when the widget failed to initialize due to missing predefined values.
 *
 * The message will guide the user to provide the predefined values in the field settings.
 *
 * @returns A Note component with a warning message.
 */
export function PredefinedValuesError() {
  return (
    <Note variant='warning' testId='predefined-values-warning'>
      The widget failed to initialize. You can fix the problem by providing
      predefined values under the validations tab in the field settings.
    </Note>
  )
}
