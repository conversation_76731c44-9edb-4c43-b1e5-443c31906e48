import { DragHandle, Table, Text } from '@contentful/f36-components';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { css } from 'emotion';
import { getHelpIcon } from '../../utils';

export function DraggableTableRow({ id ,fieldData}:any) {
    const styles = {
      row: css({
        // This lets us change z-index when dragging
        position: 'relative',
      }),
    };
    const { active, attributes, listeners, setNodeRef, transform, transition } =
      useSortable({
        id,
      });
    const zIndex = active && active.id === id ? 1 : 0;
    const style = {
      zIndex,
      transform: CSS.Translate.toString(transform),
      transition,
    };
  
    return (
      <Table.Row className={styles.row} ref={setNodeRef} style={style}>
        <Table.Cell>
          <DragHandle
            label="Reorder item"
            variant="transparent"
            {...attributes}
            {...listeners}
          />
        </Table.Cell>
        <Table.Cell width="95%">
          <Text fontWeight="fontWeightMedium">{fieldData?.name}  <span style={{fontStyle:"italic"}}>{getHelpIcon(fieldData?.type)}</span></Text>
        </Table.Cell>
      </Table.Row>
    );
  }