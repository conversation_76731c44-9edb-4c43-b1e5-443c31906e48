import { Button, Tooltip } from '@contentful/f36-components'
import React from 'react'
import LeftArrow from '../../assets/icons/LeftArrow'
interface Props {
  onClick: () => void
  helpText?: string
  tooltipPlacement?: 'left' | 'right' | 'top' | 'bottom'
  type?: 'secondary' | 'positive' | 'primary'
  btnText?: string
  isDisabled?: boolean
  isLoading?: boolean
  isIcon?: boolean
}
function PrevButton(props: Props) {
  const {
    onClick,
    helpText,
    tooltipPlacement = 'left',
    type = 'secondary',
    btnText,
    isDisabled = false,
    isLoading = false,
    isIcon = true
  } = props

  return (
    <Tooltip placement={tooltipPlacement} content={helpText || 'Amend/Update'}>
      <Button
        variant={type}
        size='small'
        onClick={onClick}
        startIcon={
          isIcon ? <LeftArrow colour={type === 'secondary' ? '#000' : '#fff'} /> : undefined
        }
        isDisabled={isDisabled} // Spread the rest of the props to the Button component
        isLoading={isLoading}
      >
        {btnText}
      </Button>
    </Tooltip>
  )
}

export default PrevButton
