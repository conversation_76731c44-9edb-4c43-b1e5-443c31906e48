import {
  Box,
  FormControl,
  IconButton,
  Select,
  Switch,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import Info from '../../../../assets/icons/Info'
import { fetchGlobalConfigurationData } from '../../../../globals/utils'
import {
  createNewGlobalConfig,
  updateGlobalConfigData,
} from '../../../../redux/slices/dashboard/dvSlices'
import { RootState, useAppDispatch } from '../../../../redux/store'
import RevertButton from '../../../Buttons/RevertButton'
import SaveButton from '../../../Buttons/SaveButton'
import ColorPickerPopup from '../../../ConfigurationEngine/Components/ColorPickerPopup'

function DvStyles() {
  const dispatch = useAppDispatch()

  const [idForColorPicker, setIdForColorPicker] = useState('')

  const [showColorPicker, setShowColorPicker] = useState(false)

  const [data, setData] = useState<any>({})

  const [loading, setLoading] = useState(false)

  const [unChangedData, setUnChangedData] = useState<any>({})

  const [isRevertible, setIsRevertible] = useState(false)

  const dvGlobalData = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )

  const dvGlobalContentId = useSelector(
    (state: RootState) => state.dvDashboard.contentId
  )

  const handleStylesDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...data,
      styles: {
        ...data.styles,
        [key]: value,
      },
    }

    setData(dataToUpdate)
  }

  const handleSave = async () => {
    setLoading(true)
    const response: any = await fetchGlobalConfigurationData()
    let payload = {
      ...response?.data,
      DataVisualization: data,
    }

    if (dvGlobalContentId) {
      dispatch(
        updateGlobalConfigData({ contentId: dvGlobalContentId, payload })
      )
    } else {
      dispatch(createNewGlobalConfig(payload))
    }

    setLoading(false)
    setUnChangedData(data)
  }

  const revertChanges = () => {
    setData(unChangedData)
  }

  useEffect(() => {
    setData(dvGlobalData?.DataVisualization)
    setUnChangedData(dvGlobalData?.DataVisualization)
  }, [dvGlobalData, dvGlobalContentId])

  useEffect(() => {
    setIsRevertible(JSON.stringify(data) !== JSON.stringify(unChangedData))
  }, [data, unChangedData])

  return (
    <Box className='dashboardItemContainer'>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          width: '100%',
          height: '100%',
          flexDirection: 'column',
          gap: '10px',
        }}
      >
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            paddingTop: '0.5rem',
          }}
        >
          <FormControl
            id='original-domain'
            style={{
              paddingLeft: '0rem !import',
              width: '100%',
            }}
          >
            <div className='formLabelWithIcon'>
              <FormControl.Label>Show Grid</FormControl.Label>
              <Tooltip
                placement='top'
                id='tooltip-1'
                content='Toggle this option to display or hide the grid lines on the chart background.'
              >
                <Info />
              </Tooltip>
            </div>
            <Switch
              name='allow-cookies-uncontrolled'
              id='allow-cookies-uncontrolled'
              onChange={() =>
                handleStylesDataChange('showGrid', !data?.styles?.showGrid)
              }
              className='switchRoot'
              isChecked={data?.styles?.showGrid}
              isDisabled={loading}
            >
              {data?.styles?.showGrid ? 'Show ' : 'Hide '} Grid
            </Switch>
          </FormControl>
        </Box>
        {data?.styles?.showGrid && (
          <Box
            style={{
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              justifyContent: 'start',
              gap: '30px',
              paddingBlock: '10px',
            }}
          >
            <FormControl id='original-domain'>
              <div className='formLabelWithIcon'>
                <FormControl.Label>Chart Bg Color</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Select the background color for the entire chart area.'
                >
                  <Info />
                </Tooltip>
              </div>
              <div
                className={`bg edit`}
                onClick={() => {
                  setShowColorPicker(!showColorPicker)
                  setIdForColorPicker('graphBgColor')
                }}
              >
                {data?.styles?.graphBgColor ? (
                  <div
                    className='color'
                    style={{
                      backgroundColor: data?.styles?.graphBgColor,
                    }}
                  ></div>
                ) : (
                  <span>Pick</span>
                )}
              </div>
            </FormControl>
            <span
              style={{
                height: '80px',
                width: '1px',
                backgroundColor: 'rgba(0, 0, 0, 0.12)',
              }}
            ></span>

            <FormControl id='original-domain'>
              <div className='formLabelWithIcon'>
                <FormControl.Label>Grid Bg Color</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Choose the background color for the grid area within the chart.'
                >
                  <Info />
                </Tooltip>
              </div>

              <div
                className={`bg edit`}
                onClick={() => {
                  setShowColorPicker(!showColorPicker)
                  setIdForColorPicker('gridBgColor')
                }}
              >
                {data?.styles?.gridBgColor ? (
                  <div
                    className='color'
                    style={{
                      backgroundColor: data?.styles?.gridBgColor,
                    }}
                  ></div>
                ) : (
                  <span>Pick</span>
                )}
              </div>
            </FormControl>
            <span
              style={{
                height: '80px',
                width: '1px',
                backgroundColor: 'rgba(0, 0, 0, 0.12)',
              }}
            ></span>
            <FormControl id='original-domain'>
              <div className='formLabelWithIcon'>
                <FormControl.Label>Grid Line Color</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Set the color for the grid lines in the chart.'
                >
                  <Info />
                </Tooltip>
              </div>

              <div
                className={`bg edit`}
                onClick={() => {
                  setShowColorPicker(!showColorPicker)
                  setIdForColorPicker('gridLineColor')
                }}
              >
                {data?.styles?.gridLineColor ? (
                  <div
                    className='color'
                    style={{
                      backgroundColor: data?.styles?.gridLineColor,
                    }}
                  ></div>
                ) : (
                  <span>Pick</span>
                )}
              </div>
            </FormControl>
          </Box>
        )}

        {data?.styles?.showGrid && (
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              width: '100%',
            }}
          >
            {/* <span
            style={{
              height: '1px',
              width: '100%',
              backgroundColor: 'rgba(0, 0, 0, 0.12)',
            }}
          ></span> */}
            <Box
              style={{
                width: '100%',
                display: 'flex',
                justifyContent: 'start',
                alignItems: 'start',
                paddingTop: '0.3rem',
                gap: '1rem',
              }}
            >
              <FormControl
                id='original-domain'
                style={{
                  width: '50%',
                }}
              >
                <div className='formLabelWithIcon'>
                  <FormControl.Label>Grid Format</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id='tooltip-1'
                    content='Choose the format for the grid lines: horizontal, vertical, or both.'
                  >
                    <Info />
                  </Tooltip>
                </div>
                <Select
                  id='optionSelect-controlled'
                  name='optionSelect-controlled'
                  value={data?.styles?.gridFormat}
                  onChange={(e) =>
                    handleStylesDataChange('gridFormat', e.target.value)
                  }
                  placeholder='Select Title Alignment'
                  isDisabled={loading}
                >
                  <Select.Option value='split'>Split</Select.Option>
                  <Select.Option value='horizontal'>Horizontal</Select.Option>
                  <Select.Option value='vertical'>Vertical</Select.Option>
                </Select>
              </FormControl>
              <FormControl
                id='original-domain'
                style={{
                  width: '50%',
                }}
              >
                <div className='formLabelWithIcon'>
                  <FormControl.Label>Grid Line Style</FormControl.Label>
                  <Tooltip
                    placement='top'
                    id='tooltip-1'
                    content='Choose the type of grid lines for the chart: solid, dashed, or dotted.'
                  >
                    <Info />
                  </Tooltip>
                </div>
                <Select
                  id='optionSelect-controlled'
                  name='optionSelect-controlled'
                  value={data?.styles?.gridLineStyle}
                  onChange={(e) =>
                    handleStylesDataChange('gridLineStyle', e.target.value)
                  }
                  placeholder='Select Title Alignment'
                  isDisabled={loading}
                >
                  <Select.Option value='solid'>Solid</Select.Option>
                  <Select.Option value='dashed'>Dashed</Select.Option>
                  <Select.Option value='dotted'>Dotted</Select.Option>
                </Select>
              </FormControl>
            </Box>
          </div>
        )}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            width: '100%',
          }}
        >
          {/* <span
          style={{
            height: '1px',
            width: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.12)',
          }}
        ></span> */}
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
              paddingTop: '0.5rem',
              gap: '1rem',
            }}
          >
            <FormControl
              id='original-domain'
              style={{
                width: '50%',
              }}
            >
              <div className='formLabelWithIcon'>
                <FormControl.Label>Border Width</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Set the width of the border around the chart.'
                >
                  <Info />
                </Tooltip>
              </div>
              <TextInput.Group
                style={{
                  width: '100%',
                }}
              >
                <TextInput
                  aria-label='Content type name'
                  id='content-type-name'
                  placeholder='10'
                  type='number'
                  value={data?.styles?.borderWidth}
                  onChange={(e) =>
                    handleStylesDataChange('borderWidth', e.target.value)
                  }
                  isDisabled={loading}
                />
                <IconButton
                  variant='secondary'
                  aria-label='Unlock'
                  icon={<></>}
                  style={{
                    cursor: 'default',
                  }}
                >
                  px
                </IconButton>
              </TextInput.Group>
            </FormControl>
            <FormControl
              id='original-domain'
              style={{
                width: '50%',
              }}
            >
              <div className='formLabelWithIcon'>
                <FormControl.Label>Border Type</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Choose the type of border for the chart: solid, dashed, or dotted.'
                >
                  <Info />
                </Tooltip>
              </div>
              <Select
                id='optionSelect-controlled'
                name='optionSelect-controlled'
                value={data?.styles?.borderType}
                onChange={(e) =>
                  handleStylesDataChange('borderType', e.target.value)
                }
                placeholder='Select border type'
                style={{
                  width: '100%',
                }}
                isDisabled={loading}
              >
                <Select.Option value='solid'>Solid</Select.Option>
                <Select.Option value='dashed'>Dashed</Select.Option>
                <Select.Option value='dotted'>Dotted</Select.Option>
              </Select>
            </FormControl>
          </Box>
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
              paddingTop: '1rem',
              gap: '1rem',
            }}
          >
            <FormControl id='original-domain' style={{ width: '50%' }}>
              <div className='formLabelWithIcon'>
                <FormControl.Label>Border Shadow</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content='Enable or disable shadow for the chart border to create a depth effect.'
                >
                  <Info />
                </Tooltip>
              </div>
              <Switch
                name='allow-cookies-uncontrolled'
                id='allow-cookies-uncontrolled'
                onChange={() =>
                  handleStylesDataChange(
                    'isBorderShadow',
                    !data?.styles?.isBorderShadow
                  )
                }
                className='switchRoot'
                isChecked={data?.styles?.isBorderShadow}
                style={{
                  paddingTop: '0.5rem',
                }}
                isDisabled={loading}
              >
                {data?.styles?.isBorderShadow ? 'Add ' : 'Hide '}
                border shadow
              </Switch>
            </FormControl>
            <FormControl id='original-domain' style={{ width: '50%' }}>
              <div className='formLabelWithIcon'>
                <FormControl.Label>Border Color</FormControl.Label>
                <Tooltip
                  placement='top'
                  id='tooltip-1'
                  content={`Select the color for the chart's border.`}
                >
                  <Info />
                </Tooltip>
              </div>

              <div
                className={`bg edit`}
                onClick={() => {
                  setShowColorPicker(!showColorPicker)
                  setIdForColorPicker('borderColor')
                }}
              >
                {data?.styles?.borderColor ? (
                  <div
                    className='color'
                    style={{
                      backgroundColor: data?.styles?.borderColor,
                    }}
                  ></div>
                ) : (
                  <span>Pick</span>
                )}
              </div>
            </FormControl>
          </Box>
        </div>
      </Box>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          width: '100%',
        }}
      >
        <RevertButton
          isDisabled={!isRevertible || loading}
          onClick={revertChanges}
          tooltipPlacement='right'
        ></RevertButton>
        <SaveButton
          onClick={handleSave}
          isDisabled={!isRevertible}
          isLoading={loading}
        ></SaveButton>
      </Box>
      <ColorPickerPopup
        isOpen={showColorPicker}
        handleClose={() => {
          setShowColorPicker(false)
        }}
        selectedColors={data?.styles?.[idForColorPicker as any] || ''}
        onColorPick={(color: any) => {
          handleStylesDataChange(idForColorPicker, color.value)
          setShowColorPicker(false)
        }}
      />
    </Box>
  )
}

export default DvStyles
