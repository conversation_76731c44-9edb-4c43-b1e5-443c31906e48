import {
  Box,
  Button,
  Select,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TextInput,
} from '@contentful/f36-components'
import React, { useMemo, useState } from 'react'

interface Column {
  id: string
  label: string
  render?: (value: any, row: any) => JSX.Element
}

interface GlobalTableProps {
  data: any[]
  columns: Column[]
  pageSizeOptions?: number[]
  defaultPageSize?: number
  searchableColumns?: string[]
  height?: string
  extraConetntAfterSearch?: any
  paginationremove?: boolean
}

const GlobalTable: React.FC<GlobalTableProps> = ({
  data,
  columns,
  pageSizeOptions = [10, 20, 30, 40, 50],
  defaultPageSize = 10,
  searchableColumns = [],
  height = '320px',
  extraConetntAfterSearch = null,
  paginationremove = false,
}) => {
  const [filter, setFilter] = useState('')
  const [pageSize, setPageSize] = useState(defaultPageSize)
  const [currentPage, setCurrentPage] = useState(0)

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilter(e.target.value)
    setCurrentPage(0) // Reset to first page on filter change
  }

  const filteredData = useMemo(() => {
    if (!filter || !searchableColumns?.length) return data
    return data.filter((row) => {
      return searchableColumns.some((columnId) => {
        const value = row[columnId]
        return String(value).toLowerCase().includes(filter.toLowerCase())
      })
    })
  }, [data, searchableColumns, filter])

  const paginatedData = useMemo(() => {
    if (paginationremove) {
      return filteredData
    } else {
      const start = currentPage * pageSize
      const end = start + pageSize
      return filteredData.slice(start, end)
    }
  }, [filteredData, currentPage, pageSize])

  const pageCount = Math.ceil(filteredData.length / pageSize)

  return (
    <Box
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      {searchableColumns?.length || extraConetntAfterSearch ? (
        <div className='w-full flex gap-3'>
          {searchableColumns?.length ? (
            <div
              className='w-1/3 flex justify-between my-5'
              // marginBottom='spacingS'
              // style={{ width: '30%' }}
              // display='flex'
              // justifyContent='space-between'
            >
              <TextInput placeholder='Search' onChange={handleFilterChange} />
            </div>
          ) : null}
          {extraConetntAfterSearch}
        </div>
      ) : null}
      <Box className='overflow-auto h-auto' style={{ minHeight: height }}>
        <Table
          style={{
            width: '100%',
            // minHeight: height,
            height: '100%',
            // overflow: 'scroll',
          }}
        >
          <TableHead isSticky>
            <TableRow>
              {columns.map((column) => (
                <TableCell key={column.id}>{column.label}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          {paginatedData?.length > 0 ? (
            <TableBody
              style={{
                width: '100%',
                // minHeight: height,
                // height: height,
                maxHeight: height,
                overflow: paginatedData?.length > 0 ? 'scroll' : 'hidden',
              }}
            >
              {paginatedData.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columns.map((column) => (
                    <TableCell key={column.id}>
                      {column.render
                        ? column.render(row[column.id], row)
                        : row[column.id]}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          ) : (
            <TableRow>
              <TableCell colSpan={columns?.length}>
                <div
                  style={{
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  No Data Available
                </div>
              </TableCell>
            </TableRow>
          )}
        </Table>
      </Box>
      {!paginationremove && (
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            marginBottom: '10px',
          }}
          marginTop='spacingS'
        >
          <Button
            onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 0))}
            isDisabled={currentPage === 0}
          >
            Previous
          </Button>
          <Select
            value={pageSize}
            onChange={(e) => {
              setPageSize(Number(e.target.value))
              setCurrentPage(0) // Reset to first page on page size change
            }}
          >
            {pageSizeOptions.map((size) => (
              <option key={size} value={size}>
                Show {size}
              </option>
            ))}
          </Select>
          <span>
            Page {currentPage + 1} of {pageCount}
          </span>
          <Button
            onClick={() =>
              setCurrentPage((prev) => Math.min(prev + 1, pageCount - 1))
            }
            isDisabled={currentPage === pageCount - 1}
          >
            Next
          </Button>
        </Box>
      )}
    </Box>
  )
}

export default GlobalTable

// import { Box, Button, Select, Table, TableBody, TableCell, TableHead, TableRow, TextInput } from '@contentful/f36-components';
// import React, { useMemo, useState } from 'react';

// interface Column {
//   id: string;
//   label: string;
//   render?: (value: any, row: any) => JSX.Element;
// }

// interface GlobalTableProps {
//   data: any[];
//   columns: Column[];
//   pageSizeOptions?: number[];
//   defaultPageSize?: number;
//   searchableColumns?: string[];
// }

// const GlobalTable: React.FC<GlobalTableProps> = ({
//   data,
//   columns,
//   pageSizeOptions = [10, 20, 30, 40, 50],
//   defaultPageSize = 10,
//   searchableColumns = [],
// }) => {
//   const [filter, setFilter] = useState('');
//   const [pageSize, setPageSize] = useState(defaultPageSize);
//   const [currentPage, setCurrentPage] = useState(0);

//   const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setFilter(e.target.value);
//     setCurrentPage(0); // Reset to first page on filter change
//   };

//   const filteredData = useMemo(() => {
//     if (!filter || !searchableColumns?.length) return data;
//     return data.filter(row => {
//       return searchableColumns.some(columnId => {
//         const value = row[columnId];
//         return String(value).toLowerCase().includes(filter.toLowerCase());
//       });
//     });
//   }, [data, searchableColumns, filter]);

//   const paginatedData = useMemo(() => {
//     const start = currentPage * pageSize;
//     const end = start + pageSize;
//     return filteredData.slice(start, end);
//   }, [filteredData, currentPage, pageSize]);

//   const pageCount = Math.ceil(filteredData.length / pageSize);

//   return (
//     <Box style={{ width: '100%', height: '100%'  }}>
//       {searchableColumns?.length ? (
//         <Box marginBottom="spacingS" style={{ width: '30%' }} display="flex" justifyContent="space-between">
//           <TextInput placeholder="Search" onChange={handleFilterChange} />
//         </Box>
//       ) : (
//         ""
//       )}
//       <Box style={{ flexGrow: 1, overflow: 'auto' }}>
//         <Table style={{ width: '100%', minHeight: '70%',maxHeight: '70%' }}>
//           <TableHead>
//             <TableRow>
//               {columns.map(column => (
//                 <TableCell key={column.id}>{column.label}</TableCell>
//               ))}
//             </TableRow>
//           </TableHead>
//           <TableBody>
//             {paginatedData.map((row, rowIndex) => (
//               <TableRow key={rowIndex}>
//                 {columns.map(column => (
//                   <TableCell key={column.id}>
//                     {column.render ? column.render(row[column.id], row) : row[column.id]}
//                   </TableCell>
//                 ))}
//               </TableRow>
//             ))}
//           </TableBody>
//         </Table>
//       </Box>
//       <Box style={{ width: '100%', display:"flex", justifyContent:"center", alignItems:"center", gap:"10px" }} marginTop="spacingS" display="flex" justifyContent="space-between" alignItems="center">
//         <Button onClick={() => setCurrentPage(prev => Math.max(prev - 1, 0))} isDisabled={currentPage === 0}>
//           Previous
//         </Button>
//         <Select
//           value={pageSize}
//           onChange={(e) => {
//             setPageSize(Number(e.target.value));
//             setCurrentPage(0); // Reset to first page on page size change
//           }}
//         >
//           {pageSizeOptions.map((size) => (
//             <option key={size} value={size}>
//               Show {size}
//             </option>
//           ))}
//         </Select>
//         <span>
//           Page {currentPage + 1} of {pageCount}
//         </span>
//         <Button onClick={() => setCurrentPage(prev => Math.min(prev + 1, pageCount - 1))} isDisabled={currentPage === pageCount - 1}>
//           Next
//         </Button>
//       </Box>
//     </Box>
//   );
// };

// export default GlobalTable;
