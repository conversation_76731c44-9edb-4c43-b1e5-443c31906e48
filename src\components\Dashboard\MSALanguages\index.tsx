import {
  Box,
  Button,
  Checkbox,
  FormControl,
  Notification,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { ENV_VARIABLES } from '../../../constant/variables'
import {
  CreateConfigurationEntry,
  SCOPE,
  UpdateConfigurationEntry,
} from '../../../globals/config-helpers'
import { getConfigurationsCollectionQuery } from '../../../globals/queries'
import { fetchGraphQL, Languages } from '../../../globals/utils'

interface MSALanguagesProps {
  selectedDomain: string
}

function MSALanguages({ selectedDomain }: MSALanguagesProps) {
  const [selectedLocale, setSelectedLocale] = useState<string[]>(['en-CA'])

  const [loading, setLoading] = useState(false)

  const [saveLoading, setSaveLoading] = useState(false)

  const [contentId, setContentId] = useState('')

  const [msaConfigData, setMsaConfigData] = useState<any>({})

  const envId = ENV_VARIABLES.contentfulEnvironment

  const spaceId = ENV_VARIABLES.contentfulSpaceID

  /**
   * Handles the onclick event for the MSA languages checkboxes. If the checkbox is currently selected,
   * it removes it from the selected locale array. If not, it adds it.
   * @param {string} x - The locale value of the checkbox.
   */
  const handleOnclick = (x: string) => {
    let oldLocale = [...selectedLocale]

    if (oldLocale.includes(x))
      oldLocale = oldLocale.filter((locale) => locale !== x)
    else oldLocale = [...oldLocale, x]

    setSelectedLocale(oldLocale)
  }

  /**
   * Saves the selected MSA languages to the configuration entry.
   * If the configuration entry doesn't exist, it creates a new one.
   * If the configuration entry exists, it updates the existing one.
   * @param {string[]} langs - The array of selected locales.
   */
  const handleSaveMsaLanguages = async (langs: string[]) => {
    setSaveLoading(true)
    if (!contentId) {
      const payload = {
        allowedLanguages: langs,
      }

      const res = await CreateConfigurationEntry({
        data: payload,
        type: 'Locale',
        scope: selectedDomain.toUpperCase() as SCOPE,
        internalName: `Locale - ${selectedDomain.toUpperCase()}`,
        envId,
        spaceId,
      })

      if (res) {
        setContentId(res)
        Notification.success('Configuration saved successfully')
      } else {
        Notification.error('Failed to save configuration')
      }
    } else {
      const payload = {
        ...msaConfigData,
        allowedLanguages: langs,
      }

      const res = await UpdateConfigurationEntry({
        contentId,
        data: payload,
        envId,
        spaceId,
      })
      if (res) {
        Notification.success('Configuration updated successfully')
        setMsaConfigData(payload)
      } else {
        Notification.error('Failed to update configuration')
        setSelectedLocale(msaConfigData?.allowedLanguages || ['en-CA'])
      }
    }

    setSaveLoading(false)
  }

  /**
   * Fetches the MSA languages configuration data from the Contentful API
   * and updates the state with the fetched data.
   * @returns {Promise<void>}
   */
  const fetchData = async () => {
    setLoading(true)
    const res = await fetchGraphQL(getConfigurationsCollectionQuery()).then(
      (res: any) => res?.data?.configurationsCollection?.items
    )

    const matchedData = res?.find((item: any) => {
      return (
        item?.scope === selectedDomain?.toUpperCase() && item?.type === 'Locale'
      )
    })?.data?.json?.content?.[0]?.content?.[0]?.value

    if (!matchedData) return setLoading(false)

    const data = JSON.parse(matchedData)
    setMsaConfigData(data)

    setSelectedLocale(data?.allowedLanguages || ['en-CA'])
    setLoading(false)
  }
  // fetch data
  useEffect(() => {
    fetchData()
  }, [])

  if (loading) {
    return (
      <Box className='flex justify-center items-center w-full h-full'>
        Loading.....
      </Box>
    )
  }

  return (
    <Box className='p-5 flex flex-col items-start w-full justify-start gap-5'>
      <Box className='w-[400px]'>
        <FormControl>
          <FormControl.Label className='py-0'>
            Allowed Languages
          </FormControl.Label>
          <p style={{ color: 'black', marginTop: 10 }}>
            These languages will be available for the translations
          </p>
          <div
            style={{ display: 'flex', flexDirection: 'column', marginTop: 10 }}
          >
            {Languages.map((locale) => (
              <Checkbox
                isChecked={selectedLocale?.includes(locale?.value)}
                onChange={() => handleOnclick(locale?.value)}
                className='py-2'
                key={locale.value}
              >
                {locale?.title}
              </Checkbox>
            ))}
          </div>
        </FormControl>
      </Box>
      <Button
        variant='positive'
        isLoading={saveLoading}
        isDisabled={saveLoading || selectedLocale?.length === 0}
        onClick={() => handleSaveMsaLanguages(selectedLocale)}
      >
        Save
      </Button>
    </Box>
  )
}

export default MSALanguages
