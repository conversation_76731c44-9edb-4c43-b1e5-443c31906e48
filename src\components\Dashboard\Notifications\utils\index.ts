import * as contentful from 'contentful-management'
import { SPACE_ID, TOKEN } from '../../../../constant/variables'
import { MSA_URLS, PROD_URLS } from './constants'
import { generateRandomId, sendMessage } from '../../../../globals/firebase/utils'
import {
  createContentEntry,
  createOrUpdateAsset,
  getAssetData,
  getDomainDataByDomainName,
  SaveConfigurationandData,
} from '../../../../globals/utils'
import { DomainNotiState } from './notifications.interface'
import { HomeAppSDK } from '@contentful/app-sdk'

export const fetchEntryDetails = async (entryId: string, extension: any) => {
  if (extension) {
    const cma = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN, // Use the access token from the extension parameters
    })

    try {
      const space = await cma.getSpace(extension.ids.space)
      const environment = await space.getEnvironment(extension.ids.environment)
      const entry: any = await environment.getEntry(entryId)

      return entry
    } catch (error) {
      console.error('Error fetching entry details:', error)
    }
  }
}

export function formatIsoDate(isoDate: string): string {
  const date = new Date(isoDate)

  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

export const PreviewBranchUrl = (domainCode: string) => {
  const previewBranch = process.env.REACT_APP_BRANCH_FRONT || 'dev'

  const isMaster = previewBranch === 'main' || previewBranch === 'master'

  if (isMaster) {
    return PROD_URLS[domainCode as keyof typeof PROD_URLS]
  } else {
    return MSA_URLS[domainCode as keyof typeof MSA_URLS].replace(
      'main',
      previewBranch,
    )
  }
}

export const CreateRealTimeNotification = async ({ state, domain }: { state: DomainNotiState, domain: string }) => {
  const {
    title,
    selectedPage,
    description,
    thumbnail,
    url,
    externalLink,
    notificationType,
    notificationDuration,
    ctaText,
    ctaTarget,
    notificationCategory,
    isSystemNotification,
    attentionRequired,
    badge,
    groupingCategory,
    renotify,
    isSystemNotificationDismissible
  } = state

  let payload: any = {
    title: title || selectedPage?.fields?.seoTitle?.['en-CA'],
    body: description || selectedPage?.fields?.seoDescription?.['en-CA'],
    icon: thumbnail?.url,
    url: url || externalLink || selectedPage?.fields?.slug?.['en-CA'],
    id: generateRandomId(),
    domain: getDomainDataByDomainName(domain)?.domainKey,
    type: notificationType,
    duration: notificationDuration,
    timeStamp: new Date(),
    ctaText,
    ctaTarget,
    category: JSON.stringify(notificationCategory),
    isSystemNotification: isSystemNotification.toString(),
    attentionRequired: attentionRequired.toString(),
    badge: badge?.url,
    tag:groupingCategory,
    renotify: renotify.toString(),
    isDismissable: isSystemNotificationDismissible.toString()
  }

  payload.url = nonHttpsHandler({ url: payload.url, domain })

  const res = await sendMessage(
    payload,
    getDomainDataByDomainName(domain)?.domainKey,
  )

  return res ? 'success' : 'error'
}

export const nonHttpsHandler = ({ url, domain }: { url: string, domain: string }) => {
  if (!url) return ''
  return url.includes('http://') || url.includes('https://') ? checkIsLastCharSlash(url) : urlEmptySlashHandler({ url, domain })
}

export const urlEmptySlashHandler = ({ url, domain }: { url: string, domain: string }) => {
  if (!url) return ''
  return checkIsLastCharSlash(url === '/' ? PreviewBranchUrl(domain) : PreviewBranchUrl(domain) + url + '/')
}

export const checkIsLastCharSlash = (url: string): string => {
  if (!url) return '';

  // Split the URL into [mainUrl, query/hash part]
  const [mainUrlWithPath, ...rest] = url.split(/(?=[?#])/); // splits at first ? or #, keeping the delimiter

  const finalUrl = mainUrlWithPath.endsWith('/') ? mainUrlWithPath : `${mainUrlWithPath}/`;

  // Re-attach the rest (query and/or hash)
  return finalUrl + rest.join('');
};


export const CreateStickyNotification = async ({ state, domain }: { state: DomainNotiState, domain: string }) => {

  const {
    internalName,
    title,
    description,
    bgColor,
    stickyNotificationTemplate,
    isGloballyEnabled,
    isClosable,
    isEnabled,
    selectedCTA,
    specificPages,
    formFloatingPage,
    categoriesPages,
  } = state

  let payload: any = {
    internalName: {
      'en-CA': internalName || '',
    },
    heading: {
      'en-CA': title || '',
    },
    description: {
      'en-CA': description || '',
    },
    backgroundColor: {
      'en-CA': bgColor || '',
    },
    template: {
      'en-CA': stickyNotificationTemplate || 'Small',
    },
    isGlobal: {
      'en-CA': isGloballyEnabled,
    },
    isEnabled: {
      'en-CA': isEnabled,
    },
    isPersistent: {
      'en-CA': isClosable,
    },
  }

  if (selectedCTA) {
    payload = {
      ...payload,
      cta: {
        'en-CA': {
          sys: {
            type: 'Link',
            linkType: 'Entry',
            id: selectedCTA?.sys?.id,
          },
        },
      },
    }
  }

  if (specificPages?.length > 0) {
    payload = {
      ...payload,
      SpecificPage: {
        'en-CA': specificPages?.map((categoryPage: any) => {
          return {
            sys: {
              type: 'Link',
              linkType: 'Entry',
              id: categoryPage?.sys?.id,
            },
          }
        }),
      },
    }
  }

  if (formFloatingPage) {
    payload = {
      ...payload,
      formFloating: {
        'en-CA': {
          sys: {
            type: 'Link',
            linkType: 'Entry',
            id: formFloatingPage?.sys?.id,
          },
        },
      },
    }
  }

  if (categoriesPages?.length > 0) {
    payload = {
      ...payload,
      categoriesPage: {
        'en-CA': categoriesPages?.map((categoryPage: any) => {
          return {
            sys: {
              type: 'Link',
              linkType: 'Entry',
              id: categoryPage?.sys?.id,
            },
          }
        }),
      },
    }
  }

  const tags = [
    {
      sys: {
        type: 'Link',
        linkType: 'Tag',
        id: getDomainDataByDomainName(domain as any)?.domainKey,
      },
    },
  ]
  const res = await createContentEntry('componentNotification', payload, tags)

  const contentId = res?.sys?.id

  return { status: res ? 'success' : 'error', id: contentId }
}

export const SingleEntrySelector = async (contentTypes: string[], sdk: HomeAppSDK) => {
  return (await sdk.dialogs.selectSingleEntry({
    contentTypes: contentTypes,
  })) as any
}

export const MultiEntrySelector = async (contentTypes: string[], sdk: HomeAppSDK) => {
  return (await sdk.dialogs.selectMultipleEntries({
    contentTypes: contentTypes,
  })) as any
}

export const entryPageSelector = async (sdk: HomeAppSDK) => {

  const contentTypes = ['page']

  const selected = await SingleEntrySelector(contentTypes, sdk)

  if (!selected) {
    return { dataFetching: false }
  }

  const title = selected?.fields?.seoTitle?.['en-CA']

  const description = selected?.fields?.seoDescription?.['en-CA']

  const internalName = selected?.fields?.internalName?.['en-CA']

  const pageThumbnail: any = await getAssetData(
    selected?.fields?.pageThumbnail?.['en-CA']?.sys?.id,
  ).then((res) => res)

  const thumbnail = {
    url: pageThumbnail?.fields?.file?.['en-CA'].url,
    status: pageThumbnail?.sys?.fieldStatus?.['*']?.['en-CA'],
  }

  return {
    title,
    internalName,
    description,
    thumbnail,
    selectedPage: selected,
    dataFetching: false,
  }
}

export const selectSingleAsset = async (sdk: HomeAppSDK, type: 'thumbnail' | 'badge') => {

  const asset = (await sdk.dialogs.selectSingleAsset({ mimetypeGroups: ['image'] })) as any

  if (!asset) {

    return {
      dataFetching: false,
    }
  }
  return {
    [type]: {
      url: asset?.fields?.file?.['en-CA'].url,
      status: asset?.sys?.fieldStatus?.['*']?.['en-CA'],
    }, dataFetching: false,
  }
}

export const createSingleAsset = async (event: any, sdk: HomeAppSDK, type: 'thumbnail' | 'badge') => {

  const file = event?.target?.files?.[0]

  if (!file) {
    return { dataFetching: false }
  }

  //Create or update asset
  const asset: any = await createOrUpdateAsset(file, 'en-CA')

  return {
    dataFetching: false,
    [type]: {
      url: asset?.fields?.file?.['en-CA']?.url,
      status: asset?.sys?.fieldStatus?.['*']?.['en-CA'],
    },
  }
}

export const SaveNotificationAsHistory = async ({ data, domain, id, dataSource, type }: {
  data: any,
  id?: string,
  domain: string,
  dataSource: any,
  type: 'realTime' | 'sticky'
}) => {
  if (!id) {
    const payload = {
      [domain]: {
        [type]: [{ ...data }],
      },
    }
    const fields = {
      internalName: {
        'en-CA': 'MSA - Notifications History',
      },
      scope: {
        'en-CA': 'MSA',
      },
      type: {
        'en-CA': 'Notifications',
      },
      data: {
        'en-CA': {
          content: [
            {
              content: [
                {
                  data: {},
                  marks: [],
                  nodeType: 'text',
                  value: JSON.stringify(payload),
                },
              ],
              data: {},
              nodeType: 'paragraph',
            },
          ],
          data: {},
          nodeType: 'document',
        },
      },
    }

    await createContentEntry('configurations', fields)
  } else {
    const payload = {
      ...dataSource,
      [domain]: {
        ...dataSource?.[domain],
        [type]: [
          ...(dataSource?.[domain]?.[type] || []), // Ensure it's an empty array if undefined
          data,
        ],
      },
    }

    await SaveConfigurationandData(payload, id)
  }
}