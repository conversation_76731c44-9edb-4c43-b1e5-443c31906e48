import { HomeAppSDK } from '@contentful/app-sdk'
import * as contentful from 'contentful-management'
import { SPACE_ID, TOKEN } from '../../../../constant/variables'
import { generateRandomId, sendMessage } from '../../../../globals/firebase/utils'
import {
  createContentEntry,
  createOrUpdateAsset,
  getAssetData,
  getDomainDataByDomainName,
  SaveConfigurationandData,
} from '../../../../globals/utils'
import { MSA_URLS, PROD_URLS } from './constants'
import { DomainNotiState } from './notifications.interface'

/**
 * Fetches an entry from the Contentful space specified in the extension's
 * parameters using the Contentful Management API.
 *
 * @param {string} entryId The ID of the entry to fetch
 * @param {any} extension The extension object that contains the space ID and
 * access token in its `ids` property
 * @returns {Promise<any>} A Promise that resolves with the entry object, or
 * `undefined` if the extension object is not provided or if there is an error
 * fetching the entry
 */
export const fetchEntryDetails = async (entryId: string, extension: any) => {
  if (extension) {
    const cma = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN, // Use the access token from the extension parameters
    })

    try {
      const space = await cma.getSpace(extension.ids.space)
      const environment = await space.getEnvironment(extension.ids.environment)
      const entry: any = await environment.getEntry(entryId)

      return entry
    } catch (error) {
      console.error('Error fetching entry details:', error)
    }
  }
}

/**
 * Converts an ISO date string into a formatted date-time string.
 *
 * @param {string} isoDate - The ISO date string to format.
 * @returns {string} A string representing the date and time in the format 'YYYY-MM-DD HH:MM:SS'.
 */

export function formatIsoDate(isoDate: string): string {
  const date = new Date(isoDate)

  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

/**
 * Returns the URL for a given domain code based on the current preview branch.
 * If the preview branch is 'main' or 'master', it returns the production URL.
 * Otherwise, it returns a modified MSA URL with the current preview branch.
 *
 * @param {string} domainCode - The code of the domain to get the URL for.
 * @returns {string} The URL corresponding to the domain code and preview branch.
 */

export const PreviewBranchUrl = (domainCode: string) => {
  const previewBranch = process.env.REACT_APP_BRANCH_FRONT || 'dev'

  const isMaster = previewBranch === 'main' || previewBranch === 'master'

  if (isMaster) {
    return PROD_URLS[domainCode as keyof typeof PROD_URLS]
  } else {
    return MSA_URLS[domainCode as keyof typeof MSA_URLS].replace(
      'main',
      previewBranch,
    )
  }
}

/**
 * Creates a real-time notification using the provided state and domain.
 * Constructs a payload based on the state details, including title, body, 
 * icon, URL, and other notification properties. Sends the notification 
 * payload using the `sendMessage` function.
 * 
 * The function handles URL construction and applies a non-HTTPS handler 
 * if necessary. Returns a string indicating the success or failure of 
 * the notification creation process.
 *
 * @param {Object} params - The parameters for creating the notification.
 * @param {DomainNotiState} params.state - The current notification state 
 * containing details such as title, description, and other notification 
 * properties.
 * @param {string} params.domain - The domain for which the notification 
 * is being created, used to fetch the domain key.
 *
 * @returns {Promise<string>} A promise that resolves to 'success' if the 
 * notification was created successfully, or 'error' if there was a failure.
 */

export const CreateRealTimeNotification = async ({ state, domain }: { state: DomainNotiState, domain: string }) => {
  const {
    title,
    selectedPage,
    description,
    thumbnail,
    url,
    externalLink,
    notificationType,
    notificationDuration,
    ctaText,
    ctaTarget,
    notificationCategory,
    isSystemNotification,
    attentionRequired,
    badge,
    groupingCategory,
    renotify,
    isSystemNotificationDismissible
  } = state

  let payload: any = {
    title: title || selectedPage?.fields?.seoTitle?.['en-CA'],
    body: description || selectedPage?.fields?.seoDescription?.['en-CA'],
    icon: thumbnail?.url,
    url: url || externalLink || selectedPage?.fields?.slug?.['en-CA'],
    id: generateRandomId(),
    domain: getDomainDataByDomainName(domain)?.domainKey,
    type: notificationType,
    duration: notificationDuration,
    timeStamp: new Date(),
    ctaText,
    ctaTarget,
    category: JSON.stringify(notificationCategory),
    isSystemNotification: isSystemNotification.toString(),
    attentionRequired: attentionRequired.toString(),
    badge: badge?.url,
    tag:groupingCategory,
    renotify: renotify.toString(),
    isDismissable: isSystemNotificationDismissible.toString()
  }

  payload.url = nonHttpsHandler({ url: payload.url, domain })

  const res = await sendMessage(
    payload,
    getDomainDataByDomainName(domain)?.domainKey,
  )

  return res ? 'success' : 'error'
}

/**
 * Handles URLs by ensuring they are properly formatted with or without HTTPS.
 * If the URL is empty, it returns an empty string.
 * If the URL contains 'http://' or 'https://', it checks if the last character is a slash.
 * Otherwise, it appends the URL to the preview branch URL for the given domain.
 * 
 * @param {Object} param - The parameter object.
 * @param {string} param.url - The URL to handle.
 * @param {string} param.domain - The domain to use for constructing the URL if needed.
 * @returns {string} - The processed URL.
 */

/**
 * Ensures the provided URL is properly formatted with or without HTTPS.
 * - If the URL is empty, returns an empty string.
 * - If the URL starts with 'http://' or 'https://', it checks if the last character is a slash.
 * - Otherwise, it appends the URL to the preview branch URL for the given domain.
 *
 * @param {Object} param - The parameter object containing URL and domain.
 * @param {string} param.url - The URL to handle.
 * @param {string} param.domain - The domain to use for constructing the URL if needed.
 * @returns {string} - The processed URL, ensuring it ends with a slash if necessary.
 */

export const nonHttpsHandler = ({ url, domain }: { url: string, domain: string }) => {
  if (!url) return ''
  return url.includes('http://') || url.includes('https://') ? checkIsLastCharSlash(url) : urlEmptySlashHandler({ url, domain })
}

/**
 * Handles the case where the provided URL is empty.
 * If the URL is empty, returns an empty string.
 * If the URL is not empty, it appends the URL to the preview branch URL for the given domain.
 * Ensures the resulting URL ends with a slash if necessary.
 *
 * @param {Object} param - The parameter object containing URL and domain.
 * @param {string} param.url - The URL to handle.
 * @param {string} param.domain - The domain to use for constructing the URL if needed.
 * @returns {string} - The processed URL, ensuring it ends with a slash if necessary.
 */
export const urlEmptySlashHandler = ({ url, domain }: { url: string, domain: string }) => {
  if (!url) return ''
  return checkIsLastCharSlash(url === '/' ? PreviewBranchUrl(domain) : PreviewBranchUrl(domain) + url + '/')
}

/**
 * Ensures the provided URL ends with a slash if necessary.
 * - If the URL is empty, returns an empty string.
 * - It splits the URL into the main URL and query/hash parts.
 * - If the main URL does not end with a slash, it appends one.
 * - Finally, it re-attaches the query/hash parts to the final URL.
 *
 * @param {string} url - The URL to handle.
 * @returns {string} - The processed URL, ensuring it ends with a slash if necessary.
 */
export const checkIsLastCharSlash = (url: string): string => {
  if (!url) return '';

  // Split the URL into [mainUrl, query/hash part]
  const [mainUrlWithPath, ...rest] = url.split(/(?=[?#])/); // splits at first ? or #, keeping the delimiter

  const finalUrl = mainUrlWithPath.endsWith('/') ? mainUrlWithPath : `${mainUrlWithPath}/`;

  // Re-attach the rest (query and/or hash)
  return finalUrl + rest.join('');
};


/**
 * Creates a sticky notification content entry.
 *
 * @param {Object} param - The parameter object containing state and domain.
 * @param {DomainNotiState} param.state - The state of the notification to create.
 * @param {string} param.domain - The domain to associate the notification with.
 * @returns {Promise<Object>} - The created content entry with a status of 'success' or 'error'.
 * The returned object contains the content entry id and the status of the creation.
 */
export const CreateStickyNotification = async ({ state, domain }: { state: DomainNotiState, domain: string }) => {

  const {
    internalName,
    title,
    description,
    bgColor,
    stickyNotificationTemplate,
    isGloballyEnabled,
    isClosable,
    isEnabled,
    selectedCTA,
    specificPages,
    formFloatingPage,
    categoriesPages,
  } = state

  let payload: any = {
    internalName: {
      'en-CA': internalName || '',
    },
    heading: {
      'en-CA': title || '',
    },
    description: {
      'en-CA': description || '',
    },
    backgroundColor: {
      'en-CA': bgColor || '',
    },
    template: {
      'en-CA': stickyNotificationTemplate || 'Small',
    },
    isGlobal: {
      'en-CA': isGloballyEnabled,
    },
    isEnabled: {
      'en-CA': isEnabled,
    },
    isPersistent: {
      'en-CA': isClosable,
    },
  }

  if (selectedCTA) {
    payload = {
      ...payload,
      cta: {
        'en-CA': {
          sys: {
            type: 'Link',
            linkType: 'Entry',
            id: selectedCTA?.sys?.id,
          },
        },
      },
    }
  }

  if (specificPages?.length > 0) {
    payload = {
      ...payload,
      SpecificPage: {
        'en-CA': specificPages?.map((categoryPage: any) => {
          return {
            sys: {
              type: 'Link',
              linkType: 'Entry',
              id: categoryPage?.sys?.id,
            },
          }
        }),
      },
    }
  }

  if (formFloatingPage) {
    payload = {
      ...payload,
      formFloating: {
        'en-CA': {
          sys: {
            type: 'Link',
            linkType: 'Entry',
            id: formFloatingPage?.sys?.id,
          },
        },
      },
    }
  }

  if (categoriesPages?.length > 0) {
    payload = {
      ...payload,
      categoriesPage: {
        'en-CA': categoriesPages?.map((categoryPage: any) => {
          return {
            sys: {
              type: 'Link',
              linkType: 'Entry',
              id: categoryPage?.sys?.id,
            },
          }
        }),
      },
    }
  }

  const tags = [
    {
      sys: {
        type: 'Link',
        linkType: 'Tag',
        id: getDomainDataByDomainName(domain as any)?.domainKey,
      },
    },
  ]
  const res = await createContentEntry('componentNotification', payload, tags)

  const contentId = res?.sys?.id

  return { status: res ? 'success' : 'error', id: contentId }
}

/**
 * Opens a dialog for selecting a single Contentful entry of the given content types and returns the selected entry.
 * @param contentTypes - An array of content types to select from.
 * @param sdk - The HomeAppSDK instance.
 * @returns The selected entry.
 */
export const SingleEntrySelector = async (contentTypes: string[], sdk: HomeAppSDK) => {
  return (await sdk.dialogs.selectSingleEntry({
    contentTypes: contentTypes,
  })) as any
}

/**
 * Opens a dialog for selecting multiple Contentful entries of the given content types and returns the selected entries.
 * @param contentTypes - An array of content types to select from.
 * @param sdk - The HomeAppSDK instance.
 * @returns The selected entries.
 */
export const MultiEntrySelector = async (contentTypes: string[], sdk: HomeAppSDK) => {
  return (await sdk.dialogs.selectMultipleEntries({
    contentTypes: contentTypes,
  })) as any
}

  /**
   * Opens a dialog for selecting a single Contentful page entry and returns the selected entry data.
   * @param sdk - The HomeAppSDK instance.
   * @returns The selected entry data, including title, description, internal name, thumbnail and entry ID.
   */
export const entryPageSelector = async (sdk: HomeAppSDK) => {

  const contentTypes = ['page']

  const selected = await SingleEntrySelector(contentTypes, sdk)

  if (!selected) {
    return { dataFetching: false }
  }

  const title = selected?.fields?.seoTitle?.['en-CA']

  const description = selected?.fields?.seoDescription?.['en-CA']

  const internalName = selected?.fields?.internalName?.['en-CA']

  const pageThumbnail: any = await getAssetData(
    selected?.fields?.pageThumbnail?.['en-CA']?.sys?.id,
  ).then((res) => res)

  const thumbnail = {
    url: pageThumbnail?.fields?.file?.['en-CA'].url,
    status: pageThumbnail?.sys?.fieldStatus?.['*']?.['en-CA'],
  }

  return {
    title,
    internalName,
    description,
    thumbnail,
    selectedPage: selected,
    dataFetching: false,
  }
}

/**
 * Opens a dialog to select a single image asset from the Contentful UI.
 * 
 * This function is used to select an asset of type 'thumbnail' or 'badge'
 * and updates the state with the asset's URL and status. If no asset is
 * selected, it returns an object indicating data fetching is complete.
 *
 * @param sdk - The HomeAppSDK instance used to interact with the Contentful dialogs.
 * @param type - The type of asset being selected, either 'thumbnail' or 'badge'.
 * @returns An object containing the selected asset's URL and status, or
 *          a data fetching complete status if no asset is selected.
 */

export const selectSingleAsset = async (sdk: HomeAppSDK, type: 'thumbnail' | 'badge') => {

  const asset = (await sdk.dialogs.selectSingleAsset({ mimetypeGroups: ['image'] })) as any

  if (!asset) {

    return {
      dataFetching: false,
    }
  }
  return {
    [type]: {
      url: asset?.fields?.file?.['en-CA'].url,
      status: asset?.sys?.fieldStatus?.['*']?.['en-CA'],
    }, dataFetching: false,
  }
}

/**
 * Creates a single asset from a file input event and updates the state with the asset's URL and status.
 * 
 * This function is used to create a single asset of type 'thumbnail' or 'badge' from a file input event.
 * It uploads the file to Contentful using the HomeAppSDK and updates the state with the asset's URL and status.
 * If no file is selected, it returns an object indicating data fetching is complete.
 *
 * @param event - The file input event containing the file to be uploaded.
 * @param sdk - The HomeAppSDK instance used to interact with the Contentful dialogs.
 * @param type - The type of asset being created, either 'thumbnail' or 'badge'.
 * @returns An object containing the created asset's URL and status, or
 *          a data fetching complete status if no file is selected.
 */
export const createSingleAsset = async (event: any, sdk: HomeAppSDK, type: 'thumbnail' | 'badge') => {

  const file = event?.target?.files?.[0]

  if (!file) {
    return { dataFetching: false }
  }

  //Create or update asset
  const asset: any = await createOrUpdateAsset(file, 'en-CA')

  return {
    dataFetching: false,
    [type]: {
      url: asset?.fields?.file?.['en-CA']?.url,
      status: asset?.sys?.fieldStatus?.['*']?.['en-CA'],
    },
  }
}

/**
 * Saves a notification as history by either creating a new configuration entry or updating an existing one.
 * @param data - The notification data to be saved as history.
 * @param domain - The domain for which the notification is being saved as history.
 * @param id - An optional ID of an existing configuration entry. If not provided, a new configuration entry will be created.
 * @param dataSource - The current configuration data.
 * @param type - The type of notification being saved, either 'realTime' or 'sticky'.
 */
export const SaveNotificationAsHistory = async ({ data, domain, id, dataSource, type }: {
  data: any,
  id?: string,
  domain: string,
  dataSource: any,
  type: 'realTime' | 'sticky'
}) => {
  if (!id) {
    const payload = {
      [domain]: {
        [type]: [{ ...data }],
      },
    }
    const fields = {
      internalName: {
        'en-CA': 'MSA - Notifications History',
      },
      scope: {
        'en-CA': 'MSA',
      },
      type: {
        'en-CA': 'Notifications',
      },
      data: {
        'en-CA': {
          content: [
            {
              content: [
                {
                  data: {},
                  marks: [],
                  nodeType: 'text',
                  value: JSON.stringify(payload),
                },
              ],
              data: {},
              nodeType: 'paragraph',
            },
          ],
          data: {},
          nodeType: 'document',
        },
      },
    }

    await createContentEntry('configurations', fields)
  } else {
    const payload = {
      ...dataSource,
      [domain]: {
        ...dataSource?.[domain],
        [type]: [
          ...(dataSource?.[domain]?.[type] || []), // Ensure it's an empty array if undefined
          data,
        ],
      },
    }

    await SaveConfigurationandData(payload, id)
  }
}