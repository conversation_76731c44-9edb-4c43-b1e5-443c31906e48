import {
  Box,
  Checkbox,
  FormControl,
  Select,
  Tabs,
  TextInput
} from '@contentful/f36-components'
import { Flex } from 'antd'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { RootState } from '../../../redux/store'
import ColorsInputV2 from '../../ColorsInputV2'
import { getColorStyles } from '../../ColorsInputV2/utils'
import ColorPickerPopup from '../../ConfigurationEngine/Components/ColorPickerPopup'
import '../index.scss'
import { defaults } from '../utils'

function DvGrid(props: any) {
  const { onChange, template, changedData } = props

  const [gridData, setGridData] = useState<any>({})
  const [isBgOrGridLineColor, setIsBgOrGridLineColor] = useState('')
  const [showColorPicker, setShowColorPicker] = useState(false)
  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )

  useEffect(() => {
    const preferenceData =
      dvGlobalData?.DataVisualization?.[template]?.['grid'] ??
      defaults?.[template]?.['grid']

    setGridData((pre) => {
      return {
        ...pre,
        ...preferenceData,
        ...changedData,
      }
    })
  }, [dvGlobalData, changedData])

  const handleGridDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...gridData,
      [key]: value,
    }

    setGridData(dataToUpdate)

    const changedDataToUpdate = {
      ...changedData,
      [key]: value,
    }
    onChange({ ...changedDataToUpdate })
  }

  return (
    <Tabs.Panel id='dv-grid' className='tabPanelDiv'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          gap: '30px',
        }}
      >
        {template !== 'Pie chart' &&
          template !== 'Doughnut chart' &&
          template !== 'Treemap chart' && (
            <Box
              style={{
                width: '50%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'start',
              alignItems: 'start',
              }}
            >
              <FormControl className='fieldsFormControl' id='pri-dv-grid'>
                <div className='SwithWithTooltip'>
                  <Checkbox 
                    name='show dv grid'
                    id='show-dv-pri-grid'
                    onChange={() =>
                      handleGridDataChange('showGrid', !gridData.showGrid)
                    }
                    className='switchRoot'
                    isChecked={gridData.showGrid}
                  >
                    Enable grid
                </Checkbox>
                </div>
              </FormControl>

              {gridData.showGrid && (
                <>
                  {' '}
                  <FormControl
                  id='dv-grid-formate'
                  className='w-100 fieldsFormControl'
                >
                  <FormControl.Label>Select grid format</FormControl.Label>
                  <Select
                    id='gridFormat-controlled'
                    name='gridFormat-controlled'
                    value={gridData.gridFormat}
                    onChange={(e) =>
                      handleGridDataChange('gridFormat', e.target.value)
                    }
                  >
                    <Select.Option value='' isDisabled>
                      Format
                    </Select.Option>
                    {['Split', 'Horizontal', 'Vertical'].map((format) => (
                      <Select.Option value={format}>{format}</Select.Option>
                    ))}
                  </Select>
                </FormControl>
                <FormControl
                  id='dv-grid-lineStyle'
                  className='w-100 fieldsFormControl'
                >
                  <FormControl.Label>Select line style</FormControl.Label>
                  <Select
                    id='lineStyle-controlled'
                    name='lineStyle-controlled'
                    value={gridData.gridLineStyle || ''}
                    onChange={(e) =>
                      handleGridDataChange('gridLineStyle', e.target.value)
                    }
                  >
                    <Select.Option value='' isDisabled>
                      Line style
                    </Select.Option>
                    {['solid', 'dotted', 'dashed'].map((format) => (
                      <Select.Option value={format}>{format}</Select.Option>
                    ))}
                  </Select>
                </FormControl>
                <FormControl
                    id='dv-pri-grid-BgColor'
                  className='w-100'
                  style={{ display: 'flex', alignItems: 'center', margin: '5px 0 5px -1rem' }}
                >
                  <ColorsInputV2
                heading='grid background'
                onClose={()=>{
                  setIsBgOrGridLineColor('')
                }}
                onOpen={()=>{
                  setIsBgOrGridLineColor('gridBgColor')
                }}
                activeColor={gridData?.gridBgColor}
                onChange={(v: any) => {
                  
                  const color = getColorStyles(v).background
                  handleGridDataChange('gridBgColor', color)
                  
                  // setShowColorPicker(false)
                }}
                pickerType='Colorama'
                // pickerType='Button'
                disabled={{
                  // Accents: true,
                  // Primaries: true,
                  Gradients: true,
                  // Neutrals: true,
                  // Secondaries: true,
                }}
                pickerdisabled={isBgOrGridLineColor && isBgOrGridLineColor !== 'gridBgColor'}
              />
                  <p className="css-1k16bbs">Background Colour</p>
                  </FormControl>
                  <FormControl
                    id='dv-pri-grid-lineColor'
                  className='w-100'
                  style={{ display: 'flex', alignItems: 'center', margin: '5px 0 5px -1rem' }}
                  >

                     <ColorsInputV2
                heading='grid line'
                onClose={()=>{
                  setIsBgOrGridLineColor('')
                }}
                onOpen={()=>{
                  setIsBgOrGridLineColor('gridLineColor')
                }}
                activeColor={gridData?.gridLineColor}
                onChange={(v: any) => {
                  
                  const color = getColorStyles(v).background
                  handleGridDataChange('gridLineColor', color)
                  
                  // setShowColorPicker(false)
                }}
                pickerType='Colorama'
                // pickerType='Button'
                disabled={{
                  // Accents: true,
                  // Primaries: true,
                   Gradients: true,
                  // Neutrals: true,
                  // Secondaries: true,
                }}
                pickerdisabled={isBgOrGridLineColor && isBgOrGridLineColor !== 'gridLineColor'}
              />
                  <p className="css-1k16bbs">Line Colour</p>
                </FormControl>
                </>
              )}
            </Box>
          )}
        <Box
          style={{
            width: '50%',
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            gap: '10px',
          }}
        >
          <Flex className='w-100' vertical>
            <FormControl id='dv-pri-top' className='w-100 fieldsFormControl'>
              <FormControl.Label>Offset top</FormControl.Label>
            <TextInput
              type='number'
              value={gridData?.gridTop}
              onChange={(e) => handleGridDataChange('gridTop', e.target.value)}
                placeholder={'Offset top'}
            />
          </FormControl>

            <FormControl id='dv-pri-left' className='w-100 fieldsFormControl'>
              <FormControl.Label>Offset left</FormControl.Label>
              <TextInput
                type='number'
                value={gridData?.gridLeft}
                onChange={(e) => handleGridDataChange('gridLeft', e.target.value)}
                placeholder={'Offset left'}
              />
            </FormControl>
          </Flex>
          <Flex className='w-100' vertical>
            <FormControl id='dv-pri-bottom' className='w-100 fieldsFormControl'>
              <FormControl.Label>Offset bottom</FormControl.Label>
            <TextInput
              type='number'
              value={gridData?.gridBottom}
              onChange={(e) =>
                handleGridDataChange('gridBottom', e.target.value)
              }
                placeholder={'Offset bottom'}
            />
          </FormControl>
            <FormControl id='dv-pri-right' className='w-100 fieldsFormControl'>
              <FormControl.Label>Offset right</FormControl.Label>
            <TextInput
              type='number'
              value={gridData?.gridRight}
              onChange={(e) =>
                handleGridDataChange('gridRight', e.target.value)
              }
                placeholder={'Offset right'}
            />
          </FormControl>
          </Flex>
        </Box>
      </Box>
      <ColorPickerPopup
        isOpen={showColorPicker}
        handleClose={() => {
          setShowColorPicker(false)
        }}
        selectedColors={gridData[isBgOrGridLineColor as keyof typeof gridData]}
        onColorPick={(color: any) => {
          handleGridDataChange(isBgOrGridLineColor, color.value)
          setShowColorPicker(false)
        }}
      />
    </Tabs.Panel>
  )
}

export default DvGrid
