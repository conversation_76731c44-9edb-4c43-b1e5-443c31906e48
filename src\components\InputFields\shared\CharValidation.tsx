import * as React from 'react'

import { ValidationType } from './types'

interface CharValidationProps {
  constraints: ValidationType
}

/**
 * Renders a string describing the constraints for the number of characters.
 *
 * The text varies based on the type of constraint:
 * - 'max': Displays a message that no more than a certain number of characters are allowed.
 * - 'min': Displays a message that at least a certain number of characters are required.
 * - 'min-max': Displays a message that requires a range of characters, or exactly a number if min equals max.
 *
 * @param props - The properties for the component.
 * @param props.constraints - The validation constraint object containing type and limits.
 */
export function CharValidation(props: CharValidationProps) {
  const { constraints } = props

  if (constraints.type === 'max') {
    return <span>Maximum {constraints.max} characters</span>
  } else if (constraints.type === 'min') {
    return <span>Requires at least {constraints.min} characters</span>
  } else {
    return (
      <span>
        Requires between {constraints.min} and {constraints.max} characters
      </span>
    )
  }
}
