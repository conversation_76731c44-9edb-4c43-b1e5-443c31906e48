import * as React from 'react'

import { isUnsupportedError } from '../../common/EntityStore'
import { MissingEntityCard } from '../MissingEntityCard/MissingEntityCard'
import { UnsupportedEntityCard } from './UnsupportedEntityCard'

/**
 * A card that is rendered when there is an error while loading a resource.
 *
 * @param linkType - The type of the resource that is being loaded.
 * @param error - The error that occurred while loading the resource.
 * @param isSelected - If true, the card will be rendered with a selected style.
 * @param isDisabled - If true, the card will be disabled and the remove button will not be rendered.
 * @param onRemove - The function to be called when the user clicks the remove button.
 */
export function ResourceEntityErrorCard(props: {
  linkType: string
  error: unknown
  isSelected?: boolean
  isDisabled: boolean
  onRemove?: Function
}) {
  if (isUnsupportedError(props.error)) {
    return (
      <UnsupportedEntityCard
        linkType={props.linkType}
        isSelected={props.isSelected}
      />
    )
  }

  return (
    <MissingEntityCard
      entityType='Entry'
      isDisabled={props.isDisabled}
      isSelected={props.isSelected}
      onRemove={props.onRemove}
    />
  )
}
