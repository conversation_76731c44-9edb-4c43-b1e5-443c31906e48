import * as React from 'react'

import { ScheduledAction, SpaceAPI } from '@contentful/app-sdk'

import { ScheduleTooltip } from './ScheduleTooltip'

type ScheduledIconWithTooltipProps = {
  getEntityScheduledActions: SpaceAPI['getEntityScheduledActions']
  entityType: 'Entry' | 'Asset'
  entityId: string
  children: React.ReactElement
}

/**
 * A component that displays an icon with a tooltip for scheduled actions related to an entity.
 *
 * This component fetches the scheduled actions for a given entity type and ID using the provided
 * `getEntityScheduledActions` function. It displays a tooltip with information about the most relevant
 * scheduled action and the total number of scheduled actions if any exist. The tooltip is rendered
 * using the `ScheduleTooltip` component, and the child elements are passed as children to this component.
 *
 * @param {object} props - The component props.
 * @param {'Entry' | 'Asset'} props.entityType - The type of entity for which to fetch scheduled actions.
 * @param {string} props.entityId - The ID of the entity for which to fetch scheduled actions.
 * @param {Function} props.getEntityScheduledActions - A function to retrieve the scheduled actions for the entity.
 * @param {React.ReactElement} props.children - The children to be rendered within the tooltip.
 * @returns {React.ReactElement | null} The rendered tooltip component or null if loading or no scheduled actions exist.
 */

export const ScheduledIconWithTooltip = ({
  entityType,
  entityId,
  getEntityScheduledActions,
  children,
}: ScheduledIconWithTooltipProps) => {
  const [status, setStatus] = React.useState<
    | { type: 'loading' }
    | { type: 'error'; error: Error }
    | { type: 'loaded'; jobs: ScheduledAction[] }
  >({ type: 'loading' })

  React.useEffect(() => {
    getEntityScheduledActions(entityType, entityId)
      .then((data) => {
        setStatus({ type: 'loaded', jobs: data })
      })
      .catch((e) => {
        setStatus({ type: 'error', error: e })
      })
    // This should only be ever called once. Following the eslint hint to add used
    // dependencies will cause page freeze (infinite loop)
    // eslint-disable-next-line -- TODO: describe this disable
  }, [])

  if (status.type === 'loading' || status.type === 'error') {
    return null
  }

  const jobs = status.jobs ?? []

  if (jobs.length === 0) {
    return null
  }

  const mostRelevantJob = jobs[0]

  return (
    <ScheduleTooltip job={mostRelevantJob} jobsCount={jobs.length}>
      {children}
    </ScheduleTooltip>
  )
}
