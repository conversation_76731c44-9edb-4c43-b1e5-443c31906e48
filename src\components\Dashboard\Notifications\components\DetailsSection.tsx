import {
  <PERSON>set<PERSON><PERSON>,
  AssetStatus,
  Box,
  Button,
  MenuItem,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import { LinkIcon, PlusIcon } from '@contentful/f36-icons'
import React from 'react'
import Info from '../../../../assets/icons/Info'
import FormControlComp from '../../../Shared/FormControlComp'
import {
  DomainNotiState,
  GlobalNotiState,
  UpdateNotiStateFunction,
} from '../utils/notifications.interface'

/**
 * A type guard function that checks if a given state is a DomainNotiState
 * based on the presence of the `internalName` property.
 *
 * @param state - the state to check
 * @returns a boolean indicating if the state is a DomainNotiState
 */
function isDomainNotiState(
  state: DomainNotiState | GlobalNotiState
): state is DomainNotiState {
  return 'internalName' in state // Check if it has the internalName property
}

/**
 * Renders the DetailsSection component for managing notification details.
 *
 * This component allows users to input and manage details for a notification,
 * including title, description, internal name, thumbnail, and badge. It also
 * provides functionality to handle file changes and asset selection.
 *
 * Props:
 * - state: The current state of the notification, either DomainNotiState or GlobalNotiState.
 * - updateState: A function to update the notification state.
 * - selectSingleAsset: A function to select a single asset, either a thumbnail or badge.
 * - handleFileChange: A function to handle file input changes.
 */

function DetailsSection({
  state,
  updateState,
  selectSingleAsset,
  handleFileChange,
}: {
  state: DomainNotiState | GlobalNotiState
  updateState: UpdateNotiStateFunction<DomainNotiState | GlobalNotiState>
  selectSingleAsset: any
  handleFileChange: any
  fromGlobal?: boolean
  fromMsa?: boolean
}) {
  const {
    typeOfNotification,
    title,
    description,
    thumbnail,
    isLoading,
    badge,
  } = state

  return (
    <Box className='flex justify-start items-start flex-col w-full'>
      <Box className='py-4 w-full'>
        <h6 className='my-2 flex gap-2'>
          Details{' '}
          <Tooltip
            placement='right'
            content={
              'Auto-fill details by selecting Page/CTA source or enter manually'
            }
          >
            <Info />
          </Tooltip>
        </h6>
        <hr className='endHorizontalLine w-full' />
      </Box>
      {isDomainNotiState(state) && typeOfNotification === '1' && (
        <>
          <FormControlComp
            label='Internal Name'
            tooltip={`Enter Internal Name for this Notification`}
            isRequired={typeOfNotification === '1'}
          >
            <TextInput
              value={state?.internalName}
              placeholder='Enter Internal Name'
              onChange={(e) =>
                updateState({
                  internalName: e?.target?.value,
                })
              }
            />
          </FormControlComp>
          <Box className='h-5' />
        </>
      )}
      <FormControlComp
        label='Title'
        tooltip={`Enter Title for this Notification`}
        isRequired={true}
      >
        <TextInput
          value={title}
          placeholder='Enter Title'
          onChange={(e) =>
            updateState({
              title: e?.target?.value,
            })
          }
        />
      </FormControlComp>
      <Box className='h-5' />
      <FormControlComp
        label='Description'
        tooltip={`Enter Description for this Notification`}
        isRequired={true}
      >
        <TextInput
          value={description}
          placeholder='Enter Description'
          onChange={(e) =>
            updateState({
              description: e?.target?.value,
            })
          }
        />
      </FormControlComp>
      <Box className='h-5' />
      {typeOfNotification !== '1' && (
        <FormControlComp
          label='Thumbnail Image'
          tooltip='Select Image for this Notification'
          isRequired={typeOfNotification !== '1'}
        >
          {thumbnail?.url ? (
            <AssetCard
              status={thumbnail?.status as AssetStatus}
              type='image'
              // title='Everest'
              src={thumbnail?.url}
              size='small'
              actions={[
                <MenuItem
                  key='delete'
                  onClick={() => {
                    updateState({
                      thumbnail: {
                        url: '',
                        status: '',
                      },
                    })
                  }}
                >
                  Remove
                </MenuItem>,
              ]}
            />
          ) : (
            <div className='dv-inputDiv'>
              <div className={`dv-newFile `}>
                <input
                  type='file'
                  hidden
                  id='new-file'
                  onChange={(e) => handleFileChange(e, 'thumbnail')}
                  disabled={isLoading}
                />
                <Button
                  variant='secondary'
                  startIcon={<PlusIcon />}
                  size='small'
                  onClick={() => document.getElementById('new-file')?.click()}
                  isLoading={isLoading}
                >
                  Add new Asset
                </Button>
              </div>
              <Button
                variant='secondary'
                startIcon={<LinkIcon />}
                size='small'
                onClick={() => selectSingleAsset('thumbnail')}
                isDisabled={isLoading}
              >
                Add existing Asset
              </Button>
            </div>
          )}
        </FormControlComp>
      )}
      <Box className='h-5' />
      {typeOfNotification !== '1' && (
        <FormControlComp
          label='Notification Badge'
          tooltip='Notification Badge will only be visible on mobile devices'
          // isRequired={typeOfNotification !== '1' }
        >
          {badge?.url ? (
            <AssetCard
              status={badge?.status as AssetStatus}
              type='image'
              // title='Everest'
              src={badge?.url}
              size='small'
              actions={[
                <MenuItem
                  key='delete'
                  onClick={() => {
                    updateState({
                      badge: {
                        url: '',
                        status: '',
                      },
                    })
                  }}
                >
                  Remove
                </MenuItem>,
              ]}
            />
          ) : (
            <div className='dv-inputDiv'>
              <div className={`dv-newFile `}>
                <input
                  type='file'
                  hidden
                  id='new-file'
                  onChange={(e) => handleFileChange(e, 'thumbnail')}
                  disabled={isLoading}
                />
                <Button
                  variant='secondary'
                  startIcon={<PlusIcon />}
                  size='small'
                  onClick={() => document.getElementById('new-file')?.click()}
                  isLoading={isLoading}
                >
                  Add new Asset
                </Button>
              </div>
              <Button
                variant='secondary'
                startIcon={<LinkIcon />}
                size='small'
                onClick={() => selectSingleAsset('badge')}
                isDisabled={isLoading}
              >
                Add existing Asset
              </Button>
            </div>
          )}
        </FormControlComp>
      )}
    </Box>
  )
}

export default DetailsSection
