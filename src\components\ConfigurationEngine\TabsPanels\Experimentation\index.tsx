import React, { useEffect, useState } from 'react'
import { getConfigurationByScopeAndType } from '../../../../globals/utils'
import { Alert } from '../../../atoms'
import ExperimentForm from './ExperimentForm'
import {
  Domains,
  getDomainFullName,
  getDomainWebsiteName,
} from '../../../Crosspost/utils'
import { ALLOWED_DOMAINS } from '../../../../globals/experimentation-util'
interface ExperimentationProps {
  selectedDomain: Domains
  // handleChange: (key: string, value: any) => void
  // value: unknown
  // refetchData: Function
  // contentId: string
}
const type = 'Experimentation'
const Experimentation = (props: ExperimentationProps) => {
  const { selectedDomain } = props
  const [contentId, setContentId] = useState('')
  const [msaConfigData, setMsaConfigData] = useState<any>({})
  const [loading, setloading] = useState(false)
  const domainTag = getDomainFullName(selectedDomain)
  const isDomainAllowed = ALLOWED_DOMAINS.includes(domainTag)
  const fetchData = async () => {
    setloading(true)
    console.log(selectedDomain, 'configurationData')

    const configurationData: any = await getConfigurationByScopeAndType(
      type,
      `${selectedDomain}`?.toUpperCase()
    )
    if (configurationData && configurationData?.data && configurationData?.id) {
      setMsaConfigData(configurationData?.data ?? {})
      setContentId(configurationData?.id)
    } else {
      setMsaConfigData({})
      setContentId('')
    }
    setloading(false)
  }
  useEffect(() => {
    if (!isDomainAllowed) return
    fetchData()
  }, [])
  if (!isDomainAllowed) {
    return (
      <Alert
        type='warning'
        message={`Experimentation is not supported for ${getDomainWebsiteName(
          domainTag
        )}. Please select a valid domain.`}
        showIcon
        style={{ margin: '20px' }}
        description={`Supported domains are: ${ALLOWED_DOMAINS.map(
          getDomainWebsiteName
        ).join(', ')}.`}
        closable={false}
        banner
      />
    )
  }
  return (
    <>
      {/* {loading ? (
        <Box
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
          }}
        >
          Loading ...
        </Box>
      ) : ( */}
      <ExperimentForm
        data={msaConfigData}
        contentId={contentId}
        refetchData={fetchData}
        initialPageData={null}
        loading={loading}
        selectedDomain={selectedDomain}
        domainTagFullName={domainTag}
      />
      {/* )} */}
    </>
  )
}

export default Experimentation
