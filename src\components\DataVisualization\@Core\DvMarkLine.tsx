import {
  Box,
  Flex,
  FormControl,
  Tabs,
  TextInput
} from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import { GlobalContext } from '../../../contexts/globalContext'
import '../index.scss'
import { getCategories, getDimensions } from '../utils'
import MarkLineFields from './MarkLineFields'

function DvMarkLine(props: any) {
  const { onChange, template, changedData, asset, isGlobal, chartData, axisData } = props

  const [markLineData, setMarkLineData] = useState<any>({})
  const [headers, setHeader] = useState<any>([])
  const [categories, setCategories] = useState<any>({})
  const { currentLocale } = useContext(GlobalContext)

  useEffect(() => {
    setMarkLineData((pre) => {
      return {
        ...pre,
        ...changedData,
        markLineData: [...(changedData?.['markLineData'] ?? [])],
      }
    })
    const fetch = async () => {
      const dimension = await getDimensions(
        template,
        asset,
        isGlobal,
        currentLocale
      )
      const categories = await getCategories(asset, currentLocale)
      setHeader(dimension)
      setCategories(categories)
    }
    fetch()
  }, [changedData])

  const handleMarkLineData = (key: string, value: any, id?: string) => {
    let dataToUpdate
    if (key === 'markLineData') {
      const updatedArray = [...(markLineData?.[key] || [])]
      const index = updatedArray.findIndex((item) => item.id === id)

      if (index > -1) {
        // If index exists, merge the existing object at index with the new value
        updatedArray[index] = {
          ...updatedArray[index], // Merge existing object at index
          ...value, // Overwrite with new values
        }
      } else {
        // If index doesn't exist (-1), add a new object to the array
        updatedArray.push({
          id: id,
          ...value, // Add new value as a new object
        })
      }

      dataToUpdate = {
        ...markLineData,
        [key]: updatedArray,
      }
    } else if (key === 'MarkLineCount') {
      // If the count decreases, remove the excess elements from the array
      const currentCount = Number(markLineData?.MarkLineCount || 0)
      const newCount = Number(value)
      let updatedArray = [...(markLineData?.['markLineData'] || [])]

      if (newCount < currentCount) {
        // If the new count is smaller, slice the array to the new size
        updatedArray = updatedArray.slice(0, newCount)
      }

      dataToUpdate = {
        ...markLineData,
        MarkLineCount: newCount,
        markLineData: updatedArray,
      }
    } else {
      dataToUpdate = {
        ...markLineData,
        [key]: value,
      }
    }

    setMarkLineData(dataToUpdate)
    onChange({ ...dataToUpdate })
  }

  return (
    <Tabs.Panel id='dv-mark' className='tabPanelDiv'>
      {template === 'Pie chart' ||
        template === 'Doughnut chart' || 
        template === 'Treemap chart' || template === 'BarRace chart' ? (
        <p style={{ textAlign: 'center', marginTop: '40px' }}>
          Mark Line do not applied for {template}.{' '}
        </p>
      ) : (
        <>
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'start',
                flexDirection: 'column',
            }}
          >
            <FormControl
              id='dv-pri-Number of Markline'
              className='w-50 fieldsFormControl'
              >
                <FormControl.Label> Select number of marklines</FormControl.Label>
              <TextInput
                aria-label='dv-Number of Markline-input'
                id='dv-Number of Markline-input'
                  placeholder='Number of mark line'
                type='number'
                value={markLineData?.MarkLineCount}
                onChange={(e) =>
                  handleMarkLineData('MarkLineCount', e.target.value)
                }
                  min={0}
              />
            </FormControl>
              <Flex gap='30px' flexWrap='wrap' className='w-100' >
              {Array.from({ length: Number(markLineData?.MarkLineCount) }).map(
                (_, i) => {
                  const updatedArray = [
                    ...(markLineData?.['markLineData'] || []),
                  ]
                  const index = updatedArray.findIndex(
                    (item) => item.id === `markLine${i}`
                  )
                  return (
                    <MarkLineFields
                      key={i}
                      headers={headers}
                      index={i}
                      markLineFieldsData={updatedArray[index]}
                      handleFieldsUpdate={(data: any) =>
                        handleMarkLineData('markLineData', data, `markLine${i}`)
                      }
                      chartPreference={chartData}
                      categories={categories}
                      axisPreference={axisData}
                      template={template}
                    />
                  )
                }
              )}
            </Flex>
          </Box>
        </>
      )}
    </Tabs.Panel>
  )
}

export default DvMarkLine
