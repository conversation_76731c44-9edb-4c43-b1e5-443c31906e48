import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Checkbox } from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import { GlobalContext } from '../../../../contexts/globalContext'
import { getLocaleFullName } from '../../../Crosspost/utils'
import InputFieldsRenderer from '../../../InputFields/InputFieldsRenderer'
import { DYNAMIC_PAGE_PREFIX } from '../DynamicPage/utils'
import { Tooltip } from 'antd'
import { BiInfoCircle } from 'react-icons/bi'

interface Props {
  sdk: EditorAppSDK
  locale: string[]
  domain: string
}

function General(props: Props) {
  const { sdk, locale, domain } = props

  const { currentLocale } = useContext(GlobalContext)

  const sdkFields = sdk.entry.fields

  const isExperimentRunning =
    sdkFields?.['isExperimentation'].getForLocale('en-CA').getValue() &&
    sdkFields?.['experimentationId'].getForLocale('en-CA').getValue()

  const slugField = sdk.entry.fields['slug']

  const [isPageDynamic, setIsPageDynamic] = useState<boolean>(false)

  const [refresh, setRefresh] = useState(false)

  const template = sdkFields['template'].getForLocale('en-CA').getValue()

  useEffect(() => {
    const isPageDynamicFlag = locale.every((locale) => {
      const slug = slugField?.getValue(locale) || ''
      return getNormalizedSlug(slug)?.startsWith(DYNAMIC_PAGE_PREFIX)
    })

    setIsPageDynamic(isPageDynamicFlag)
  }, [locale, refresh, slugField])

  useEffect(() => {
    if (domain === 'agl') {
      sdk.entry.fields['slug'].getForLocale('en-CA').onValueChanged((x) => {
        sdk.entry.fields['slug'].getForLocale('fr-CA').setValue(x)
      })
    }
  }, [sdk])

  const handleToggleDynamic = async (checked: boolean) => {
    console.log(JSON.stringify({ locale }, null, 2))

    const pr = locale.map(async (loc) => {
      const slugFieldPerLocale = sdkFields?.['slug']?.getForLocale(loc)

      if (!slugFieldPerLocale) {
        console.warn(`Locale '${loc}' not found or field not localized.`)
        return
      }

      const currentSlug = slugFieldPerLocale.getValue() || ''
      const normalized = getNormalizedSlug(currentSlug)

      if (checked) {
        // Add prefix if not already present
        if (!normalized?.startsWith(DYNAMIC_PAGE_PREFIX)) {
          const updatedValue = `${DYNAMIC_PAGE_PREFIX}/${normalized}`
          await slugFieldPerLocale.setValue(updatedValue)
        }
      } else {
        // Remove prefix if present
        if (normalized?.startsWith(DYNAMIC_PAGE_PREFIX)) {
          const withoutPrefix = normalized.replace(
            new RegExp(`^${DYNAMIC_PAGE_PREFIX}/?`),
            ''
          )
          await slugFieldPerLocale.setValue(withoutPrefix)
        }
      }
    })

    await Promise.all(pr)

    // Force a UI refresh (especially if isPageDynamic is derived from field)
    setRefresh((prev) => !prev)
  }

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-10 p-3'>
      <Box className='w-full flex justify-start items-start flex-col gap-5'>
        <InputFieldsRenderer
          currentLocale='en-CA'
          fieldId='template'
          isPageSettingsFields={true}
          label='Page Template'
          isRequired={sdkFields?.['template'].required}
          tooltip={`Select a relevant template to set the page's structure and layout.`}
        />

        <InputFieldsRenderer
          currentLocale='en-CA'
          fieldId='publishDate'
          isPageSettingsFields={true}
          label='Publish Date'
          tooltip={`elect the page's publication date and time.`}
          isRequired={sdkFields?.['publishDate'].required}
        />

        <Box className='flex items-center justify-start w-full gap-1.5 pt-1.5'>
          <Checkbox
            isChecked={isPageDynamic}
            onChange={(e) => handleToggleDynamic(e.target.checked)}
            className='p-0'
          >
            is Dynamic Page ?
          </Checkbox>
          <Tooltip
            placement='right'
            title={`slug will be prefixed with "${DYNAMIC_PAGE_PREFIX}"`}
          >
            <BiInfoCircle />
          </Tooltip>
        </Box>
        {/***
         * comment here regarding slug field
         * slug and chcekbox will not update relatime due to sdk entry fields contenfull limation you might need to change and then exit and go to another tab and come backe it will reflect
         *
         *
         * make this here in grey text for user
         *
         *
         */}
        <p className='text-sm text-gray-500'>
          <span className='font-semibold'>Note:</span> Due to a Contentful
          limitation, the slug may not update immediately after checking the
          box. To refresh it, close the modal, switch tabs, then return.
        </p>

        {locale.map((locale) => {
          return (
            <InputFieldsRenderer
              currentLocale={locale}
              fieldId='slug'
              isPageSettingsFields={true}
              label={`Slug (${locale.split('-')[0].toUpperCase()})`}
              tooltip={`Add the ${getLocaleFullName(
                locale.split('-')[0]
              )} version of the page slug.`}
              isRequired={locale === 'en-CA' && sdkFields?.['slug']?.required}
              key={'slug-' + locale}
              isDisabled={Boolean(
                Boolean(domain === 'agl' && locale === 'fr-CA') ||
                  Boolean(isExperimentRunning)
              )}
            />
          )
        })}
        {/* {domain === 'agl' && (
          <p className='text-sm text-gray-500'>
            Slug will be auto-populated for French based on English slug
          </p>
        )}
        {isExperimentRunning && (
          <p className='text-sm text-gray-500'>
            Slug is disabled while experiment is running
          </p>
        )}
        {isPageDynamic && (
          <p className='text-sm text-gray-500'>
            Slug will be prefixed with {DYNAMIC_PAGE_PREFIX}
          </p>
        )} */}

        <InputFieldsRenderer
          currentLocale='en-CA'
          fieldId='canonicalUrl'
          isPageSettingsFields={true}
          label={`Canonical URL`}
          isRequired={sdkFields?.['canonicalUrl']?.required}
          tooltip='Add the canonical slug of the preferred version of the page to avoid duplicate content.'
        />
      </Box>
      <Box className='w-full flex justify-start items-start flex-col gap-5'>
        {currentLocale === 'en-CA' && (
          <InputFieldsRenderer
            currentLocale='en-CA'
            fieldId='pageThumbnail'
            isPageSettingsFields={true}
            label={`Thumbnail Image`}
            tooltip='Add the image that will be used to generate thumbnail previews when sharing this page URL.'
            isRequired={sdkFields?.['pageThumbnail']?.required}
          />
        )}
        {(template === 'Insight Article' || template === 'Press Release') && (
          <>
            {locale.map((locale) => (
              <InputFieldsRenderer
                currentLocale={locale}
                fieldId='authorsHeading'
                isPageSettingsFields={true}
                label={`Author Heading (${locale.split('-')[0].toUpperCase()})`}
                tooltip={`Add the ${getLocaleFullName(
                  locale.split('-')[0]
                )} version of the author heading.`}
                isRequired={
                  locale === 'en-CA' && sdkFields?.['authorsHeading']?.required
                }
                key={'authorsHeading-' + locale}
              />
            ))}
            {currentLocale === 'en-CA' && (
              <InputFieldsRenderer
                currentLocale='en-CA'
                fieldId='authors'
                isPageSettingsFields={true}
                label={`Authors`}
                tooltip='Add the author cards for this page.'
                isRequired={sdkFields?.['authors']?.required}
              />
            )}
          </>
        )}
      </Box>
    </Box>
  )
}

export default General

export const getNormalizedSlug = (slug: unknown): string => {
  if (typeof slug !== 'string') return ''
  return slug.startsWith('/') ? slug.slice(1) : slug
}
