import * as React from 'react'

import { Text } from '@contentful/f36-components'
import { css } from 'emotion'

import { Constraint, ConstraintsType } from './types'

interface TagEditorConstraintsProps {
  constraintsType: ConstraintsType
  constraints: Constraint
}

/**
 * Renders a text element that describes the constraints for the number of tags.
 * 
 * The text varies based on the type of constraint:
 * - 'min': Displays a message that at least a certain number of tags are required.
 * - 'max': Displays a message that no more than a certain number of tags are allowed.
 * - 'min-max': Displays a message that requires a range of tags, or exactly a number if min equals max.
 * 
 * @param props - The properties for the component.
 * @param props.constraintsType - The type of constraints applied ('min', 'max', 'min-max').
 * @param props.constraints - The constraint values for the tags, containing 'min' and/or 'max'.
 */

export function TagsEditorConstraints(props: TagEditorConstraintsProps) {
  const { constraintsType, constraints } = props
  return (
    <Text
      as='p'
      fontColor='gray600'
      marginBottom='none'
      marginTop='spacingS'
      className={css({
        fontStyle: 'italic',
      })}
      testId='tag-editor-constraints'
    >
      {constraintsType === 'min' && (
        <span>
          Requires at least {constraints.min}{' '}
          {constraints.min === 1 ? 'tag' : 'tags'}
        </span>
      )}
      {constraintsType === 'max' && (
        <span>
          Requires no more than {constraints.max}{' '}
          {constraints.max === 1 ? 'tag' : 'tags'}
        </span>
      )}
      {constraintsType === 'min-max' && constraints.max !== constraints.min && (
        <span>
          Requires between {constraints.min} and {constraints.max} tags
        </span>
      )}
      {constraintsType === 'min-max' && constraints.max === constraints.min && (
        <span>
          Requires exactly {constraints.max}{' '}
          {constraints.max === 1 ? 'tag' : 'tags'}
        </span>
      )}
    </Text>
  )
}
