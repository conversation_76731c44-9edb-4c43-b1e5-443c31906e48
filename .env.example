# Contentful Space ID - Identifies the specific Contentful space being used
REACT_APP_CONTENTFUL_SPACE_ID=your_space_id

# Contentful Environment - Specifies the environment (e.g., 'master', 'staging','dev')
REACT_APP_CONTENTFUL_ENVIRONMENT=dev

# Contentful API Tokens - Required for authentication
REACT_APP_USER_TOKEN=your_user_token   # User-level token
REACT_APP_ACCESS_TOKEN=your_access_token  # Access token for Contentful API requests

# Statsig Console Token - Used for authentication with Statsig
REACT_APP_STATSIG_CONSOLE_TOKEN=your_statsig_console_token

# Statsig Console ID - Identifier for the Statsig console
REACT_APP_STATASIG_CONSOLE_ID=your_statsig_console_id

# Vercel Bypass Secret - Used for authentication when bypassing Vercel protection
REACT_APP_VERCEL_BYPASS_SECRET=your_vercel_bypass_secret

# Base URL of the application - Defines the main URL for the frontend
REACT_APP_BASE_URL=https://your-app-url.vercel.app

# Branch information - Specifies the front-end branch being used
REACT_APP_BRANCH_FRONT=dev

# Main Preview Branch - Defines the primary branch used for previewing
REACT_APP_MAIN_PREVIEW_BRANCH=main-preview

# Determines if the main preview branch should be displayed
REACT_APP_DISPLAY_ON_MAIN_BRANCH=true
