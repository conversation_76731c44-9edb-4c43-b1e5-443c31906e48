import { ENV_VARIABLES } from '../../constant/variables'

export type Domains = 'agl' | 'fia' | 'reo' | 'ver' | 'o11' | 'for' | 'adx'

export const domains = [
  'domainAltusGroupCom',
  'domainFinanceActiveCom',
  'domainReonomyCom',
  'domainVerifinoCom',
  'domainOne11Com',
  'domainForburyCom',
  'domainAgStudio',
]
interface DomainToVercelAPI {
  [env: string]: {
    [domain: string]: string
  }
}

export const domaintoVercelAPI: DomainToVercelAPI = {
  dev: {
    agl: 'https://api.vercel.com/v1/integrations/deploy/prj_iA7Yz29zU7DeCoZIEDcQvHLvf9o9/zq09GHEJKN',
    o11: 'https://api.vercel.com/v1/integrations/deploy/prj_16X50aPzWonGUt1VqC74J2PJ6A3X/8zAFyptMm0',
    fia: 'https://api.vercel.com/v1/integrations/deploy/prj_tik5bpP5nWfIKXmLM3waXBde443Y/HUGqTpVVGk',
    reo: 'https://api.vercel.com/v1/integrations/deploy/prj_WBJhTjS0HnWc7YGvzDir7RWuNQaT/caZfNn4JTS',
    ver: 'https://api.vercel.com/v1/integrations/deploy/prj_0hPelBxv8cDYGY0FQB9x5yTE0wUl/SjKmOGvWFF',
    for: 'https://api.vercel.com/v1/integrations/deploy/prj_oA3a7yvE5KFHD5F7lw2fIeXEfEoP/p1vdQ2aMbw',
  },
  'staging-v2': {
    agl: 'https://api.vercel.com/v1/integrations/deploy/prj_iA7Yz29zU7DeCoZIEDcQvHLvf9o9/lOyKa4LCFj',
  },
  // staging: {
  //   agl: '',
  //   o11: '',
  //   fia: '',
  //   reo: '',
  //   ver: '',
  //   for: '',
  //   adx: '',
  // },
  ...(ENV_VARIABLES?.isMainPreview && {
    [ENV_VARIABLES.mainBranch]: {
      // currently this acts as main as front branch logic
      agl: 'https://api.vercel.com/v1/integrations/deploy/prj_iA7Yz29zU7DeCoZIEDcQvHLvf9o9/IvlzMQSKfW',
      o11: 'https://api.vercel.com/v1/integrations/deploy/prj_16X50aPzWonGUt1VqC74J2PJ6A3X/wsQnb5ZcaE',
      fia: 'https://api.vercel.com/v1/integrations/deploy/prj_tik5bpP5nWfIKXmLM3waXBde443Y/j9lWAcMLKo',
      reo: 'https://api.vercel.com/v1/integrations/deploy/prj_WBJhTjS0HnWc7YGvzDir7RWuNQaT/4AneexCRGv',
      ver: 'https://api.vercel.com/v1/integrations/deploy/prj_0hPelBxv8cDYGY0FQB9x5yTE0wUl/iGZZPCrrXB',
      for: 'https://api.vercel.com/v1/integrations/deploy/prj_oA3a7yvE5KFHD5F7lw2fIeXEfEoP/dT1RKlsfZk',
      // adx: '',
    },
  }),
}
export function getDomainFullName(domain: Domains) {
  switch (domain) {
    case 'agl':
      return 'domainAltusGroupCom'
    case 'fia':
      return 'domainFinanceActiveCom'
    case 'reo':
      return 'domainReonomyCom'
    case 'ver':
      return 'domainVerifinoCom'
    case 'o11':
      return 'domainOne11Com'
    case 'for':
      return 'domainForburyCom'
    case 'adx':
      return 'domainAgStudio'
    default:
      return ''
  }
}

export const getDomainWebsiteName = (domain: string) => {
  switch (domain) {
    case 'domainAltusGroupCom':
      return 'Altus Group'
    case 'domainFinanceActiveCom':
      return 'Finance Active'
    case 'domainReonomyCom':
      return 'Reonomy'
    case 'domainVerifinoCom':
      return 'Verifino'
    case 'domainOne11Com':
      return 'One11'
    case 'domainForburyCom':
      return 'Forbury'
    case 'domainAgStudio':
      return 'Ag Studio'
    default:
      return ''
  }
}

export function getDomainShortName(domain: string) {
  switch (domain) {
    case 'domainAltusGroupCom':
      return 'agl'
    case 'domainFinanceActiveCom':
      return 'fia'
    case 'domainReonomyCom':
      return 'reo'
    case 'domainVerifinoCom':
      return 'ver'
    case 'domainOne11Com':
      return 'o11'
    case 'domainForburyCom':
      return 'for'
    case 'domainAgStudio':
      return 'adx'
    default:
      return ''
  }
}

export function getLocaleFullName(locale: string) {
  switch (locale) {
    case 'en':
      return 'English'
    case 'fr':
      return 'French'
    case 'de':
      return 'German'
    case 'es':
      return 'Spanish'
    case 'nl':
      return 'Dutch'
    case 'it':
      return 'Italian'
    default:
      return ''
  }
}


export const domainsConfig: DomainConfig[] = [
  {
    key: 'agl',
    label: 'Altus Group',
    domainKey: 'domainAltusGroupCom',
    locales: ['en-CA', 'fr-CA'],
    urlwithoutbackslash: 'https://www.altusgroup.com',
    url: 'https://www.altusgroup.com/',
    primaryLang: 'en-CA',
  },
  {
    key: 'fia',
    label: 'Finance Active',
    domainKey: 'domainFinanceActiveCom',
    locales: ['en-CA', 'fr-CA', 'de-DE', 'es', 'nl', 'it'],
    urlwithoutbackslash: 'https://financeactive.com',
    url: 'https://financeactive.com/',
    primaryLang: 'fr-CA',
  },
  {
    key: 'ver',
    label: 'Verifino',
    domainKey: 'domainVerifinoCom',
    locales: ['en-CA', 'de-DE'],
    urlwithoutbackslash: 'https://verifino.com',
    url: 'https://verifino.com/',
    primaryLang: 'de-DE',
  },
  {
    key: 'reo',
    label: 'Reonomy',
    domainKey: 'domainReonomyCom',
    locales: ['en-CA', 'fr-CA'],
    urlwithoutbackslash: 'https://reonomy.com',
    url: 'https://reonomy.com/',
    primaryLang: 'en-CA',
  },
  {
    key: 'o11',
    label: 'One11',
    domainKey: 'domainOne11Com',
    locales: ['en-CA', 'fr-CA'],
    primaryLang: 'en-CA',

    urlwithoutbackslash: '',
    url: '',
  },
  {
    key: 'for',
    label: 'Forbury',
    domainKey: 'domainForburyCom',
    locales: ['en-CA'],
    primaryLang: 'en-CA',
    urlwithoutbackslash: 'https://www.forbury.com',
    url: 'https://www.forbury.com/',
  },
  {
    key: 'adx',
    label: 'Ag Studio',
    domainKey: 'domainAgStudio',
    locales: ['en-CA'],
    primaryLang: 'en-CA',
    urlwithoutbackslash: 'https://msa-adx-v3w-git-main-altus.vercel.app',
    url: 'https://msa-adx-v3w-git-main-altus.vercel.app/',
  },
]

export function renameKeys(obj: any, domain: string) {
  const newObj: any = {}
  // const isDomainFia = domain === 'fia'

  const isDomainFia = false
  for (const key in obj) {
    if (key === 'en') {
      newObj['en-CA'] = obj[key]
    } else if (key === 'fr') {
      newObj['fr-CA'] = isDomainFia ? 'fr/' + obj[key] : obj[key]
    } else if (key === 'de') {
      newObj['de-DE'] = isDomainFia ? 'fr/' + obj[key] : obj[key]
    } else {
      newObj[key] = isDomainFia ? 'fr/' + obj[key] : obj[key]
    }
  }
  return newObj
}


export function filterEmptyObjects(data: any) {
  // Iterate over each key in the object
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      // Check if the current property is an object and not null
      if (typeof data[key] === 'object' && data[key] !== null) {
        let isEmpty = true // Assume the inner object is empty initially

        // Check each key in the inner object
        for (const innerKey in data[key]) {
          if (data[key][innerKey]) {
            // If any value is not empty, set isEmpty to false and break
            isEmpty = false
            break
          }
        }

        // If isEmpty is still true, delete the inner object
        if (isEmpty) {
          delete data[key]
        }
      }
    }
  }
  return data
}

export function getDomainTagsFromDomainSlugJson(data: any) {
  if (!data) return

  let tagsToAdd = Object.keys(data)
    .map((key) => {
      return getDomainShortName(key)
    })
    .filter((key) => {
      return key !== ''
    })
    .map((key) => {
      return {
        sys: {
          type: 'Link',
          linkType: 'Tag',
          id: getDomainFullName(key),
        },
      }
    })

  return tagsToAdd
}

export function mergeUniqueTags(tags: any[], d: any[]) {
  if (!d) return

  const uniqueTags = new Set()
  const mergedArray = [...tags, ...d].reduce((acc, item) => {
    const id = item.sys.id
    if (!uniqueTags.has(id)) {
      uniqueTags.add(id)
      acc.push(item)
    }
    return acc
  }, [])
  return mergedArray
}

export const getDomainPreview = (
  domain: string,
  slug: string,
  locale: string
) => {
  if (slug) return addLangParaToURL(domain, locale, slug)
  else return `https://msa-${domain}-v3w-git-tmpcrosspost-altus.vercel.app/`
}

const addLangParaToURL = (domain: string, locale: string, url: string) => {
  if (locale !== 'en') {
    if (domain === 'agl')
      return `https://msa-${domain}-v3w-git-tmpcrosspost-altus.vercel.app/${url}/?lang=${locale}`

    if (domain === 'fia')
      return `https://msa-${domain}-v3w-git-tmpcrosspost-altus.vercel.app/${locale}/${url}`
  }

  return `https://msa-${domain}-v3w-git-tmpcrosspost-altus.vercel.app/${url}`
}

export const checkEachKeyValue = (data: { [key: string]: string }) => {
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      if (data[key] === '') {
        return true
      }
    }
  }
  return false
}

type DomainConfig = {
  key: string
  label: string
  domainKey: string
  locales: string[]
  urlwithoutbackslash: string
  url: string
  primaryLang: string
}

export function findDomainConfig<T extends keyof DomainConfig>(
  searchKey: T,
  searchValue: DomainConfig[T]
): DomainConfig | undefined {
  return domainsConfig.find((config) => config[searchKey] === searchValue)
}
