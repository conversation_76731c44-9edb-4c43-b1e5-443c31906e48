import {
  Box,
  Checkbox,
  FormControl,
  IconButton,
  Select,
  Tabs,
  TextInput
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { RootState } from '../../../redux/store'
import ColorsInputV2 from '../../ColorsInputV2'
import { getColorStyles } from '../../ColorsInputV2/utils'
import '../index.scss'
import { defaults } from '../utils'

function DVStyles(props: any) {
  const { onChange, template, changedData } = props

  const [styleData, setStyleData] = useState<any>({})
  const [isBgOrGridLineColor, setIsBgOrGridLineColor] = useState('')
  const [showColorPicker, setShowColorPicker] = useState(false)
  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )

  useEffect(() => {
    const preferenceData =
      dvGlobalData?.DataVisualization?.[template]?.['styles'] ??
      defaults?.[template]?.['styles']
    setStyleData((pre) => {
      return {
        ...pre,
        ...preferenceData,
        ...changedData,
      }
    })
  }, [dvGlobalData, changedData])

  const handleStyleDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...styleData,
      [key]: value,
    }

    setStyleData(dataToUpdate)

    const changedDataToUpdate = {
      ...changedData,
      [key]: value,
    }
    onChange({ ...changedDataToUpdate })
  }

  return (
    <Tabs.Panel id='dv-styles' className='tabPanelDiv'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          gap: '30px',
        }}
      >
        <Box
          style={{
            width: '50%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'start',
            alignItems: 'start',
          }}
        >
          <FormControl id='dv-pri-height' className='w-100 fieldsFormControl'>
            <FormControl.Label>Chart height</FormControl.Label>
            <TextInput.Group
              style={{
                width: '100%',
              }}
            >
              <TextInput
                aria-label='dv-height-input'
                id='dv-height-input'
                placeholder='Height'
                type='number'
                value={styleData.chartHeight}
                onChange={(e) =>
                  handleStyleDataChange('chartHeight', e.target.value)
                }
              />
              <IconButton
                variant='secondary'
                aria-label='px'
                icon={<></>}
                style={{
                  cursor: 'default',
                }}
              >
                px
              </IconButton>
            </TextInput.Group>
          </FormControl>

          <FormControl id='dv-border-type' className='w-100 fieldsFormControl'>
            <FormControl.Label> Border style</FormControl.Label>
            <Select
              id='borderStyle-controlled'
              name='borderStyle-controlled'
              value={styleData.borderType || 'None'}
              onChange={(e) =>
                handleStyleDataChange('borderType', e.target.value)
              }
              style={{
                width: '100%',
              }}
            >
              <Select.Option value='' isDisabled>
                Border style
              </Select.Option>
              {['None', 'solid', 'dotted', 'dashed'].map((type) => (
                <Select.Option value={type}>{type}</Select.Option>
              ))}
            </Select>
          </FormControl>
          <FormControl id='dv-border-width' className='w-100 fieldsFormControl'>
            <FormControl.Label> Border stroke</FormControl.Label>
            <TextInput.Group
              style={{
                width: '100%',
              }}
            >
              <TextInput
                aria-label='dv-border-width-input'
                id='dv-border-width-input'
                placeholder='Border Width'
                type='number'
                value={styleData.borderWidth}
                onChange={(e) =>
                  handleStyleDataChange('borderWidth', e.target.value)
                }
              />
              <IconButton
                variant='secondary'
                aria-label='px'
                icon={<></>}
                style={{
                  cursor: 'default',
                }}
              >
                px
              </IconButton>
            </TextInput.Group>
          </FormControl>
        </Box>
        <Box
          style={{
            width: '50%',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
            alignItems: 'start',
            gap: '10px',
          }}
        >
          <FormControl className='fieldsFormControl' id='dv-chart-BGColor' style={{ display: 'flex', alignItems: 'center' }}>
            <ColorsInputV2
              heading='chart background'
              onClose={() => {
                setIsBgOrGridLineColor('')
              }}
              onOpen={() => {
                setIsBgOrGridLineColor('graphBgColor')
              }}
              activeColor={styleData.graphBgColor}
              onChange={(v: any) => {

                const color = getColorStyles(v).background
                handleStyleDataChange('graphBgColor', color)

          // setShowColorPicker(false)
              }}
              pickerType='Colorama'
              // pickerType='Button'
              disabled={{
                // Accents: true,
                // Primaries: true,
                // Gradients: true,
                // Neutrals: true,
                // Secondaries: true,
              }}
              pickerdisabled={isBgOrGridLineColor && isBgOrGridLineColor !== 'graphBgColor'}
            />
            <p className="css-1k16bbs">Background colour</p>
          </FormControl>
          <FormControl id='dv-border-color' className='w-100 fieldsFormControl' style={{ display: 'flex', alignItems: 'center' }}>
              <ColorsInputV2
                  heading='chart border'
                  onClose={()=>{
                    setIsBgOrGridLineColor('')
                  }}
                  onOpen={()=>{
                    setIsBgOrGridLineColor('borderColor')
                  }}
                  activeColor={styleData.borderColor}
                  onChange={(v: any) => {
                    
                    const color = getColorStyles(v).background
                    handleStyleDataChange('borderColor', color)
                    
                    // setShowColorPicker(false)
                  }}
                  pickerType='Colorama'
                  // pickerType='Button'
                  disabled={{
                    // Accents: true,
                    // Primaries: true,
                    Gradients: true,
                    // Neutrals: true,
                    // Secondaries: true,
                  }}
                  pickerdisabled={isBgOrGridLineColor && isBgOrGridLineColor !== 'borderColor'}
                />
            <p className="css-1k16bbs">Border colour</p>
          </FormControl>
          <FormControl
            id='dv-border-shadow'
            className='w-100 fieldsFormControl'
            style={{ paddingLeft: '16px' }}
          >
            <div className='SwithWithTooltip'>
              <Checkbox
                name='dv-border-shadow-Checkbox'
                id='dv-border-shadow-Checkbox'
                onChange={() =>
                  handleStyleDataChange(
                    'isBorderShadow',
                    !styleData.isBorderShadow
                  )
                }
                className='switchRoot'
                isChecked={styleData.isBorderShadow}
              >
                Enabled chart shadow
              </Checkbox>
            </div>
          </FormControl>
        </Box>
      </Box>
    </Tabs.Panel>
  )
}

export default DVStyles
