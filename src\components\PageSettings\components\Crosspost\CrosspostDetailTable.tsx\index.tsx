import { Box, Skeleton, Table } from '@contentful/f36-components'
import React, { useEffect } from 'react'
import { getDomainData, getDomainPreview } from '../../../../Crosspost/helpers'
import {
  Domains,
  getDomainFullName,
  getDomainShortName,
  getDomainWebsiteName,
  getLocaleFullName,
} from '../../../../Crosspost/utils'
import { fetchEntryDetails } from '../../../../Dashboard/Notifications/utils'

function CrosspostDetailTable({
  jsonData,
  isSummaryView = false,
}: {
  jsonData: any
  isSummaryView?: boolean
}) {
  const allowedDomains = jsonData?.allowedDomains?.map((domain: Domains) => {
    return getDomainFullName(domain)
  })

  const slugCell = ({
    slug,
    domain,
    locale,
    data,
  }: {
    slug: any
    domain: string
    locale: string
    data: any
  }) => {
    if (isSummaryView) {
      return <p>{slug?.[locale] || '404'}</p>
    } else {
      return (
        <a
          href={getDomainPreview(
            getDomainShortName(domain),
            data?.slug?.[locale],
            locale
          )}
          target='_blank'
          rel='noreferrer'
          className='text-[#0028D7]'
        >
          {slug?.[locale] || '404'}
        </a>
      )
    }
  }

  return (
    <Table>
      <Table.Head>
        <Table.Row>
          <Table.Cell>Title</Table.Cell>
          <Table.Cell>Website</Table.Cell>
          <Table.Cell>Language</Table.Cell>
          <Table.Cell>CTA</Table.Cell>
        </Table.Row>
      </Table.Head>
      <Table.Body>
        {Object.keys(jsonData).map((domain, index) => {
          if (!getDomainWebsiteName(domain)) return null
          if (!allowedDomains.includes(domain)) return null
          const data = jsonData?.[domain]

          const { slug, primaryCTA } = data

          const langs = Object.keys(slug)

          const website = getDomainData(domain, 'domainName')

          return (
            <Table.Row>
              <Table.Cell>
                <Box className='flex flex-col items-start justify-start gap-3'>
                  {langs.map((locale) =>
                    slugCell({ slug, domain, locale, data })
                  )}
                </Box>
              </Table.Cell>
              <Table.Cell>{website}</Table.Cell>
              <Table.Cell>
                <Box className='flex flex-col items-start justify-start gap-3'>
                  {langs.map((locale) => {
                    return <p>{getLocaleFullName(locale)}</p>
                  })}
                </Box>
              </Table.Cell>
              <Table.Cell>
                {primaryCTA ? <PrimaryCTACellById id={primaryCTA} /> : 'N/A'}
              </Table.Cell>
            </Table.Row>
          )
        })}
      </Table.Body>
    </Table>
  )
}

export default CrosspostDetailTable

const PrimaryCTACellById = ({ id }: { id: string }) => {
  const [cardData, setCardData] = React.useState<any>({})

  const [isLoading, setIsLoading] = React.useState(false)

  const handleOnEdit = () => {
    let url = cardData.sys.urn.split(':::content:')[1]
    const final = 'https://app.contentful.com/' + url

    window.open(final, '_blank')
  }

  useEffect(() => {
    const fetchDataById = async (id: string) => {
      setIsLoading(true)
      const detailedEntry = await fetchEntryDetails(id)

      if (detailedEntry) setCardData(detailedEntry)
      setIsLoading(false)
    }
    if (id) {
      fetchDataById(id)
    }
  }, [id])

  return (
    <Box
      onClick={handleOnEdit}
      className='text-[#0028D7] max-w-[300px] max-h-[40px] truncate text-ellipsis cursor-pointer hover:text-wrap transition-all duration-200'
    >
      {isLoading ? (
        <Skeleton.Container>
          <Skeleton.BodyText numberOfLines={1} width={300} />
        </Skeleton.Container>
      ) : (
        cardData?.fields?.internalName?.['en-CA']
      )}
    </Box>
  )
}
