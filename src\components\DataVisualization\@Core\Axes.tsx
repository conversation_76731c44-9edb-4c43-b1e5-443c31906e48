import {
  Box,
  Checkbox,
  FormControl,
  Tabs,
  TextInput
} from '@contentful/f36-components'
import { Flex } from 'antd'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { RootState } from '../../../redux/store'
import '../index.scss'
import { defaults, mergeById } from '../utils'
import AxisFields from './AxesFields'

function DVAxes(props: any) {
  const { onChange, template, changedData, isGlobal } = props

  const [axesData, setAxesData] = useState<any>({})
  const [isAxesLineColor, setIsAxesLineColor] = useState('')

  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )

  useEffect(() => {
    const preferenceData =
      dvGlobalData?.DataVisualization?.[template]?.['axes'] ??
      defaults?.[template]?.['axes']
    setAxesData((pre) => {
      return {
        ...pre,
        ...preferenceData,
        ...changedData,
        'x-axis': mergeById(
          preferenceData?.['x-axis'] || [],
          changedData?.['x-axis'] || []
        ),
        'y-axis': mergeById(
          preferenceData?.['y-axis'] || [],
          changedData?.['y-axis'] || []
        ),
        showAxes: true,
      }
    })
  }, [dvGlobalData, changedData])

  const handleAFieldsUpdate = (key: string, value: any, id?: string) => {
    let dataToUpdate

    if (key === 'x-axis' || key === 'y-axis') {
      const updatedArray = [...(axesData?.[key] || [])]
      const index = updatedArray.findIndex((item) => item.id === id)

      if (index > -1) {
        // If index exists, merge the existing object at index with the new value
        updatedArray[index] = {
          ...updatedArray[index], // Merge existing object at index
          ...value, // Overwrite with new values
        }
      } else {
        // If index doesn't exist (-1), add a new object to the array
        updatedArray.push({
          id: id,
          ...value, // Add new value as a new object
        })
      }

      dataToUpdate = {
        ...axesData,
        [key]: updatedArray,
      }
    } else if (key === 'x-axisCount' || key === 'y-axisCount') {
      // Update the count directly when the user changes the axis count
      const axisKey = key === 'x-axisCount' ? 'x-axis' : 'y-axis'
      const axisCount = value < 1 ? 1 : value

      let updatedAxisArray = [...(axesData?.[axisKey] || [])]
      if (updatedAxisArray?.length < axisCount) {
        for (let i = updatedAxisArray?.length; i < axisCount; i++) {
          updatedAxisArray.push({ id: `${axisKey}-${i}`})
        }
      } else if (updatedAxisArray?.length > axisCount) {
        updatedAxisArray = updatedAxisArray?.slice(0, axisCount)
      }
      dataToUpdate = {
        ...axesData,
        [key]: axisCount,
        [axisKey]: updatedAxisArray, // Update the axis array based on the new count
      }
    } else {
      dataToUpdate = {
        ...axesData,
        [key]: value,
      }
    }

    setAxesData(dataToUpdate)
    onChange({ ...dataToUpdate })
  }

  const xAxisCount = parseInt(axesData?.['x-axisCount'] || '1')
  const yAxisCount = parseInt(axesData?.['y-axisCount'] || '1')

  return (
    <Tabs.Panel id='dv-axes' className='tabPanelDiv'>
      {template === 'Pie chart' ||
      template === 'Doughnut chart' ||
      template === 'Treemap chart' ? (
        <p style={{ textAlign: 'center', marginTop: '40px' }}>
          Axes do not applied for {template}.{' '}
        </p>
      ) : (
        <>
            {/*    <FormControl
            className='fieldsFormControl'
            id='pri-dv-axes'
            style={{
              paddingLeft: '0rem',
              marginBottom: '25px',
            }}
          >
            <div className='SwithWithTooltip'>
              <Checkbox
                name='show dv axes'
                id='show-dv-pri-axes'
                onChange={() =>
                  handleAFieldsUpdate('showAxes', !axesData.showAxes)
                }
                className='switchRoot'
                isChecked={axesData?.showAxes}
              >
                Enable Axes
              </Checkbox>
              <Tooltip
                placement='top'
                id='dv-pri-show-axes-tooltip'
                content='Toggle to show or hide the axes.'
              >
                <Info />
              </Tooltip>
            </div>
          </FormControl> */}
          {axesData?.showAxes && (
            <>
                {template !== 'Candlestick chart' &&
                xAxisCount === 1 &&
                yAxisCount === 1 && (
                  <FormControl
                    className='fieldsFormControl'
                    id='pri-dv-reverse-axis-type'
                    style={{
                      paddingLeft: '0rem',
                    }}
                  >
                    <div className='SwithWithTooltip'>
                      <Checkbox
                        name='show dv reverse x-axis direction'
                        id='show-dv-pri-reverse-x-axis-direction'
                        onChange={() =>
                          handleAFieldsUpdate(
                            'reverseAxisType',
                            !axesData?.reverseAxisType
                          )
                        }
                        className='switchRoot'
                        isChecked={axesData?.reverseAxisType}
                      >
                        Swap axis
                      </Checkbox>
                    </div>
                  </FormControl>
                  )} 
              <Box
                style={{
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'start',
                  alignItems: 'start',
                  paddingTop: '0.5rem',
                    gap: '50px',
                }}
              >
                <Box
                  style={{
                      width: '50%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'start',
                    alignItems: 'start',
                      paddingTop: '0.5rem',
                  }}
                >
                  {/* Number of X Axes */}
                    {(template !== 'BarStackNormalization chart' && template !== 'BarRace chart') && <FormControl
                    id='pri-dv-number-of-x-axis'
                    className='w-100 fieldsFormControl'
                    >
                      <FormControl.Label>Number of X-axis</FormControl.Label>
                    <TextInput
                      type='number'
                        placeholder='X-axis count'
                      value={axesData?.['x-axisCount']}
                      onChange={(e) =>
                        handleAFieldsUpdate('x-axisCount', e.target.value)
                      }
                      min={1}
                      onFocus={(e) => e.target.blur()}
                    />
                    </FormControl>}
                  {/* X-Axis fields */}
                    <Flex gap={30} wrap={'wrap'} style={{ width: '100%' }}>
                  {Array.from({ length: xAxisCount }).map((_, i) => {
                    const updatedXArray = [...(axesData?.['x-axis'] || [])]
                    const index = updatedXArray.findIndex(
                      (item) => item?.id === `x-axis-${i}`
                    )
                    return (
                      <div key={`${i}x-axisFields`} className={`  ${isGlobal && 'w-100-imp'} axisDiv`}>
                        <AxisFields
                          isAxesLineColor={isAxesLineColor}
                          setIsAxesLineColor={setIsAxesLineColor}
                          index={i}
                          axis={'x'}
                          axisFieldData={updatedXArray?.[index]}
                          axesCount={axesData?.['x-axisCount']}
                          handleAFieldsUpdate={(data: any) =>
                            handleAFieldsUpdate('x-axis', data, `x-axis-${i}`)
                          }
                          template={template}
                        />
                      </div>
                    )
                  })}
                    </Flex>
                </Box>
                <Box
                  style={{
                      width: '50%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'start',
                    alignItems: 'start',
                      paddingTop: '0.5rem',
                  }}
                >
                  {' '}
                    {(template !== 'BarStackNormalization chart' && template !== 'BarRace chart') && <FormControl
                    id='pri-dv-number-of-y-axis'
                    className='w-100 fieldsFormControl'
                    >
                      <FormControl.Label>Number of Y-axis</FormControl.Label>
                    <TextInput
                      type='number'
                        placeholder='Y-axis count'
                      value={axesData?.['y-axisCount']}
                      onChange={(e) =>
                        handleAFieldsUpdate('y-axisCount', e.target.value)
                      }
                      min={1}
                      onFocus={(e) => e.target.blur()}
                    />
                    </FormControl>}
                  {/* Y-Axis fields */}
                    <Flex gap={30} wrap={'wrap'} style={{ width: '100%' }}>
                  {Array.from({ length: yAxisCount }).map((_, i) => {
                    const updatedXArray = [...(axesData?.['y-axis'] || [])]
                    const index = updatedXArray?.findIndex(
                      (item) => item?.id === `y-axis-${i}`
                    )
                    return (
                      <div key={`${i}y-axisFields`} className={`  ${isGlobal && 'w-100-imp'} axisDiv`}>
                        <AxisFields
                          isAxesLineColor={isAxesLineColor}
                          setIsAxesLineColor={setIsAxesLineColor}
                          index={i}
                          axis={'y'}
                          axisFieldData={updatedXArray[index]}
                          axesCount={axesData?.['y-axisCount']}
                          handleAFieldsUpdate={(data: any) =>
                            handleAFieldsUpdate('y-axis', data, `y-axis-${i}`)
                          }
                          template={template}
                        />
                      </div>
                    )
                  })}
                    </Flex>
                </Box>
              </Box>
            </>
          )}
        </>
      )}
    </Tabs.Panel>
  )
}

export default DVAxes
