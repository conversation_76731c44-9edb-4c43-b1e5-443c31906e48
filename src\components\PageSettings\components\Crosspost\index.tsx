import { <PERSON>, <PERSON><PERSON> } from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { GlobalContext } from '../../../../contexts/globalContext'
import { RootState } from '../../../../redux/store'
import {
  Domains,
  getDomainFullName,
  getDomainPreview,
  getDomainShortName,
  getDomainWebsiteName,
  getLocaleFullName,
} from '../../../Crosspost/utils'

/**
 * CrossPost component displays information about the crossposting status of a page.
 * It uses global JSON data to determine if crossposting data is available and renders
 * a list of preview URLs for each domain and locale if available.
 * 
 * - Fetches crosspost data from the Redux store and updates the local state.
 * - Displays a button to open/close the crossposting modal.
 * - Shows a message if no crossposting data is available.
 * 
 * 
 * Displays:
 * - Preview URLs for crossposted domains
 * - Button to start/edit crossposting
 * 
 * The component conditionally renders different content based on the availability 
 * of crossposting data.
 */

function CrossPost() {
  const { isCrossPostingModalOpen, setIsCrossPostingModalOpen } =
    useContext(GlobalContext)

  const [globalJson, setGlobalJson] = useState<any>({})

  const isCrossPostingDataAvailable = Object.keys(globalJson).length > 0

  const entryCrossPostData = useSelector(
    (state: RootState) => state.pageData.crossPostData
  )

  const allowedDomains = globalJson?.allowedDomains?.map((domain: Domains) => {
    return getDomainFullName(domain)
  })

  useEffect(() => {
    entryCrossPostData &&
      Object.keys(entryCrossPostData).length > 0 &&
      setGlobalJson(entryCrossPostData)
  }, [entryCrossPostData])

  return (
    <div
      className={`flex flex-col w-full justify-start pt-3 gap-5 items-start`}
    >
      <Box
        className={`flex w-full  ${
          isCrossPostingDataAvailable
            ? 'justify-between items-center'
            : 'flex-col gap-5 justify-start items-start'
        }`}
      >
        {isCrossPostingDataAvailable ? (
          <div>
            <h6
              style={{
                marginTop: 0,
                marginBottom: 4,
              }}
            >
              Preview URLs
            </h6>

            <p className='text-sm'>
              This Page has been crossposted to the undermentioned Preview
              websites with the following slugs .
            </p>
          </div>
        ) : (
          <div>
            <h6
              style={{
                marginTop: 0,
                marginBottom: 4,
              }}
            >
              No Data Available
            </h6>

            <p className='text-sm'>This Page hasn't been crossposted yet.</p>
          </div>
        )}
        <Button
          variant='primary'
          size='medium'
          onClick={() => setIsCrossPostingModalOpen(!isCrossPostingModalOpen)}
        >
          {isCrossPostingDataAvailable ? 'Edit' : 'Start'} Crossposting
        </Button>
      </Box>
      {isCrossPostingDataAvailable && (
        <div className='flex flex-col gap-5 '>
          {Object.keys(globalJson).map((domain, index) => {
            if (!getDomainWebsiteName(domain)) return null
            if (!allowedDomains.includes(domain)) return null
            const data = globalJson[domain]

            return (
              <div className='flex flex-col' key={index}>
                <div className='flex gap-3 w-full justify-start items-start flex-col'>
                  <span className='flex justify-between items-center text-black w-[150px] font-medium text-base'>
                    <p>{getDomainWebsiteName(domain)}</p>
                  </span>
                  <div className='flex flex-col gap-3'>
                    {Object.keys(data).map((locale, slugIndex) => {
                      return (
                        <div
                          key={slugIndex}
                          className='flex justify-start items-start'
                        >
                          <span className='flex items-center justify-between w-[80px] font-medium pr-1'>
                            <p className='font-medium text-[#667082] w-[120px] text-xs'>
                              {getLocaleFullName(locale.split('-')[0])} (
                              {locale})
                            </p>{' '}
                            <p> :</p>
                          </span>

                          <a
                            className='text-sm  underline underline-offset-4'
                            href={getDomainPreview(
                              getDomainShortName(domain),
                              data[locale],
                              locale
                            )}
                            target='_blank'
                            rel='noreferrer'
                          >
                            {data[locale] || '404'}
                          </a>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

export default CrossPost
