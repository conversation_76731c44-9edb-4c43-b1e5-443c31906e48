import * as React from 'react'

import { Paragraph } from '@contentful/f36-components'

/**
 * Displays a message when a user doesn't have permission to view content in a
 * reference field or the field is not correctly configured.
 *
 * @returns A `Paragraph` component with the message.
 */
export function NoLinkPermissionsInfo() {
  return (
    <Paragraph>
      You don&apos;t have permission to view this content or this field is not
      correctly configured. Contact your administrator for help.
    </Paragraph>
  )
}
