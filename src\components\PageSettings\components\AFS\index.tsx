import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Button } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { getLocaleFullName } from '../../../Crosspost/utils'
import InputFieldsRenderer from '../../../InputFields/InputFieldsRenderer'
interface Props {
  sdk: EditorAppSDK
  locale: string[]
  domain: string
}
function AFS(props: Props) {
  const { sdk, locale } = props

  const sdkFields = sdk.entry.fields

  const [aFSDescriptionByLocale, setAFSDescriptionByLocale] = useState<any>({})

  /**
   * Copy AFS description content from one field to another.
   *
   * This function will loop through all the locales and copy the content from
   * the source field to the destination field. It will also set the
   * configurations field to mark the page as not translated.
   *
   * @param source The field id of the source field.
   * @param destination The field id of the destination field.
   */
  const handleCopyFrom = (source: string, destination: string) => {
    let x = { ...aFSDescriptionByLocale }

    locale.forEach(async (locale) => {
      const value = sdk.entry.fields?.[source]?.getForLocale(locale)?.getValue()
      const oldValue = aFSDescriptionByLocale?.[locale]

      if (oldValue === value) return

      sdk.entry.fields[destination].getForLocale(locale).setValue(value)
      sdk.entry.save()
      x = {
        ...x,
        [locale]: value,
      }
    })

    let pageConfigurations = sdkFields?.['configurations']?.getValue()

    pageConfigurations = {
      ...pageConfigurations,
      isTranslated: false,
    }

    sdkFields?.['configurations']?.setValue(pageConfigurations)

    setAFSDescriptionByLocale(x)

    sdk.entry.save()
  }

  useEffect(() => {
    if (sdk) {
      // func to update the local state with the latest data from the entry on load
      const updateLocaleData = () => {
        const translatedValues = locale
          .map((item) => ({
            [item]: sdk.entry.fields['afsDescription']
              .getForLocale(item)
              .getValue(),
          }))
          .reduce((acc, val) => ({ ...acc, ...val }), {})

        setAFSDescriptionByLocale(translatedValues)
      }

      // Initial fetch of data
      updateLocaleData()

      // Set up onValueChanged for each locale
      const unsubscribeFunctions = locale.map((item) =>
        sdk.entry.fields['afsDescription']
          .getForLocale(item)
          .onValueChanged(() => {
            updateLocaleData()
          })
      )

      // Cleanup all subscriptions on unmount
      return () => {
        unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
      }
    }
  }, [sdk, locale])

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-5 p-3 flex-col'>
      <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <InputFieldsRenderer
            currentLocale={locale}
            fieldId='afsCardTitle'
            isPageSettingsFields={true}
            label={`AFS Title (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the AFS title displayed on users browser.`}
            isRequired={
              locale === 'en-CA' && sdkFields?.['afsCardTitle']?.required
            }
            key={'afsCardTitle-' + locale}
          />
        ))}
      </Box>
      <Box className='flex gap-3'>
        <Button
          variant='primary'
          size='small'
          onClick={() => handleCopyFrom('seoTitle', 'afsCardTitle')}
        >
          Copy from SEO Title
        </Button>
      </Box>
      <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <InputFieldsRenderer
            currentLocale={locale}
            fieldId='afsDescription'
            isPageSettingsFields={true}
            label={`AFS Description (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the AFS Card title displayed on users browser.`}
            isRequired={
              locale === 'en-CA' && sdkFields?.['afsDescription']?.required
            }
            key={'afsDescription-' + locale}
          />
        ))}
      </Box>
      <Box className='flex gap-3'>
        <Button
          variant='primary'
          size='small'
          onClick={() => handleCopyFrom('seoDescription', 'afsDescription')}
        >
          Copy from SEO Description
        </Button>
      </Box>
    </Box>
  )
}

export default AFS
