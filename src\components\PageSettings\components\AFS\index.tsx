import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Button, TextInput } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { getLocaleFullName } from '../../../Crosspost/utils'
import {
  Char<PERSON>ounter,
  CharValidation,
  ConstraintsUtils,
} from '../../../InputFields/shared'
import { SingleLineEditor } from '../../../InputFields/SingleLine'
import * as styles from '../../../InputFields/SingleLine/styles'
import FormControlComp from '../../../Shared/FormControlComp'
interface Props {
  sdk: EditorAppSDK
  locale: string[]
  domain: string
}
function AFS(props: Props) {
  const { sdk, locale } = props

  const sdkFields = sdk.entry.fields

  const [aFSDescriptionByLocale, setAFSDescriptionByLocale] = useState<any>({})

  /**
   * Update the isTranslated field of the configurations field
   * whenever a field value changes and is different from the
   * existing value.
   *
   * @param {any} value - The new value of the field
   * @param {string} fieldId - The ID of the field
   * @param {string} locale - The locale of the field
   */
  const handleFieldValueChangeToUpdateConfigurations = (
    value: any,
    fieldId: string,
    locale: string
  ) => {
    const fieldValue = sdkFields?.[fieldId]?.getForLocale(locale)?.getValue()

    const isValueSame = JSON.stringify(fieldValue) === JSON.stringify(value)

    if (isValueSame) return

    let pageConfigurations = sdkFields?.['configurations']?.getValue()

    pageConfigurations = {
      ...pageConfigurations,
      isTranslated: false,
    }

    sdkFields?.['configurations']?.setValue(pageConfigurations)

    sdk.entry.save()
  }

  /**
   * Copy the value of the field with the given ID from the
   * master locale to all locales. If the value is the same
   * as the existing value, do not update the field.
   *
   * @param {string} field - The ID of the field to copy
   */
  const handleCopyFrom = (field: string) => {
    let iterableObj = { ...aFSDescriptionByLocale }

    locale.forEach(async (locale) => {
      const value = sdk.entry.fields?.[field]?.getForLocale(locale)?.getValue()
      const oldValue = aFSDescriptionByLocale?.[locale]

      if (oldValue === value) return

      sdk.entry.fields['afsDescription'].getForLocale(locale).setValue(value)
      sdk.entry.save()
      iterableObj = {
        ...iterableObj,
        [locale]: value,
      }
    })

    let pageConfigurations = sdkFields?.['configurations']?.getValue()

    pageConfigurations = {
      ...pageConfigurations,
      isTranslated: false,
    }

    sdkFields?.['configurations']?.setValue(pageConfigurations)

    setAFSDescriptionByLocale(iterableObj)

    sdk.entry.save()
  }

  // Fetch the afsDescription data for each locale and set the state
  useEffect(() => {
    if (sdk) {
      const updateLocaleData = () => {
        const translatedValues = locale
          .map((item) => ({
            [item]: sdk.entry.fields['afsDescription']
              .getForLocale(item)
              .getValue(),
          }))
          .reduce((acc, val) => ({ ...acc, ...val }), {})

        setAFSDescriptionByLocale(translatedValues)
      }

      // Initial fetch of data
      updateLocaleData()

      // SDK's built-in method to listen for change in the field value
      const unsubscribeFunctions = locale.map((item) =>
        sdk.entry.fields['afsDescription']
          .getForLocale(item)
          .onValueChanged(() => {
            updateLocaleData()
          })
      )

      // Cleanup all subscriptions on unmount
      return () => {
        unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
      }
    }
  }, [sdk, locale])

  /**
   * Handle debounced change of AFS description field.
   * If the new value is the same as the existing value, do nothing.
   * Otherwise, update the field's value, mark configurations as not translated,
   * and set the state to the new value.
   * @param {string} value - New value of the field
   * @param {string} locale - Locale of the field
   */
  const handleDebouncedChange = async (value: string, locale: string) => {
    const oldValue = aFSDescriptionByLocale?.[locale]

    if (oldValue === value) return

    sdk.entry.fields['afsDescription'].getForLocale(locale).setValue(value)

    handleFieldValueChangeToUpdateConfigurations(
      value,
      'afsDescription',
      locale
    )

    setAFSDescriptionByLocale({
      ...aFSDescriptionByLocale,
      [locale]: value,
    })

    sdk.entry.save()
  }

  const constraints = ConstraintsUtils.fromFieldValidations(
    sdk.entry.fields['afsDescription'].validations,
    'Symbol'
  )

  const checkConstraint = ConstraintsUtils.makeChecker(constraints)

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-5 p-3 flex-col'>
      <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <FormControlComp
            label={`AFS Title (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the AFS title displayed on users browser.`}
            key={locale}
            isRequired={
              locale === 'en-CA' && sdkFields?.['afsCardTitle']?.required
            }
          >
            <SingleLineEditor
              locales={sdk.locales}
              field={sdkFields?.['afsCardTitle']?.getForLocale(locale)}
              onValueChange={handleFieldValueChangeToUpdateConfigurations}
            />
          </FormControlComp>
        ))}
      </Box>

      <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <FormControlComp
            label={`AFS Description (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the AFS Card title displayed on users browser.`}
            key={locale}
            isRequired={
              locale === 'en-CA' && sdkFields?.['afsDescription']?.required
            }
          >
            <TextInput
              value={aFSDescriptionByLocale?.[locale] || ''}
              onChange={(e) => handleDebouncedChange(e.target.value, locale)}
            />
            <div className={styles.validationRow}>
              <CharCounter
                value={aFSDescriptionByLocale?.[locale] || ''}
                checkConstraint={checkConstraint}
              />
              <CharValidation constraints={constraints} />
            </div>
          </FormControlComp>
        ))}
      </Box>
      <Box className='flex gap-3'>
        <Button
          variant='primary'
          size='small'
          onClick={() => handleCopyFrom('seoDescription')}
        >
          Copy from SEO Description
        </Button>
      </Box>
    </Box>
  )
}

export default AFS
