import React, { useEffect, useState } from 'react'
import { BsCheckCircle, BsGlobe2, BsInfoCircle } from 'react-icons/bs'
import { Button, notification, Popconfirm, Tooltip } from '../atoms'
import { domaintoVercelAPI } from '../Crosspost/utils'
// const { Countdown } = Statistic

// const time = 600
const BuildButton = ({
  builddisabled,
  setBuildDisabled,
  selectedDomain,
  ActiveDomainLink,
}: {
  setBuildDisabled: Function
  builddisabled: boolean
  selectedDomain: string
  ActiveDomainLink: string
}) => {
  const [isButtonDisabled, setIsButtonDisabled] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  // const [progress, setProgress] = useState(0)
  // const [deadline, setDeadline] = useState(Date.now())
  // const [isPropgress, SetisProgress] = useState(false)

  useEffect(() => {
    setIsButtonDisabled(builddisabled)
  }, [builddisabled])

  const startProgress = async () => {
    setIsLoading(true)

    // console.log({f1 : domaintoVercelAPI?.[process.env.REACT_APP_BRANCH_FRONT],f2 :domaintoVercelAPI?.[process.env.REACT_APP_BRANCH_FRONT ?? 'dev']})
    if (
      // domaintoVercelAPI?.[process.env.REACT_APP_BRANCH_FRONT] &&
      domaintoVercelAPI?.[process.env.REACT_APP_BRANCH_FRONT ?? 'dev'] &&
      domaintoVercelAPI?.[process.env.REACT_APP_BRANCH_FRONT ?? 'dev']?.[
        selectedDomain
      ]
    ) {
      const res = await fetch(
        `${
          domaintoVercelAPI[process.env.REACT_APP_BRANCH_FRONT ?? 'dev']?.[
            selectedDomain
          ]
        }`
      )
      notification.success({
        icon: <BsCheckCircle size={'20'} />,

        message: 'Your chnages are being pushed to live.',
        // description: (
        //   <>
        //     <a
        //       href={ActiveDomainLink}
        //       target='_blank'
        //       rel='noreferrer'
        //       className=''
        //     >
        //       <Tooltip title='Check the file on live website.'>
        //         Please verify the changes
        //       </Tooltip>
        //     </a>{' '}
        //     &nbsp; that you made are live.
        //   </>
        // ),
      })
      setIsLoading(false)
      setBuildDisabled(true)
      // // setProgress(0)
      // setDeadline(Date.now() + 100 * time)
      // SetisProgress(true)
      // // const interval = setInterval(() => {
      // //   setProgress((prev) => {
      // //     if (prev >= 100) {
      // //       clearInterval(interval)
      // // setIsLoading(false)
      // // SetisProgress(false)
      // // setBuildDisabled(true)
      // // notification.success({
      // //   icon: <BsCheckCircle size={'20'} />,

      // //   message: 'Process complete',
      // //   description: (
      // //     <>
      // //       <a
      // //         href={ActiveDomainLink}
      // //         target='_blank'
      // //         rel='noreferrer'
      // //         className=''
      // //       >
      // //         <Tooltip title='Check the file on live website.'>
      // //           Please verify the changes
      // //         </Tooltip>
      // //       </a>{' '}
      // //       &nbsp; that you made are live.
      // //     </>
      // //   ),
      // // })
      // //       return 0
      // //     }
      // //     return prev + 1
      // //   })
      // // }, time)
    } else {
      // SetisProgress(false)
      setIsLoading(false)
      setBuildDisabled(true)
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Could not push changes to live.
          </p>
        ),
        description: <p style={{ lineHeight: 1.25 }}>Something went wrong.</p>,
      })
    }
  }

  const handleClick = () => {
    startProgress()
  }
  // console.log((deadline - Date.now()) / 1000, "seconds remaining", (deadline - Date.now()) / deadline, "progress");
  return (
    <div className='flex justify-center items-center gap-4'>
      <Popconfirm
        title={<b>Push your changes to live?</b>}
        //description={
        //  <p
        //  //style={{
        //  //  marginTop: '10px',
        //  //  marginBottom: '7px',
        //  //}}
        //  >
        //    Click yes to confirm.
        //  </p>
        //}
        icon={<BsGlobe2 size={20} className='mr-2  bg-ac' />}
        onConfirm={handleClick}
        okText='Yes'
        okButtonProps={{
          type: 'primary',
          className: 'bg-neutral-950',
        }}
        styles={{
          body: {
            padding: '15px',
          },
        }}
        cancelButtonProps={{
          //className: 'border-accent10-500 text-accent10-500',
          type: 'default',
        }}
        cancelText='No'
        disabled={isLoading || isButtonDisabled}
      >
        <Button
          type='primary'
          ghost
          disabled={isButtonDisabled || isLoading}
          loading={isLoading}
        >
          <BsGlobe2 /> Push to live
        </Button>
      </Popconfirm>
      <Tooltip title='Check status of the build.' placement='top'>
        <a
          href={`https://vercel.com/altus/msa-${selectedDomain}-v3w/deployments?filterBranch=${
            process.env.REACT_APP_BRANCH_FRONT?.toLowerCase() === 'staging'
              ? 'main'
              : (process.env.REACT_APP_BRANCH_FRONT ?? 'dev')
          }`}
          target='_blank'
          rel='noreferrer'
        >
          <BsInfoCircle size={'20'} />
        </a>
      </Tooltip>
    </div>
  )
}

export default BuildButton
