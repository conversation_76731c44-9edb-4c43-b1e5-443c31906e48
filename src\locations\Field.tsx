// @ts-ignore`

import React, { useEffect, useState } from 'react';
import { useSDK } from '@contentful/react-apps-toolkit';
import { FieldAppSDK } from '@contentful/app-sdk';
import fieldConfig from '../configs/fields.config.json'
import { Form, FormControl, Select } from '@contentful/f36-components';
import AppFields from '../components/InputFields';



const Field = () => {

  const [fieldIdsToDisplay, setFieldIdsToDisplay] = useState([])
  const [fieldsToDisplay, setFieldsToDisplay] = useState([])

  const sdk = useSDK<FieldAppSDK>()
  const [values, setValues] = useState(sdk.field.getValue())
  const contentType = sdk.contentType.sys.id
  const templateList = fieldConfig[contentType]
  const fields = Object.values(sdk.entry.fields);

  function handleChange(e) {
    const filteredFields = templateList.find(i => i.templateId === e.target.value)?.templateFields
    setFieldIdsToDisplay(filteredFields)
  }


  useEffect(() => {
    sdk.window.startAutoResizer()

    console.log('fields', sdk.entry.fields)
    const filteredSdkFields = fields.filter(field => fieldIdsToDisplay.includes(field.id))
    setFieldsToDisplay(filteredSdkFields)
    console.log('filterdSdkFields', filteredSdkFields)

  }, [fieldIdsToDisplay])


  return <>
    <Form>
      <FormControl>
        <FormControl.Label>Select Template</FormControl.Label>
        <Select onChange={handleChange} id="optionSelect" name="optionSelect">
          {
            templateList?.map((item: any) => (
              <Select.Option value={item.templateId}>
                {item.templateName}
              </Select.Option>))
          }
        </Select>
      </FormControl>


      {fieldsToDisplay.map(field => <>
        <h4>{field.name}</h4>
        <AppFields
          key={field.id}
          sdk={sdk}
          fieldId={field.id}
          values={values}
          setValues={setValues}
          type={field.type} />
      </>)}

      {/* <TransformFields /> */}
    </Form>
  </>
};


export default Field;
