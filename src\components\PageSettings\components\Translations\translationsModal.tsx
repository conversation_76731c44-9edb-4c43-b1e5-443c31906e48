/* eslint-disable react-hooks/rules-of-hooks */
import { EditorAppSDK, SidebarAppSDK } from '@contentful/app-sdk'
import { Box, Modal } from '@contentful/f36-components'
import React, { useContext } from 'react'
import Translations from '.'
import { GlobalContext } from '../../../../contexts/globalContext'
import { EntityProvider } from '../../../InputFields/Reference'
import './index.scss'

interface Props {
  sdk: EditorAppSDK | SidebarAppSDK
  pageDomainAllowedLocale: string[]
}

function TranslationsModal(props: Props) {
  const { sdk, pageDomainAllowedLocale } = props

  const { isTranslationModalOpen, setIsTranslationModalOpen } =
    useContext(GlobalContext)

  const handleClose = () => {
    setIsTranslationModalOpen(false)
  }

  return (
    <EntityProvider sdk={sdk}>
      <Modal
        onClose={() => handleClose()}
        isShown={isTranslationModalOpen}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        className='translationsModal'
      >
        {() => (
          <>
            <Modal.Header title='' onClose={() => handleClose()}>
              <Box className='flex items-center justify-start w-full'>
                <p className='flex w-full justify-start items-start gap-1 pl-1'>
                  <span className='text-lg font-semibold'>Translations </span>
                </p>
              </Box>
            </Modal.Header>

            <Modal.Content className='translationsModalContent'>
              <Translations
                sdk={sdk}
                pageDomainAllowedLocale={pageDomainAllowedLocale}
              />
            </Modal.Content>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default TranslationsModal
