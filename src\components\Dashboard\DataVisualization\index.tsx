import { <PERSON>, <PERSON><PERSON>, Spin<PERSON>, <PERSON><PERSON><PERSON> } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import Download from '../../../assets/icons/Download'
import { fetchGlobalConfigurationData } from '../../../globals/utils'
import {
  createNewGlobalConfig,
  updateGlobalConfigData,
} from '../../../redux/slices/dashboard/dvSlices'
import { RootState, useAppDispatch } from '../../../redux/store'
import RevertButton from '../../Buttons/RevertButton'
import SaveButton from '../../Buttons/SaveButton'
import ModalConfirm from '../../ConfirmModal'
import Preferences from '../../DataVisualization/@Core/Preferences'
import Visualizer from '../../DataVisualization/@Core/Visualizer'
import {
  defaults,
  getDataSourceUrlByTemplateName,
  templateName,
} from '../../DataVisualization/utils'
import './index.scss'

function DataVisualizationDashboard({
  selectedTemplate,
}: {
  selectedTemplate: templateName
}) {
  const [dvUpdatedData, setDVUpdatedData] = useState<any>({})

  const [isLoading, setIsLoading] = useState(false)
  const [confirm, setConfirm] = useState<boolean>(false)
  const [isRevertible, setIsRevertible] = useState<any>({})
  const [currentTab, setCurrentTab] = useState<
    'dv-axes' | 'dv-general' | 'dv-chart' | 'dv-styles' | 'dv-grid' | 'dv-mark'
  >('dv-general')
  const [close, setClose] = useState<boolean>(false)
  const [isDisabled, setIsDisabled] = useState<boolean>(false)

  const dispatch = useAppDispatch()
  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )
  const defaultsAxes = defaults?.[selectedTemplate]?.axes
  const defaultsGeneral = defaults?.[selectedTemplate]?.general
  const defaultsStyles = defaults?.[selectedTemplate]?.styles
  const defaultsChart = defaults?.[selectedTemplate]?.chart
  const defaultsGrid = defaults?.[selectedTemplate]?.grid
  const dvGlobalContentId = useSelector(
    (state: RootState) => state.dvDashboard.contentId
  )

  useEffect(() => {
    const json =
      dvGlobalData?.DataVisualization?.[selectedTemplate] ??
      defaults?.[selectedTemplate]
    setDVUpdatedData(json)

    const jsonUpdatedChart = {
      ...json?.chart,
    }
    delete jsonUpdatedChart?.asset

    setIsRevertible((pre) => {
      return {
        ...pre,
        'dv-axes': JSON.stringify(defaultsAxes) !== JSON.stringify(json?.axes),
        'dv-styles':
          JSON.stringify(defaultsStyles) !== JSON.stringify(json?.styles),
        'dv-general':
          JSON.stringify(defaultsGeneral) !== JSON.stringify(json?.general),
        'dv-grid': JSON.stringify(defaultsGrid) !== JSON.stringify(json?.grid),
        'dv-chart':
          JSON.stringify(defaultsChart) !== JSON.stringify(jsonUpdatedChart),
      }
    })
    setIsDisabled(
      dvGlobalData?.DataVisualization?.[selectedTemplate] !== undefined
    )
  }, [dvGlobalData, selectedTemplate])

  const handleSave = async () => {
    setIsLoading(true)
    const response: any = await fetchGlobalConfigurationData()
    let payload = {
      ...response?.data,
      DataVisualization: {
        ...response?.data?.DataVisualization,
        [selectedTemplate]: {
          ...response?.data?.DataVisualization?.[selectedTemplate],
          ...dvUpdatedData,
        },
      },
    }
    if (dvGlobalContentId) {
      dispatch(
        updateGlobalConfigData({ contentId: dvGlobalContentId, payload })
      )
    } else {
      dispatch(createNewGlobalConfig(payload))
    }

    setIsLoading(false)
    setConfirm(false)
    setClose(true)
  }

  const revertChanges = () => {
    let payload = {
      ...dvUpdatedData,
    }
    if (currentTab === 'dv-axes') {
      payload = {
        ...payload,
        axes: defaultsAxes,
      }
    } else if (currentTab === 'dv-general') {
      payload = {
        ...payload,
        general: defaultsGeneral,
      }
    } else if (currentTab === 'dv-styles') {
      payload = {
        ...payload,
        styles: defaultsStyles,
      }
    } else if (currentTab === 'dv-grid') {
      payload = {
        ...payload,
        grid: defaultsGrid,
      }
    } else if (currentTab === 'dv-chart') {
      payload = {
        ...payload,
        chart: {
          asset: payload?.chart?.asset,
          ...defaultsChart,
        },
      }
    }
    setDVUpdatedData(payload)
    revertFunction(payload)
    setIsDisabled(false)
  }
  const handleDataChange = (key: string, data: any) => {
    setIsLoading(true)
    const payLoad = {
      ...dvUpdatedData,
      [key]: {
        ...(dvUpdatedData?.[key] ?? {}),
        ...data,
      },
    }
    setDVUpdatedData(payLoad)
    revertFunction(payLoad)
    setIsDisabled(false)
    setIsLoading(false)
  }

  const revertFunction = (dvUpdatedData: any) => {
    let payload = {
      ...isRevertible,
    }
    if (currentTab === 'dv-axes') {
      payload = {
        ...payload,
        'dv-axes':
          JSON.stringify(defaultsAxes) !== JSON.stringify(dvUpdatedData?.axes),
      }
    } else if (currentTab === 'dv-styles') {
      payload = {
        ...payload,
        'dv-styles':
          JSON.stringify(defaultsStyles) !==
          JSON.stringify(dvUpdatedData?.styles),
      }
    } else if (currentTab === 'dv-general') {
      payload = {
        ...payload,
        'dv-general':
          JSON.stringify(defaultsGeneral) !==
          JSON.stringify(dvUpdatedData?.general),
      }
    } else if (currentTab === 'dv-grid') {
      payload = {
        ...payload,
        'dv-grid':
          JSON.stringify(defaultsGrid) !== JSON.stringify(dvUpdatedData?.grid),
      }
    } else if (currentTab === 'dv-chart') {
      const dvUpdatedChart = {
        ...dvUpdatedData?.chart,
      }
      delete dvUpdatedChart?.asset

      payload = {
        ...payload,
        'dv-chart':
          JSON.stringify(defaultsChart) !== JSON.stringify(dvUpdatedChart),
      }
    }
    setIsRevertible(payload)
  }

  return (
    <Box
      className='dashboardItemContainer'
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'start',
        width: '100%',
        height: '100%',
        flexDirection: 'column',
        gap: '10px',
      }}
    >
      {' '}
      <>
        <Box
          style={{
            display: 'flex',
            justifyContent: 'start',
            alignItems: 'start',
            width: '100%',
            height: '100%',
            flexDirection: 'column',
            gap: '10px',
            position: 'relative',
          }}
        >
          <h6
            style={{
              marginBlock: 10,
            }}
          >
            The Global preferences for {selectedTemplate}
          </h6>
          <hr
            className='endHorizontalLine'
            style={{
              width: '100%',
            }}
          />
          <Box
            style={{
              width: '100%',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'start',
              paddingTop: '0.5rem',
              gap: '5px',
            }}
          >
            <div style={{ width: '45%' }}>
              <Preferences
                changedData={dvUpdatedData}
                asset={dvUpdatedData?.chart?.asset}
                currentTab={currentTab}
                setCurrentTab={setCurrentTab}
                isGlobal={true}
                template={selectedTemplate}
                onDataChange={handleDataChange}
                setDataLoading={setIsLoading}
                dataLoading={isLoading}
              />
            </div>
            <div className='verticalLine'></div>
            <div className='previewCol'>
              {dvUpdatedData?.chart?.asset ? (
                isLoading ? (
                  <Spinner customSize={20} />
                ) : (
                  <Visualizer
                    asset={dvUpdatedData?.chart?.asset}
                    template={selectedTemplate}
                    id={selectedTemplate}
                    changedData={dvUpdatedData}
                  />
                )
              ) : (
                <div className='emptyPreview'>
                  <p>please add data source</p>
                </div>
              )}
              <Box className='box'>
                {' '}
                <Tooltip
                  placement='top'
                  id='download-tool-tip'
                  content={`Download the reference Data Source for ${selectedTemplate ?? ''} `}
                >
                  <Button
                    as='a'
                    variant='primary'
                    startIcon={<Download />}
                    size='small'
                    href={
                      selectedTemplate &&
                      getDataSourceUrlByTemplateName(selectedTemplate)
                    }
                    isDisabled={isLoading}
                  ></Button>
                </Tooltip>
                <Box>
                  {' '}
                  <h5>Download Reference Data Source</h5>
                  <p>
                    Reference data sources are pre-defined templates that are
                    formatted and populated with sample datasets to help you get
                    started quickly.
                    <br />
                    To ensure the charts display data as intended, use reference
                    data sources as the starting point to build unique data
                    source for your charts.
                  </p>{' '}
                </Box>
              </Box>
            </div>
          </Box>
        </Box>
        <hr
          className='endHorizontalLine'
          style={{
            width: '100%',
          }}
        />
        <Box
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
          }}
        >
          <RevertButton
            isDisabled={isLoading || !isRevertible?.[currentTab]}
            onClick={revertChanges}
            tooltipPlacement='top'
            helpText={
              isRevertible?.[currentTab]
                ? 'Revert to defaults setting'
                : 'You have made no changes'
            }
          ></RevertButton>
          <SaveButton
            onClick={() => setConfirm(true)}
            isDisabled={isLoading || isDisabled}
            helpText={
              isDisabled ? 'You have made no changes to save' : 'Save changes'
            }
          ></SaveButton>
        </Box>
      </>
      <ModalConfirm
        btn1Text='Do not save'
        btn2Text='Save'
        open={confirm}
        title={`The Global preferences for ${selectedTemplate}`}
        loading={isLoading}
        onConfirm={handleSave}
        children={
          <>
            <Box className='summery'>
              <p>
                Your preferences are ready to be saved.
                <br />
                You have updated the global preferences for line chart. These
                settings will be inherited by all the line charts automatically
                wherever defaults are used.
              </p>
            </Box>
          </>
        }
        handleClose={() => setConfirm(false)}
      />
      <ModalConfirm
        isNotification={true}
        btn2Text='Ok'
        open={close}
        title={`The Global preferences for ${selectedTemplate}`}
        loading={isLoading}
        onConfirm={() => {
          setClose(false)
          setIsDisabled(true)
        }}
        children={
          <>
            <Box className='summery'>
              <p>
                The Global preferences for {selectedTemplate} were saved
                successfully.
              </p>
            </Box>
          </>
        }
        handleClose={() => {
          setClose(false)
          setIsDisabled(true)
        }}
      />
    </Box>
  )
}

export default DataVisualizationDashboard
