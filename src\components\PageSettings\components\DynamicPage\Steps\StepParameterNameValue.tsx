import React from 'react'
import StepParameterName from './StepParameterName'
import { Divider } from 'antd'
import StepParameterValues from './StepParameterValues'

const StepParameterNameValue = ({
  parameterName,
  setParameterName,
  setParameterData,
  dynamicPageConfiguration,
  isActiveDynamicPage,
  parameterData,
  references,
}: {
  parameterName: string
  isActiveDynamicPage: boolean
  setParameterName: (val: string) => void
  dynamicPageConfiguration: ParameterDataType
  setParameterData: SetParameterDataType
  parameterData: ParameterDataType
  references: string[]
}) => {
  return (
    <>
      <Divider></Divider>
      <StepParameterName
        parameterName={parameterName}
        setParameterName={setParameterName}
        setParameterData={setParameterData}
        dynamicPageConfiguration={dynamicPageConfiguration}
        isActiveDynamicPage={isActiveDynamicPage}
      />
      <Divider />
      <StepParameterValues
        parameterName={parameterName}
        parameterData={parameterData}
        setParameterData={setParameterData}
        references={references}
      />
    </>
  )
}

export default StepParameterNameValue
