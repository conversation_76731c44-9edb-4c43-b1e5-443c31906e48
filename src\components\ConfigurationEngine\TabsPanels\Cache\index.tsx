import {
  Button,
  FormControl,
  Notification,
  Select,
  TextInput,
} from '@contentful/f36-components'
import React from 'react'
import { getBranchDomainByTag } from '../../../../globals/utils'
import { domainsConfig } from '../../../Crosspost/utils'
import UpcomingFeatComp from '../../Components/UpcomingFeatComp'

interface CacheControlProps {
  selectedDomain: string
  // handleChange: (key: string, value: string) => void
  // value: unknown
}

function CacheControl(props: CacheControlProps) {
  const { handleChange, value } = props

  const [error, setError] = React.useState<string | null>(null)
  const [loading, setLoading] = React.useState<boolean>(false)
  const [success, setSuccess] = React.useState<string | null>(null)

  const [selectedComponent, setSelectedComponent] =
    React.useState<string>('header')

  const domainData = domainsConfig.find((d) => d.key === props.selectedDomain)
  const domainTag = domainData?.domainKey
  const siteName = domainData?.label

  const componentList = [
    {
      label: 'Header',
      value: 'header',
    },
    {
      label: 'Footer',
      value: 'footer',
    },
    {
      label: '404 Page',
      value: '404',
    },
    {
      label: 'Notification',
      value: 'notifications',
    },
  ]

  function fullRevalidate() {
    setLoading(true)

    const vercelUrl = getBranchDomainByTag(domainTag, "main")

    fetch(`${vercelUrl}/api/revalidate/full/?key=sf9ds8fd9fgdsf8dgk`)
      .then((res) => res.json())
      .then((data) => {
        if (data.revalidated) {
          Notification.success('Full flush successfull on ' + siteName)
          console.log('Full flush successfull ', data)
        } else {
          Notification.error('Full flush failed')
          console.error('Full flush failed', data)
        }
      })
      .catch((err) => {
        Notification.error('Full flush failed')
        console.error('Full flush failed', err)
      })
      .finally(() => {
        setLoading(false)
      })
  }

  function revalidateCacheByTag(tag: string) {
    const vercelUrl = getBranchDomainByTag(domainTag, "main")
    if (vercelUrl) {
      setLoading(true)
      fetch(`${vercelUrl}/api/revalidate/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tag: tag }),
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.revalidated) {
            Notification.success(`${selectedComponent} updated successfully`)
          } else {
            Notification.error(`${selectedComponent} revalidation failed`)
            console.error('Page revalidation failed', data)
          }
        })
        .catch((err) => {
          Notification.error('Component revalidation failed')
          console.error('Component revalidation failed', err)
        })
        .finally(() => {
          setLoading(false)
        })
    } else {
      Notification.error('Vercel revalidation URL not found')
    }
  }

  return (
    <div style={{ width: '100%', height: '60vh' }}>
      <div style={{}}>
        <Button
          style={{ margin: 15 }}
          isDisabled={loading}
          variant='negative'
          onClick={fullRevalidate}
        >
          Full flush
        </Button>

        <div style={{ height: 250 }}>
          <UpcomingFeatComp
            bgColor='#ffeea4'
            borderColor='#bdd8ff'
            color='#000000'
            subtitle='Nice to know'
            // title='Warning'
            desc='Manually flushing the system wide cache will remove the statically generated content from the previous release and the site-wide content will be regenerated afresh. <br/> Successively generated static pages will load speedily.<br/><br/> <strong>Note:</strong> <br/>Once the cache is flushed, the initial pageloads across the site will take a few more seconds to load.'
          />
        </div>
      </div>
      <div>
        <FormControl>
          <FormControl.Label>Flush Global Component</FormControl.Label>
          <div style={{ display: 'flex', marginTop: 10 }}>
            <Select
              name='components'
              value={selectedComponent}
              onChange={(e) => setSelectedComponent(e.target.value)}
            >
              {componentList.map((comp) => (
                <Select.Option key={comp.value} value={comp.value}>
                  {comp.label}
                </Select.Option>
              ))}
            </Select>
            <Button
              style={{ marginLeft: 20 }}
              isDisabled={loading}
              variant='primary'
              onClick={() => {
                revalidateCacheByTag(selectedComponent)
              }}
            >
              Flush {selectedComponent}
            </Button>
          </div>
          <p style={{ color: 'black', marginTop: 10 }}>
            The cache for {selectedComponent} will be flushed across the site{' '}
            {siteName}.
          </p>
        </FormControl>
        <FormControl>
          <FormControl.Label>Auto flush interval</FormControl.Label>
          <div style={{ display: 'flex', marginTop: 10 }}>
            <TextInput
              style={{ width: '100px', marginRight: 10 }}
              // value={value?.revalidationInterval || 10}
              type='number'
              name='revalidationInterval'
              isReadOnly
            // onChange={(e) => handleChange("revalidationInterval", e.target.value)}
            />

            <Select
              name='timeUnit'
              // value={value?.timeUnit || "minutes"}
              isDisabled
            // onChange={(e) => handleChange("timeUnit", e.target.value)}
            >
              {['minutes', 'hours', 'days', 'weeks', 'years', 'decades'].map(
                (unit) => (
                  <Select.Option key={unit} value={unit}>
                    {unit}
                  </Select.Option>
                )
              )}
            </Select>
          </div>
          <p style={{ color: 'black', marginTop: 10 }}>
            The cache for {siteName} will be automatically flushed every{' '}
            {value?.revalidationInterval || 1} {value?.timeUnit || 'hour'}.
          </p>
        </FormControl>
      </div>
    </div>
  )
}

export default CacheControl
