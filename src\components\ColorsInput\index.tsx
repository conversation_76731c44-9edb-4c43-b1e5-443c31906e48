/**
 * ColorsInput Component - Legacy Color Picker Interface
 *
 * This is the original color picker component that provides a simple grid-based
 * interface for selecting colors. It displays predefined colors in a grid layout
 * and allows users to select colors for text styling in the Tiptap editor.
 *
 * Key Features:
 * - Grid-based color selection interface
 * - Predefined color palette from colorsValues configuration
 * - Active color highlighting with visual feedback
 * - Integration with legacy Customiser component
 * - Simple click-to-select functionality
 * - Undo/reset functionality for color selection
 *
 * Note: This is the legacy version. ColorsInputV2 provides enhanced functionality
 * with advanced color management, search capabilities, and better UX.
 */

import { IconButton, Tooltip } from '@contentful/f36-components'
import React, { Fragment } from 'react'
//import ToolTip from '../Tooltip'
import Undo from '../../assets/icons/Undo'
import { GlobalContext } from '../../contexts/globalContext'
import '../Tiptap/styles.scss'
import { colorsValues } from './@core/colorsValues'
import SingleColor from './@core/SingleColor'
import styles from './styles/colorInput.module.scss'

/**
 * ColorsInput Main Component Function
 *
 * Renders a grid of color options with active state management and optional undo functionality.
 * Integrates with the global context for color state management and provides
 * visual feedback for the currently selected color.
 *
 * @param name - Category name for the color selection (e.g., 'Font Color', 'Highlight')
 * @param onClick - Callback function triggered when a color is selected, receives name and value
 * @param activeColor - Currently active/selected color hex value for highlighting
 * @param showUndo - Optional flag to show/hide the undo button (defaults to true)
 * @returns JSX.Element - The color picker grid interface
 */
const ColorsInput = ({
  name,
  onClick,
  activeColor,
  showUndo = true,
}: {
  name: string                                                                    // Color category identifier
  onClick: ({ name, value }: { name: string; value: string | number }) => void  // Color selection callback with structured data
  activeColor: string                                                             // Current active color for visual feedback
  showUndo?: boolean                                                              // Controls undo button visibility
}) => {
  // Access global color state management context
  const { setActiveColor } = React.useContext(GlobalContext)
  console.warn('activeColoe', activeColor) // Debug log for active color tracking

  function handleReset() {
    if (name === 'Color') {
      setActiveColor((prev) => {
        return {
          ...prev,
          fontColor: 'black',
        }
      })
    }
    else {
      setActiveColor((prev) => {
        return {
          ...prev,
          highlightColor: null,
        }
      })
    }
  }

  return (
    <div
      style={{
        //display : 'flex',
        flexWrap: 'wrap',
        gap: '0.5rem',
        width: 300,
        margin: '0 20px',
      }}
    >
      {showUndo && (
        <div className={styles.colorSelectionDiv}>
          {/*<ButtonGroup>*/}
          <Tooltip placement='bottom' content={`Reset ${name}`}>
            <IconButton
              size={'medium'}
              variant='transparent'
              aria-label='reset'
              icon={<Undo />}
              onClick={handleReset}
            />
          </Tooltip>
          {/*</ButtonGroup>*/}
        </div>
      )}
      {colorsValues.map((colors) => (
        <Fragment key={colors.name}>
          <div className={styles.colorRoot}>
            <p className={styles.colorTitle}>{colors.name}</p>
            <div className={styles.colorTiles}>
              {colors.colors.map((color) => (
                <Fragment key={color.hex}>
                  <Tooltip
                    placement='top'
                    content={`${color.name} : ${color.codeName}`}
                  >
                    <div className={styles.singleColorRoot}>
                      <SingleColor
                        activeColor={activeColor}
                        name={name}
                        onClick={onClick}
                        value={color.hex}
                      />
                    </div>
                  </Tooltip>
                </Fragment>
              ))}
            </div>
          </div>
        </Fragment>
      ))}
    </div>
  )
}

export default ColorsInput