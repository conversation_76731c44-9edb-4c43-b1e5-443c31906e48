export const SPACE_ID = process.env.REACT_APP_CONTENTFUL_SPACE_ID || ''

export const ENVIRONMENT = process.env.REACT_APP_CONTENTFUL_ENVIRONMENT || ''

export const TOKEN = process.env.REACT_APP_USER_TOKEN || ''

export const ACCESS_TOKEN = process.env.REACT_APP_ACCESS_TOKEN || ''

export const CONTENTFUL_URL = `https://preview.contentful.com/spaces/${SPACE_ID}/environments/${ENVIRONMENT}/entries?access_token=${ACCESS_TOKEN}`

export const CONTENTFULASSET_DOMAIN = `assets.ctfassets.net`
export const NODE_ENVIRONEMENT = process.env.NODE_ENV ?? 'development'

export const STATSIG_CONSOLE_TOKEN =
  process.env.REACT_APP_STATSIG_CONSOLE_TOKEN || ''

export const BRANCH_FRONT = process.env.REACT_APP_BRANCH_FRONT || 'dev'

export const STATASIG_CONSOLE_ID =
  process.env.REACT_APP_STATASIG_CONSOLE_ID || '4DI5baOXXxC87NE87uIoP6'

export const ENV_VARIABLES = {
  contentfulSpaceID: SPACE_ID,
  contentfulEnvironment: ENVIRONMENT,
  contentfulToken: TOKEN,
  contentfulAccessToken: ACCESS_TOKEN,
  contenfulPreviewUrl: CONTENTFUL_URL,
  nodeEnvironment: NODE_ENVIRONEMENT,
  statsigConsoleToken: STATSIG_CONSOLE_TOKEN,
  branchFront: BRANCH_FRONT,
  statsigConsoleId: STATASIG_CONSOLE_ID,
  FIREBASE_API_KEY: process.env.REACT_APP_FIREBASE_API_KEY || '',
  FIREBASE_AUTH_DOMAIN: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || '',
  FIREBASE_PROJECT_ID: process.env.REACT_APP_FIREBASE_PROJECT_ID || '',
  FIREBASE_STORAGE_BUCKET: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || '',
  FIREBASE_MESSAGING_SENDER_ID:
    process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || '',
  FIREBASE_APP_ID: process.env.REACT_APP_FIREBASE_APP_ID || '',
  FIREBASE_MEASUREMENT_ID: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID || '',
  FIREBASE_DATABASE_URL: process.env.REACT_APP_FIREBASE_DATABASE_URL || '',
  BASE_URL: process.env.REACT_APP_BASE_URL || '',
}
