export const SPACE_ID = process.env.REACT_APP_CONTENTFUL_SPACE_ID || ''

export const ENVIRONMENT = process.env.REACT_APP_CONTENTFUL_ENVIRONMENT || ''

export const TOKEN = process.env.REACT_APP_USER_TOKEN || ''

export const ACCESS_TOKEN = process.env.REACT_APP_ACCESS_TOKEN || ''

export const CONTENTFUL_URL = `https://preview.contentful.com/spaces/${SPACE_ID}/environments/${ENVIRONMENT}/entries?access_token=${ACCESS_TOKEN}`

export const NODE_ENVIRONEMENT = process.env.NODE_ENV ?? 'development'

export const STATSIG_CONSOLE_TOKEN =
  process.env.REACT_APP_STATSIG_CONSOLE_TOKEN || ''

export const MAIN_BRANCH = process.env.REACT_APP_MAIN_BRANCH || 'dev'

export const STATASIG_CONSOLE_ID =
  process.env.REACT_APP_STATASIG_CONSOLE_ID || '4DI5baOXXxC87NE87uIoP6'

export const CONTENTFULASSET_DOMAIN = `assets.ctfassets.net`

export const VERCEL_BYPASS_SECRET = process.env.REACT_APP_VERCEL_BYPASS_SECRET

export const APP_BASE_URL =
  process.env.REACT_APP_BASE_URL ||
  'https://msa-agl-v3w-git-dev-altus.vercel.app'

export const MAIN_PREVIEW_BRANCH = process.env.REACT_APP_MAIN_PREVIEW_BRANCH

export const IS_MAIN_PREVIEW_BRANCH =
  process.env.REACT_APP_DISPLAY_ON_MAIN_BRANCH === 'true' &&
  MAIN_PREVIEW_BRANCH?.toString() !== MAIN_BRANCH?.toString()

export const ENV_VARIABLES = {
  contentfulSpaceID: SPACE_ID,
  contentfulEnvironment: ENVIRONMENT,
  contentfulToken: TOKEN,
  contentfulAccessToken: ACCESS_TOKEN,
  contenfulPreviewUrl: CONTENTFUL_URL,
  nodeEnvironment: NODE_ENVIRONEMENT,
  statsigConsoleToken: STATSIG_CONSOLE_TOKEN,
  mainBranch: MAIN_BRANCH,
  statsigConsoleId: STATASIG_CONSOLE_ID,
  assetdomain: CONTENTFULASSET_DOMAIN,
  vercelByPassSecret: VERCEL_BYPASS_SECRET,
  appBaseURL: APP_BASE_URL,
  MainPreviewBranch: MAIN_PREVIEW_BRANCH ?? MAIN_BRANCH ?? 'dev',
  isMainPreview: IS_MAIN_PREVIEW_BRANCH,
  // isMainPreview:
  //   MAIN_PREVIEW_BRANCH &&
  //   BRANCH_FRONT?.toLocaleLowerCase() ===
  //     MAIN_PREVIEW_BRANCH?.toLocaleLowerCase(),
  FIREBASE_API_KEY: process.env.REACT_APP_FIREBASE_API_KEY || '',
  FIREBASE_AUTH_DOMAIN: process.env.REACT_APP_FIREBASE_AUTH_DOMAIN || '',
  FIREBASE_PROJECT_ID: process.env.REACT_APP_FIREBASE_PROJECT_ID || '',
  FIREBASE_STORAGE_BUCKET: process.env.REACT_APP_FIREBASE_STORAGE_BUCKET || '',
  FIREBASE_MESSAGING_SENDER_ID:
    process.env.REACT_APP_FIREBASE_MESSAGING_SENDER_ID || '',
  FIREBASE_APP_ID: process.env.REACT_APP_FIREBASE_APP_ID || '',
  FIREBASE_MEASUREMENT_ID: process.env.REACT_APP_FIREBASE_MEASUREMENT_ID || '',
  FIREBASE_DATABASE_URL: process.env.REACT_APP_FIREBASE_DATABASE_URL || '',
  BASE_URL: process.env.REACT_APP_BASE_URL || '',
}
