import { <PERSON>AppSDK } from '@contentful/app-sdk'
import React, { useEffect } from 'react'

/**
 * Applies default navigation settings to a page based on its template.
 *
 * It will check if the page is published and if the page's creation date is before 2024-11-17.
 * If the page is published or the creation date is before the specified date, it will do nothing.
 * If the page is not published and the creation date is after the specified date, it will apply the default navigation settings based on the page's template.
 *
 * @param {EditorAppSDK} sdk The Contentful app SDK
 * @returns {React.ReactElement} An empty React element
 */
export const ApplyNavDefaultSettings = ({ sdk }: { sdk: EditorAppSDK }) => {
  const apply = () => {
    // get the page template
    const template = sdk.entry.fields['template'].getValue()

    // check for specific template
    if (TemplatesToApply.includes(template)) {
      // get the page config
      const pageConfig = sdk.entry.fields['configurations'].getValue() || {}

      // check if default nav is initialized, if yes, do nothing
      const isDefaultNavInitialized = pageConfig.isDefaultNavInitialized

      if (isDefaultNavInitialized) return

      // check if default nav is updated by user, if yes, do nothing
      const isDefaultNavUpdated = pageConfig.isDefaultNavUpdated

      if (isDefaultNavUpdated) return

      // check if page is published
      const publishedCounter = sdk.entry.getSys().publishedCounter

      const isPagePublished = publishedCounter ? publishedCounter > 0 : false

      if (isPagePublished) return

      // get the page's creation date
      const propsDate = new Date(sdk.entry.getSys().createdAt)

      // check if the page's creation date is before 2024-12-03
      const comparisonDate = new Date('2024-12-03')

      const isBefore = propsDate < comparisonDate

      // check if the page's creation date is before 2024-11-17 if yes, do nothing
      if (isBefore) return

      // set default nav initialized as true
      sdk.entry.fields['configurations'].setValue({
        ...pageConfig,
        isDefaultNavInitialized: true,
      })

      // set default nav settings
      if (template === 'Insight Article') {
        InsightArticleDefaultNav.forEach((key) => {
          sdk.entry.fields[key].setValue(true)
        })
      } else if (template === 'Press Release') {
        PressReleaseDefaultNav.forEach((key) => {
          sdk.entry.fields[key].setValue(true)
        })
      } else if (template === 'Generic') {
        GenericDefaultNav.forEach((key) => {
          sdk.entry.fields[key].setValue(true)
        })
      }

      sdk.entry.save()
    }
  }

  useEffect(() => {
    sdk.entry.fields['template'].onValueChanged(() => {
      apply()
    })
  }, [sdk])

  return <></>
}

const InsightArticleDefaultNav = ['isLightBgImage', 'isNavLightMode']

const PressReleaseDefaultNav = [
  'isLightBgImage',
  'isNavLightMode',
  'isTranslucent',
]

const GenericDefaultNav = ['isTranslucent']

const TemplatesToApply = ['Insight Article', 'Press Release', 'Generic']
