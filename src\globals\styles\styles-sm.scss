// @todo implement media queries for styles present in styles-lg.scss

/**
Import defaults variables
 */
@import 'variables';

/** globalStylesV4 is made for v4, it uses v4 naming convention for future.
There are instances where variable and classNames are used from "_vars.scss" but in future when we revisit,
we will consolidate both vars and vars2 into one and "_vars2.scss" will replace old "_vars.scss" */

@import "globalStylesV4";

@media (max-width: $smScreenSize) {
  .sH1 {
    font-size: $sH1FontSize;
    line-height: 22px;
  }

  .sH2 {
    font-size: $sH2FontSize;
    line-height: 24px;
  }

  .sH3 {
    font-size: $sH3FontSize;
    line-height: 26px;
  }

  .sH4 {
    font-size: $sH4FontSize;
    line-height: 32px;
  }

  .sH5 {
    font-size: $sH5FontSize;
    line-height: 40px;
  }

  .sH6 {
    font-size: $sH6FontSize;
    line-height: 50px;
  }

  .sfMax {
    font-size: $sfMax;
    line-height: 60px;
  }
}
