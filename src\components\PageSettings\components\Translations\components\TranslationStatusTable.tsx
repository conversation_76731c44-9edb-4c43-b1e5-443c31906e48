import { Badge, Table } from '@contentful/f36-components'
import _ from 'lodash'
import React from 'react'
import { ENV_VARIABLES } from '../../../../../constant/variables'

function TranslationStatusTable({ data }: { data: any }) {
  /**
   * Open a Contentful entry in a new tab.
   * @param {string} id - The ID of the entry to open.
   */
  const handleEntryNavigateById = (id: string) => {
    if (!id) return

    const url = `https://app.contentful.com/spaces/${ENV_VARIABLES.contentfulSpaceID}/environments/${ENV_VARIABLES.contentfulEnvironment}/entries/${id}`

    window.open(url, '_blank')
  }

  return (
    <Table>
      <Table.Head>
        <Table.Row>
          <Table.Cell>No.</Table.Cell>
          <Table.Cell>Internal Name</Table.Cell>
          <Table.Cell>Component Type</Table.Cell>
          <Table.Cell>Status</Table.Cell>
          <Table.Cell>Message</Table.Cell>
        </Table.Row>
      </Table.Head>
      <Table.Body>
        {data.map((item: any, index: number) => (
          <Table.Row>
            <Table.Cell className='w-[30px]'>{index + 1}</Table.Cell>

            <Table.Cell
              className='cursor-pointer hover:underline underline-offset-2 w-[150px]'
              onClick={() => handleEntryNavigateById(item?.entryId)}
            >
              {item?.internalName}
            </Table.Cell>
            <Table.Cell className='w-[150px]'>{item?.type}</Table.Cell>
            <Table.Cell className='w-[100px]'>
              <Badge
                variant={
                  item?.status === 'pending' || item?.status === 'N/A'
                    ? 'secondary'
                    : item?.status === 'done'
                    ? 'positive'
                    : 'negative'
                }
              >
                {item?.status === 'N/A' ? 'N/A' : _(item?.status)?.capitalize()}
              </Badge>
            </Table.Cell>
            <Table.Cell className='w-[200px]'>
              {item?.message ?? '-'}
            </Table.Cell>
          </Table.Row>
        ))}
      </Table.Body>
    </Table>
  )
}

export default TranslationStatusTable
