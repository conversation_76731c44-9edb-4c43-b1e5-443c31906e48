import { useEffect } from 'react'

/**
 * A custom React hook that adds a 'mouseup' event listener to the document.
 * 
 * @param handler - A function to be executed when the 'mouseup' event is triggered.
 * 
 * This hook ensures that the provided handler is registered as an event listener for the 'mouseup' event
 * on the document. The handler will be removed when the component using this hook is unmounted or if the
 * handler function changes.
 */

export const useGlobalMouseUp = (
  handler: (this: Document, ev: MouseEvent) => unknown
) => {
  useEffect(() => {
    document.addEventListener('mouseup', handler)
    return () => document.removeEventListener('mouseup', handler)
  }, [handler])
}
