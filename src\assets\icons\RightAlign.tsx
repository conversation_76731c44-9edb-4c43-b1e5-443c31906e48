import React from 'react'
import { Icon } from '@contentful/f36-components'

function RightAlign(props: any) {
  return (
    <Icon {...props} size={'tiny'}>
      <svg
        fill='#000000'
        version='1.1'
        id='Layer_1'
        xmlns='http://www.w3.org/2000/svg'
        xmlnsXlink='http://www.w3.org/1999/xlink'
        viewBox='0 0 512 512'
        xmlSpace='preserve'
      >
        <g id='SVGRepo_bgCarrier' strokeWidth='0'></g>
        <g
          id='SVGRepo_tracerCarrier'
          strokeLinecap='round'
          strokeLinejoin='round'
        ></g>
        <g id='SVGRepo_iconCarrier'>
          <g>
            <g>
              <rect y='38.957' width='512' height='33.391'></rect>
            </g>
          </g>
          <g>
            <g>
              <rect
                x='111.304'
                y='139.13'
                width='400.696'
                height='33.391'
              ></rect>
            </g>
          </g>
          <g>
            <g>
              <rect y='239.304' width='512' height='33.391'></rect>
            </g>
          </g>
          <g>
            <g>
              <rect y='439.652' width='512' height='33.391'></rect>
            </g>
          </g>
          <g>
            <g>
              <rect
                x='111.304'
                y='339.478'
                width='400.696'
                height='33.391'
              ></rect>
            </g>
          </g>
        </g>
      </svg>
    </Icon>
  )
}

export default RightAlign
