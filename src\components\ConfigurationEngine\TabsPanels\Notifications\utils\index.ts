import * as contentful from 'contentful-management'
import { SPACE_ID, TOKEN } from '../../../../../constant/variables'

export const fetchEntryDetails = async (entryId: string, extension: any) => {
  if (extension) {
    const cma = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN, // Use the access token from the extension parameters
    })

    try {
      const space = await cma.getSpace(extension.ids.space)
      const environment = await space.getEnvironment(extension.ids.environment)
      const entry: any = await environment.getEntry(entryId)

      return entry
    } catch (error) {
      console.error('Error fetching entry details:', error)
    }
  }
}

export function formatIsoDate(isoDate: string): string {
  const date = new Date(isoDate)

  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const seconds = date.getSeconds().toString().padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
