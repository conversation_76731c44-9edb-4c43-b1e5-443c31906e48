import {
  Box,
  Button,
  FormControl,
  Select,
  Switch,
  Text,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import { LinkIcon } from '@contentful/f36-icons'
import React from 'react'
import Info from '../../../assets/icons/Info'
import FormControlComp from '../../Shared/FormControlComp'
import TextDivider from '../../Shared/TextDivider'

import { EditorAppSDK } from '@contentful/app-sdk'
import { Entry } from 'contentful-management'
import ModalConfirm from '../../ConfirmModal'
import { getDomainDataFromDomainConfig } from '../helpers'
import {
  Domains,
  domainsConfig,
  getDomainFullName,
  getDomainWebsiteName,
  getLocaleFullName,
} from '../utils'
import ContentEntryCard from './ContentEntryCard'

interface DomainLocale {
  [key: string]: string[]
}
interface Props {
  checkboxState: { [key: string]: boolean }
  handleCheckBoxStateChange: (x: Domains) => void
  isDataFetching: boolean
  originalDomain: Domains
  setOriginalDomain: React.Dispatch<React.SetStateAction<Domains>>
  setSourceDomainSlugs: (y: Domains) => void
  domainLocale: DomainLocale
  getSelectedDomainNames: () => string[]
  slugAgainstDomains: any
  handleSlugChangeByDomainAndLocale: (
    value: string,
    domain: Domains,
    locale: string
  ) => void
  entryId: string
  handlePrimaryCTAChange: (domain: string, id: string) => void
  handleRemoveByField: (domain: string, field: string) => void
  isInsightsArticle: boolean
  sdk: EditorAppSDK
  resetSlugAgainstDomainsAndSetNewSourceDomain: (x: Domains) => void
}

/**
 * Renders a component that allows users to manage slugs and primary CTAs for different domains and locales.
 * It provides functionality to select source domains, toggle domain visibility, and edit slugs and CTAs.
 *
 * @param {Props} props - The properties object containing configuration and state handlers.
 * @param {Object} props.checkboxState - The state to track which domains are checked.
 * @param {Function} props.handleCheckBoxStateChange - Handler to toggle domain checkbox state.
 * @param {boolean} props.isDataFetching - Flag to indicate if data is being fetched.
 * @param {Domains} props.originalDomain - The currently selected original domain.
 * @param {Function} props.setOriginalDomain - Handler to set the original domain.
 * @param {Function} props.setSourceDomainSlugs - Handler to set slugs for the source domain.
 * @param {DomainLocale} props.domainLocale - Object containing locales for each domain.
 * @param {Function} props.getSelectedDomainNames - Function to get selected domain names.
 * @param {Object} props.slugAgainstDomains - Object containing slug data against domains.
 * @param {Function} props.handleSlugChangeByDomainAndLocale - Handler to change slug by domain and locale.
 * @param {string} props.entryId - The entry ID associated with the slugs and CTAs.
 * @param {Function} props.handlePrimaryCTAChange - Handler to change the primary CTA for a domain.
 * @param {Function} props.handleRemoveByField - Handler to remove a field value for a domain.
 * @param {boolean} props.isInsightsArticle - Flag to indicate if the entry is an insights article.
 * @param {EditorAppSDK} props.sdk - The SDK instance for interacting with Contentful.
 */
function SourceAndSlug(props: Props) {
  const {
    checkboxState,
    handleCheckBoxStateChange,
    isDataFetching,
    originalDomain,
    setOriginalDomain,
    setSourceDomainSlugs,
    domainLocale,
    getSelectedDomainNames,
    slugAgainstDomains,
    handleSlugChangeByDomainAndLocale,
    entryId,
    handlePrimaryCTAChange,
    handleRemoveByField,
    isInsightsArticle,
    sdk,
    resetSlugAgainstDomainsAndSetNewSourceDomain,
  } = props

  const isCrossPosted = !!sdk.entry.fields['configurations']
    .getForLocale('en-CA')
    .getValue()?.isCrossPosted

  const [isConfirmModalOpen, setIsConfirmModalOpen] = React.useState(false)

  const [tempSourceValue, setTempSourceValue] = React.useState('')

  const [hasAlertShown, setHasAlertShown] = React.useState(false)

  /**
   * Opens a dialog for selecting a single Contentful entry of type 'componentCtaRow'
   * and updates the primaryCTA state with the selected entry's id.
   * @param {string} domain - The domain that the primaryCTA is being selected for
   */
  const PrimaryCTASelector = async (domain: string) => {
    const contentTypes = ['componentCtaRow']

    const selected: Entry | null = await sdk.dialogs.selectSingleEntry({
      contentTypes: contentTypes,
    })

    if (!selected) return

    const id = selected.sys.id

    handlePrimaryCTAChange(domain, id)
  }

  /**
   * Removes the primaryCTA for the given domain. This is triggered when the
   * user clicks the 'Remove' button next to a selected primaryCTA.
   * @param {string} domain - The domain to remove the primaryCTA for
   */
  const handlePrimaryCTARemoveByDomain = (domain: string) => {
    handleRemoveByField(domain, 'primaryCTA')
  }

  const handleSourceWebsiteChange = (x: Domains) => {
    if (isCrossPosted && !hasAlertShown) {
      setTempSourceValue(x)
      setIsConfirmModalOpen(true)
    } else {
      setOriginalDomain(x)
      setSourceDomainSlugs(x)
      if (!checkboxState[x]) handleCheckBoxStateChange(x)
    }
  }

  const handleConfirm = () => {
    resetSlugAgainstDomainsAndSetNewSourceDomain(tempSourceValue as Domains)
    setIsConfirmModalOpen(false)
    setTempSourceValue('')
    setHasAlertShown(true)
  }

  return (
    <Box className='flex items-start justify-start w-full h-full '>
      <aside className='bg-[#F7F9FA] w-[350px] h-full'>
        <h6 className={'pl-4 pt-2 mb-2'}>Websites</h6>
        <ul>
          {domainsConfig?.map((item, index) => {
            return (
              <li
                key={index}
                className={` cursor-pointer flex justify-start items-center p-0 hover:bg-[rgba(207,217,224,0.5)]`}
              >
                <Switch
                  isChecked={checkboxState[item.key as Domains]}
                  onChange={() =>
                    handleCheckBoxStateChange(item.key as Domains)
                  }
                  className='py-2 pl-5'
                  isDisabled={isDataFetching}
                >
                  {item.label}
                </Switch>
              </li>
            )
          })}
        </ul>
      </aside>

      <main className='w-full px-5 overflow-y-auto h-full py-5'>
        <div className='flex w-full flex-col items-start justify-start px-1'>
          <FormControlComp
            label='Source Website'
            tooltip={`Source website specifies where the canonical URL should point to.`}
          >
            <Select
              isDisabled={isDataFetching}
              value={originalDomain}
              onChange={(event: React.ChangeEvent<HTMLSelectElement>) =>
                handleSourceWebsiteChange(event.target.value as Domains)
              }
            >
              <Select.Option value={''} isDisabled>
                Select source website
              </Select.Option>
              {domainsConfig?.map((item) => (
                <Select.Option key={item.key} value={item.key}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </FormControlComp>
          {getSelectedDomainNames().map((selectedDomain: string) => {
            return (
              <>
                <TextDivider
                  text={getDomainWebsiteName(
                    getDomainFullName(selectedDomain as Domains)
                  )}
                />
                {getDomainDataFromDomainConfig(selectedDomain)?.map(
                  (domain: any) => {
                    const localeToShow: any = domainLocale?.[
                      domain.domainCode
                    ] || ['en-CA']

                    return (
                      <Box
                        className='w-full flex items-start justify-start gap-5'
                        key={domain.domainCode}
                      >
                        <Box className='w-full pt-4 flex justify-start items-start gap-5 flex-col'>
                          {localeToShow?.map((locale: string) => (
                            <FormControl
                              key={`${domain.domainCode}-${locale}`}
                              className='mb-0 w-full'
                            >
                              <div className='flex justify-start items-start gap-0.5 cursor-pointer mb-2.5 ml-0'>
                                <FormControl.Label className='mb-0'>
                                  <span className='flex gap-1 items-center'>
                                    <p className='text-black font-semibold'>
                                      {getLocaleFullName(locale.split('-')[0])}
                                    </p>
                                    <p className='text-[#667082] text-xs'>
                                      ({locale.toUpperCase()})
                                    </p>
                                  </span>
                                </FormControl.Label>
                                <Tooltip
                                  placement='top'
                                  id='tooltip-2'
                                  content='Do not specify language settings, they will be applied automatically.'
                                >
                                  <Info />
                                </Tooltip>
                              </div>
                              <TextInput
                                name={`${domain.domainCode}-${
                                  locale.split('-')[0]
                                }`}
                                value={
                                  slugAgainstDomains?.[domain.domainId]?.slug?.[
                                    locale.split('-')[0]
                                  ]
                                }
                                onChange={(event) =>
                                  handleSlugChangeByDomainAndLocale(
                                    event.target.value,
                                    domain.domainCode as Domains,
                                    locale.split('-')[0]
                                  )
                                }
                                testId={`${domain.domainCode}-${
                                  locale.split('-')[0]
                                }`}
                              />
                            </FormControl>
                          ))}
                        </Box>
                        {isInsightsArticle && (
                          <FormControl
                            key={`${domain.domainCode}`}
                            className='mb-0 w-full pt-5'
                          >
                            <div className='flex justify-start items-start gap-0.5 cursor-pointer mb-2.5 ml-0'>
                              <FormControl.Label className='mb-0'>
                                <span className='flex gap-1 items-center'>
                                  <p className='text-black font-semibold'>
                                    Primary CTA
                                  </p>
                                </span>
                              </FormControl.Label>
                              <Tooltip
                                placement='top'
                                id='tooltip-2'
                                content='Select the primary CTA for this domain.'
                              >
                                <Info />
                              </Tooltip>
                            </div>
                            {slugAgainstDomains?.[domain.domainId]
                              ?.primaryCTA ? (
                              <ContentEntryCard
                                id={
                                  slugAgainstDomains?.[domain.domainId]
                                    ?.primaryCTA
                                }
                                onRemoveEntry={() =>
                                  handlePrimaryCTARemoveByDomain(
                                    domain.domainId
                                  )
                                }
                              />
                            ) : (
                              <Button
                                onClick={() =>
                                  PrimaryCTASelector(domain.domainId)
                                }
                                variant='secondary'
                                startIcon={<LinkIcon />}
                                size='small'
                              >
                                Link existing CTA Row
                              </Button>
                            )}
                          </FormControl>
                        )}
                      </Box>
                    )
                  }
                )}
              </>
            )
          })}
        </div>
      </main>
      <ModalConfirm
        open={isConfirmModalOpen}
        handleClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirm}
        children={
          <Text className='text-base font-semibold'>
            Updating crossposted page's source website will reset crosspost
            config for this page
          </Text>
        }
        btn1Text='Cancel'
        btn2Text='Confirm'
        title='Confirm Action'
      />
    </Box>
  )
}

export default SourceAndSlug
