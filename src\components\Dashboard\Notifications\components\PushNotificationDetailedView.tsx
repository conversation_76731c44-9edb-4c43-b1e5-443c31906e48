import { But<PERSON>, <PERSON><PERSON>, Tag, Tooltip } from 'antd'
import React from 'react'
import { CiCircleInfo } from 'react-icons/ci'
import {
  AllowedNotificationType,
  CTATargets,
  NotificationDuration,
  NotificationType,
} from '../utils/constants'
import { DETAILED_VIEW_TOOLTIP } from './constants'
import SummaryItem from './summary/SummaryItem'

/**
 * @description
 * A component that renders a button to open a detailed view of a push notification.
 * The detailed view includes information about the notification, such as its title,
 * description, URL, type, CTA text, CTA target, duration, and more.
 * @param {Object} props
 * @prop {any} noti - The notification object to be rendered in the detailed view.
 */
function PushNotificationDetailedView({ noti }: { noti: any }) {
  const [showDetailedView, setShowDetailedView] = React.useState(false)

  const handleClose = () => setShowDetailedView(false)

  /**
   * Retrieves the names of notification categories.
   *
   * This function parses the `category` field from the `noti` object, which is expected
   * to be a JSON string. It maps over the parsed categories, matching each category value
   * with its corresponding title from the `AllowedNotificationType` array.
   *
   * @returns {string[]} An array of category titles based on the `noti` object's categories.
   *                      Returns an empty array if no categories are found or if parsing fails.
   */

  const getNotificationCategoryNames = () => {
    return (
      JSON.parse(noti?.category || '[]')?.map(
        (cat: string) =>
          AllowedNotificationType.find((item) => item.value === cat)?.title ||
          ''
      ) || []
    )
  }

  const colorMap = {
    General: '#6C757D',
    Insights: '#4A6FA5',
    'Press releases': '#8E4162',
    Webinars: '#D17B46',
    Events: '#5A8F7B',
    Research: '#3A5683',
    'Training & Education': '#946846',
  }

  return (
    <>
      <Tooltip title={DETAILED_VIEW_TOOLTIP}>
        <Button
          onClick={() => setShowDetailedView(true)}
          type='primary'
          size={'large'}
          icon={<CiCircleInfo />}
          className={'text-neutral-950 bg-transparent border-none'}
        />
      </Tooltip>
      <Modal
        open={showDetailedView}
        onCancel={handleClose}
        onClose={handleClose}
        footer={null}
        centered
        className={'w-full max-w-4xl'}
        title={
          <div className=''>
            <div className='text-xl font-semibold flex flex-col start gap-4 justify-start'>
              <div className={'flex items-center gap-5'}>
                {noti?.icon && (
                  <img
                    alt={'icon'}
                    src={noti?.icon}
                    className={'rounded w-12 h-12'}
                  />
                )}
                {noti?.title}
              </div>
              <div className={'flex items-center'}>
                {getNotificationCategoryNames()?.map((item: string) => {
                  return (
                    <Tag
                      color={colorMap[item as keyof typeof colorMap]}
                      className={'rounded-lg px-2.5 py-0.5'}
                    >
                      {item}
                    </Tag>
                  )
                })}
              </div>
            </div>
          </div>
        }
      >
        <div className={'flex flex-col justify-start items-start w-full gap-3'}>
          <SummaryItem
            title={'Desc'}
            value={noti?.body}
            type={'String'}
            isSmall={true}
          />
          <SummaryItem
            title={'URL'}
            value={noti?.url}
            type={'String'}
            isSmall={true}
          />
          <SummaryItem
            title={'Type'}
            value={
              NotificationType.find((item) => item.value === noti?.type)?.title
            }
            type={'String'}
            isSmall={true}
          />
          <SummaryItem
            title={'CTA Text'}
            value={noti?.ctaText}
            type={'String'}
            isSmall={true}
          />
          <SummaryItem
            title={'CTA Target'}
            value={
              CTATargets.find((item) => item.value === noti?.ctaTarget)?.title
            }
            type={'String'}
            isSmall={true}
          />
          <SummaryItem
            title={'Duration'}
            type={'String'}
            value={
              NotificationDuration.find((item) => item.value === noti?.duration)
                ?.title
            }
            isSmall={true}
          />
          {/*<SummaryItem*/}
          {/*  title={'Is System Notification'}*/}
          {/*  value={noti?.isSystemNotification === 'true'}*/}
          {/*  type={'Boolean'} isSmall={true} />*/}
          {/*{noti?.isSystemNotification === 'true' && (*/}
          {/*  <SummaryItem*/}
          {/*    title={'Is System Notification Dismissible'}*/}
          {/*    value={noti?.isDismissible === 'true'}*/}
          {/*    type={'Boolean'} isSmall={true} />*/}
          {/*)}*/}
          <SummaryItem
            title={'Is Attention Required'}
            value={noti?.attentionRequired === 'true'}
            type={'Boolean'}
            isSmall={true}
          />
          <SummaryItem
            title={'Renotify'}
            value={noti?.renotify === 'true'}
            type={'Boolean'}
            isSmall={true}
          />
          <SummaryItem
            title={'Grouping Category'}
            value={
              AllowedNotificationType.find((item) => item.value === noti?.tag)
                ?.title
            }
            type={'String'}
          />
          <SummaryItem
            title={'Badge'}
            type={'Asset'}
            thumbnail={{
              url: noti?.badge,
              status: '',
            }}
          />
        </div>
      </Modal>
    </>
  )
}

export default PushNotificationDetailedView
