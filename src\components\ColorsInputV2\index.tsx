'use client'
import { <PERSON><PERSON>, Icon } from '@contentful/f36-components'
import { CloseIcon, DoneIcon } from '@contentful/f36-icons'
import React, { useEffect, useState } from 'react'
import { BsClipboard2MinusFill } from 'react-icons/bs'
import { Tooltip as ReactTooltip } from 'react-tooltip'
import TheAnimatedNode from '../TheAnimatedNode'
import ColorPickerTooltip from './@core/ColorPickerTooltip'
import { colorsValues } from './colorValues'
import styles from './index.module.scss'
import { cleanRgb, findColorIndexByHex, getColorStyles } from './utils'

export interface ColorI {
  name: string
  hex: string
  rgb: string
  codeName: string
  variants?: Array<ColorI>
}
export interface DisabledColors {
  Primaries?: boolean
  Secondaries?: boolean
  Accents?: boolean
  Neutrals?: boolean
  Gradients?: boolean
  // add other color keys as needed
}
export interface ActiveColorIndexI {
  categoryIndex: number
  colorIndex: number
  variantIndex: number | null
}

export default function ColorsInputV2({
  onChange,
  heading,
  // handleClose,
  // isOpen,
  onOpen = () => {},
  onClose = () => {},
  activeColor = '',
  disabled = {},
  pickerdisabled = false,
  pickerType = 'Colorama',
  isHeaderHidden = false,
  isApplyButtonHidden = false,
  copiedSpanValue,
  onCopyValue,
}: {
  onChange: (color: ColorI) => void
  heading?: string
  onOpen: () => void
  onClose: () => void
  // handleClose: () => void
  // isOpen: boolean
  onCopyValue?: (x: string) => void
  copiedSpanValue?: string
  activeColor?: string | null
  pickerdisabled?: boolean
  disabled?: DisabledColors
  pickerType?: 'Button' | 'Colorama' | 'Standalone'
  isHeaderHidden?: boolean
  isApplyButtonHidden?: boolean
}) {
  const [isOpen, setIsOpen] = useState(pickerType === 'Standalone')
  const [activeColorIndex, setActiveColorIndex] = useState<ActiveColorIndexI>({
    categoryIndex: null,
    colorIndex: null,
    variantIndex: null,
  })

  const [selectedColor, setSelectedColor] = useState<ColorI>()
  const [searchValue, setSearchValue] = useState('')
  const [selectedSpan, setSelectedSpan] = useState('')
  const [currentColor, setCurrentColor] = useState(selectedColor)
  const [debouncedHexInput, setDebouncedHexInput] = useState(searchValue)

  //tooltip triangle animation
  const variants = {
    active: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.15,
        ease: 'easeIn',
        delay: 0.1,
      },
    },
    inactive: {
      opacity: 0,
      y: -10,
      transition: {
        duration: 0.3,
      },
    },
  }
  const handleClose = () => {
    setIsOpen(false)
    onClose()
  }
  useEffect(() => {
    setCurrentColor(selectedColor)
  }, [selectedColor])

  const togglePopup = () => {
    handleClose()
  }

  useEffect(() => {
    if (selectedColor && onChange) {
      onChange(selectedColor)
    }
  }, [selectedColor])

  useEffect(() => {
    searchColorHandler(activeColor || '')
  }, [activeColor])

  useEffect(() => {
    if (copiedSpanValue) {
      setSelectedSpan(copiedSpanValue)
    }
  }, [copiedSpanValue])

  // Function to search for a color by hex value
  const searchColorHandler = (hexInput: string) => {
    setSearchValue(hexInput)
    const index = findColorIndexByHex(hexInput, disabled)

    if (index) {
      setActiveColorIndex(index)
      setSelectedColor(
        index.variantIndex !== null
          ? colorsValues[index.categoryIndex].colors[index.colorIndex]
              ?.variants?.[index.variantIndex]
          : colorsValues[index.categoryIndex].colors[index.colorIndex]
      )
    } else {
      setActiveColorIndex(null)
      setSelectedColor(null)
    }
  }

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedHexInput(searchValue)
    }, 1000)

    return () => {
      clearTimeout(handler)
    }
  }, [searchValue])

  useEffect(() => {
    if (debouncedHexInput) {
      searchColorHandler(debouncedHexInput)
    }
  }, [debouncedHexInput])

  function copyToClipboard(value: string | undefined) {
    if (!value) {
      return
    }
    navigator.clipboard
      .writeText(value)
      .then(() => {
        setSelectedSpan(value)
        onCopyValue && onCopyValue(value)
        console.log('Copied to clipboard:', value ?? '')
      })
      .catch((err) => {
        console.error('Failed to copy:', err)
      })
  }

  const variant = {
    closed: { scale: 0.8, display: 'none' },
    open: { scale: 1, display: 'block' },
  }
  return (
    <>
      <div
        className='flex w-auto'
        style={{
          // pointerEvents: pickerdisabled ? 'none' : 'auto',
          cursor: pickerdisabled ? 'not-allowed' : 'auto',
          opacity: pickerdisabled ? '0.5' : '1',
        }}
      >
        {pickerType === 'Button' && (
          <Button
            className={`${isOpen && 'bg'} edit`}
            style={{
              pointerEvents: pickerdisabled ? 'none' : 'auto',
              // cursor: pickerdisabled ? 'not-allowed' : 'auto',
              // opacity: pickerdisabled ? '0.5' : '1',
            }}
            onClick={() => {
              onOpen()
              setIsOpen(true)
              // setSelectedRow(`dimension${i + 1}`)
            }}
          >
            Pick a Color
          </Button>
        )}
        {pickerType === 'Colorama' && (
          <div
            className={`${isOpen && 'bg'} edit`}
            style={{
              pointerEvents: pickerdisabled ? 'none' : 'auto',
              // //pointerEvents: pickerdisabled ? 'none' : 'auto',
              // cursor: pickerdisabled ? 'not-allowed' : 'auto',
              // opacity: pickerdisabled ? '0.5' : '1',
            }}
            onClick={() => {
              onOpen()
              setIsOpen(true)
              // setSelectedRow(`dimension${i + 1}`)
            }}
          >
            <div
              className='color'
              style={{
                background: activeColor ?? '#333',
                // chartData?.colors?.[`dimension${i + 1}`],
              }}
            ></div>
          </div>
        )}
      </div>

      <TheAnimatedNode
        as='div'
        animatedProps={
          pickerType !== 'Standalone'
            ? {
                initial: { scale: 0.8, display: 'none' }, // Initial state (closed)
                animate: isOpen ? 'open' : 'closed',
                variants: variant,
                transition: { duration: 0.5, ease: [0.68, -0.55, 0.27, 1.55] },
              }
            : {}
        }
        htmlAttr={{
          className:
            pickerType !== 'Standalone'
              ? `${styles.colorPickerRoot}`
              : styles.colorPickerRootStandalone,
        }}
      >
        {pickerType !== 'Standalone' && (
          <CloseIcon
            variant='secondary'
            className={styles.closeIcon}
            onClick={() => {
              togglePopup()
            }}
          />
        )}
        {!isHeaderHidden && (
          <div className={styles.colorPickerHeader}>
            <p className='font-bold'>
              Selecting colour for {heading ?? 'Heading 1'}
            </p>
          </div>
        )}

        <div className={styles.colorPickerRootInner}>
          <div className={styles.colorPickerTop}>
            {colorsValues.slice(0, 4).map((item, i) => (
              <div className={`${disabled?.[item?.name] && styles.disabled}`}>
                <p className={styles.colorTitle}>{item.name}</p>
                <div
                  className={`${styles.colorRoot} ${styles.cursorPointerNone}`}
                >
                  <div
                    className={`${
                      i === 2 ? styles.colorTilesNeutral : styles.colorTiles
                    } ${
                      i === 3 ? styles.colorTilesGradient : styles.colorTiles
                    }`}
                  >
                    {item.colors.map((color, j) => (
                      <>
                        {activeColorIndex?.categoryIndex === i &&
                          activeColorIndex?.colorIndex === j &&
                          Boolean(color.variants?.length) && (
                            <>
                              <ColorPickerTooltip
                                left={i >= 1 ? (i >= 3 ? -35 : -70) : 0}
                                // top={46}
                                variants={color.variants}
                                activeColorIndex={activeColorIndex}
                                setActiveColorIndex={setActiveColorIndex}
                                setSelectedColor={setSelectedColor}
                                setSearchValue={setSearchValue}
                              />
                              <TheAnimatedNode
                                as={'div'}
                                animatedProps={{
                                  variants,
                                  initial: 'inactive',
                                  animate: 'active',
                                  exit: 'inactive',
                                }}
                                htmlAttr={{
                                  className: styles.tooltipTriangle,
                                  style: { left: j * 35 + 9 },
                                }}
                              ></TheAnimatedNode>
                            </>
                          )}

                        <div
                          className={` ${styles.colorSwatch} 
                        ${j === 0 ? styles.firstChild : ''}
                        ${
                          activeColorIndex?.categoryIndex === i &&
                          activeColorIndex?.colorIndex === j
                            ? //&& !Boolean(activeColorIndex.variantIndex)
                              styles.rootActiveState
                            : ''
                        }`}
                          style={getColorStyles(color)}
                          onClick={() => {
                            setSearchValue('')
                            setSelectedColor(color)
                            // if (
                            //   activeColorIndex?.categoryIndex === i &&
                            //   activeColorIndex?.colorIndex === j
                            // ) {
                            //   setActiveColorIndex(null)
                            // } else {
                            setActiveColorIndex({
                              categoryIndex: i,
                              colorIndex: j,
                              variantIndex: null,
                            })
                            // }
                          }}
                        ></div>
                      </>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {colorsValues.slice(4).map((item, i) => (
            <div
              className={`${styles.colorPickerBottom} ${
                disabled?.[item?.name] && styles.disabled
              }`}
            >
              <p className={styles.colorTitle}>{item.name}</p>
              <div
                className={`${styles.colorRoot} ${styles.cursorPointerNone}`}
              >
                <div className={styles.colorTiles}>
                  {item.colors.map((color, j) => (
                    <>
                      {activeColorIndex?.categoryIndex === i + 4 &&
                        activeColorIndex?.colorIndex === j &&
                        Boolean(color.variants?.length) && (
                          <>
                            <ColorPickerTooltip
                              left={j > 7 ? 70 : 0}
                              variants={color.variants}
                              activeColorIndex={activeColorIndex}
                              setActiveColorIndex={setActiveColorIndex}
                              setSelectedColor={setSelectedColor}
                              setSearchValue={setSearchValue}
                            />
                            <TheAnimatedNode
                              as={'div'}
                              animatedProps={{
                                variants,
                                initial: 'inactive',
                                animate: 'active',
                                exit: 'inactive',
                              }}
                              htmlAttr={{
                                className: styles.tooltipTriangle,
                                style: { left: j * 35 + 9 },
                              }}
                            ></TheAnimatedNode>
                          </>
                        )}

                      <div
                        className={`${styles.colorSwatch}
                       ${j === 0 ? styles.firstChild : ''}
                       ${
                         item.colors.length === 1 ? `${styles.onlyChild}` : ''
                       } ${
                          activeColorIndex?.categoryIndex === i + 4 &&
                          activeColorIndex?.colorIndex === j
                            ? // !Boolean(activeColorIndex.variantIndex)
                              styles.rootActiveState
                            : ''
                        }`}
                        style={{ backgroundColor: color.hex }}
                        onClick={() => {
                          setSearchValue('')
                          setSelectedColor(color)
                          // if (
                          //   activeColorIndex?.categoryIndex === i + 4 &&
                          //   activeColorIndex?.colorIndex === j
                          // ) {
                          //   setActiveColorIndex(null)
                          // } else {
                          setActiveColorIndex({
                            categoryIndex: i + 4,
                            colorIndex: j,
                            variantIndex: null,
                          })
                          // }
                        }}
                      ></div>
                    </>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className={styles.colorPickerContent}>
          {activeColorIndex &&
          !(
            activeColorIndex?.categoryIndex === null &&
            activeColorIndex?.colorIndex === null &&
            activeColorIndex?.variantIndex === null
          ) ? (
            <div className={styles.colorPickerContentInner}>
              <div className={styles.colorPickerValue}>
                {/* <div className={styles.colorPreviewBoxRoot}> */}
                <ReactTooltip
                  id='color-preview-box'
                  style={{
                    backgroundColor: '#000',
                    zIndex: '6',
                  }}
                />

                <TheAnimatedNode
                  key={JSON.stringify(selectedColor)}
                  as='div'
                  animatedProps={{
                    initial: getColorStyles(currentColor),
                    animate: getColorStyles(selectedColor),
                    transition: {
                      background: { duration: 0.5, ease: 'easeInOut' },
                    },
                  }}
                  htmlAttr={{
                    className: styles.colorPreviewBox,
                    'data-tooltip-id': 'color-preview-box',
                    'data-tooltip-content': 'Copy all swatch details',
                    onClick: () => {
                      const color = { ...selectedColor }
                      if (color && color.variants) {
                        delete color.variants
                      }
                      copyToClipboard(JSON.stringify(color))
                    },
                  }}
                ></TheAnimatedNode>

                {/* </div> */}

                <div className={styles.colorPickerText}>
                  <div className={styles.colorPickerTextInner}>
                    <ReactTooltip
                      id='name-tooltip'
                      style={{
                        backgroundColor: '#000',
                        zIndex: '6',
                      }}
                    />
                    <span
                      data-tooltip-id='name-tooltip'
                      data-tooltip-content='Copy Name'
                      onClick={() => {
                        copyToClipboard(selectedColor?.name)
                      }}
                      className={`${styles.value} ${
                        selectedSpan === selectedColor?.name
                          ? styles.active
                          : ''
                      }`}
                    >
                      {selectedColor?.name}
                    </span>

                    <ReactTooltip
                      id='codeName-tooltip'
                      style={{
                        backgroundColor: '#000',
                        zIndex: '6',
                      }}
                    />
                    <span
                      data-tooltip-id='codeName-tooltip'
                      data-tooltip-content='Copy Class Name'
                      onClick={() => {
                        copyToClipboard(selectedColor?.codeName)
                      }}
                      className={`${styles.value} ${
                        selectedSpan === selectedColor?.codeName
                          ? styles.active
                          : ''
                      }`}
                    >
                      {selectedColor?.codeName}
                    </span>

                    {/* {selectedColor?.hex && ( */}
                    <>
                      <ReactTooltip
                        id='hex-tooltip'
                        style={{
                          backgroundColor: '#000',
                          zIndex: '6',
                        }}
                      />

                      <span
                        data-tooltip-id='hex-tooltip'
                        data-tooltip-content='Copy Hex'
                        onClick={() => {
                          if (selectedColor?.gradientColors?.length) {
                            copyToClipboard(
                              `${selectedColor?.gradientColors?.[0]?.hex} / ${selectedColor?.gradientColors?.[1]?.hex}`
                            )
                          } else {
                            copyToClipboard(selectedColor?.hex)
                          }
                        }}
                        className={`${styles.value} ${
                          selectedColor?.gradientColors
                            ? styles.gradientText
                            : styles.value
                        }  ${
                          selectedSpan === selectedColor?.hex ||
                          selectedSpan ===
                            `${selectedColor?.gradientColors?.[0]?.hex} / ${selectedColor?.gradientColors?.[1]?.hex}`
                            ? styles.active
                            : ''
                        }`}
                      >
                        {selectedColor?.gradientColors?.length ? (
                          <span>
                            {selectedColor?.gradientColors?.[0]?.hex}
                            <span
                              className={'cn4'}
                              style={{ paddingLeft: '3px' }}
                            >
                              /
                            </span>{' '}
                            {selectedColor?.gradientColors?.[1]?.hex}
                          </span>
                        ) : (
                          selectedColor?.hex
                        )}
                      </span>
                    </>
                    {/* )} */}

                    {/* {selectedColor?.rgb && ( */}
                    <>
                      <ReactTooltip
                        id='rgb-tooltip'
                        style={{
                          backgroundColor: '#000',
                          zIndex: '6',
                        }}
                      />
                      <span
                        data-tooltip-id='rgb-tooltip'
                        data-tooltip-content='Copy RGB'
                        onClick={() => {
                          if (selectedColor?.gradientColors?.length) {
                            copyToClipboard(
                              `${cleanRgb(
                                selectedColor?.gradientColors?.[0]?.rgb
                              )} / ${cleanRgb(
                                selectedColor?.gradientColors?.[1]?.rgb
                              )}`
                            )
                          } else {
                            copyToClipboard(cleanRgb(selectedColor?.rgb))
                          }
                        }}
                        className={`${styles.value} ${
                          selectedColor?.gradientColors
                            ? styles.gradientText
                            : styles.value
                        } ${
                          selectedSpan === cleanRgb(selectedColor?.rgb) ||
                          selectedSpan ===
                            `${cleanRgb(
                              selectedColor?.gradientColors?.[0]?.rgb
                            )} / ${cleanRgb(
                              selectedColor?.gradientColors?.[1]?.rgb
                            )}`
                            ? styles.active
                            : ''
                        }`}
                      >
                        {selectedColor?.gradientColors?.length ? (
                          <span>
                            {cleanRgb(selectedColor?.gradientColors?.[0]?.rgb)}
                            <span
                              className={'cn4'}
                              style={{ paddingLeft: '3px' }}
                            >
                              /
                            </span>{' '}
                            {cleanRgb(selectedColor?.gradientColors?.[1]?.rgb)}
                          </span>
                        ) : (
                          cleanRgb(selectedColor?.rgb)
                        )}
                      </span>
                    </>
                    {/* )} */}
                  </div>

                  <ReactTooltip
                    id='paste-tooltip'
                    style={{
                      backgroundColor: '#000',
                      zIndex: '6',
                    }}
                  />
                  <div className={styles.colorPickerSearchRoot}>
                    <div
                      className={styles.search}
                      data-tooltip-id='paste-tooltip'
                      data-tooltip-content='Paste Name, ClassName, HEX or RGB to identify the brand swatch.'
                    >
                      <Icon
                        as={BsClipboard2MinusFill}
                        style={{ fill: '#c6c6c6' }}
                      />
                      <label htmlFor='hex'>
                        <input
                          type='text'
                          className={'input'}
                          // placeholder='...'
                          value={searchValue}
                          name='hex'
                          onChange={(e) => {
                            setSearchValue(e.target.value)
                          }}
                        />
                      </label>
                    </div>
                    <div className={styles.button} onClick={togglePopup}>
                      {!isApplyButtonHidden && (
                        <button
                          className='flex gap-1'
                          onClick={() => {
                            if (selectedColor && onChange) {
                              onChange(selectedColor)
                            }
                          }}
                        >
                          <span>{selectedColor?.name}</span>
                          <DoneIcon
                            variant={'secondary'}
                            className={styles.icon}
                            onClick={() => {
                              togglePopup()
                            }}
                          />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className={styles.colorPickerFooter}>
              {searchValue === '' ? (
                <p className={'fs2'}>
                  Please choose a brand swatch to continue.
                </p>
              ) : (
                <p>Brand swatch not found.</p>
              )}
              <ReactTooltip
                id='paste-tooltip'
                style={{
                  backgroundColor: '#000',
                  zIndex: '6',
                }}
              />
              <div
                className={styles.search}
                data-tooltip-id='paste-tooltip'
                data-tooltip-content='Paste Name, ClassName, HEX or RGB to identify the brand swatch.'
              >
                {/* <GenericIcon icon={'CopyToClipboard'} iconColour={'cn4'} /> */}
                <Icon as={BsClipboard2MinusFill} style={{ fill: '#c6c6c6' }} />
                <label htmlFor='hex'>
                  <input
                    type='text'
                    className={styles.input}
                    value={searchValue}
                    // placeholder='...'
                    name='hex'
                    onChange={(e) => {
                      setSearchValue(e.target.value)
                    }}
                  />
                </label>
              </div>
            </div>
          )}
        </div>
      </TheAnimatedNode>
    </>
  )
}
