.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
  background-color: #fefefe;
  // margin: 15% auto;
  // margin: 10% auto;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding-block: 10px;
  padding-inline: 5px;
  border: 1px solid #888;
  width: 50%;
  box-shadow: 0 4px 6px 0 rgba(0, 0, 0, 0.2);
  border-radius: 12px;
}

.modal-header {
  padding: 10px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close {
  color: #000;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover,
.close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
}

.modal-body {
  padding: 2px 16px;
}

.modal-footer {
  padding: 10px 16px;
  text-align: right;
  background-color: white;
  color: #5cb85c;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  gap: 10px;
}

button {
  border: none;
  padding: 10px;
  margin: 5px;
  cursor: pointer;
  color: white;
  background-color: #4caf50;
}

button:hover {
  opacity: 0.8;
}