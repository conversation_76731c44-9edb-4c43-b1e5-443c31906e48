import React from 'react'

function UpcomingFeatComp({
  title,
  desc,
  bgColor = '#eff6ff',
  borderColor = '#bfdbfe',
  color = '#1e40af',
  about,
  aboutTitle,
  subtitle = 'Under Development',
}: {
  title: string
  desc: string
  borderColor?: string
  bgColor?: string
  color?: string
  about?: string
  aboutTitle?: string
  subtitle?: string
}) {
  return (
    <div className='tabPanelRoot upcomingFeatureRoot'>
      <div
        id='alert-additional-content-1'
        role='alert'
        style={{
          padding: '16px',
          border: `1px solid ${borderColor}`,
          borderRadius: '8px',
          backgroundColor: bgColor,
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'start',
            flexDirection: 'column',
            color: color,
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <svg
              style={{
                flexShrink: 0,
                width: '16px',
                height: '16px',
                marginRight: '8px',
              }}
              aria-hidden='true'
              xmlns='http://www.w3.org/2000/svg'
              fill='currentColor'
              viewBox='0 0 20 20'
            >
              <path d='M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z' />
            </svg>
            <span className=''>{subtitle}</span>
          </div>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '500' }}>{title}</h3>
        </div>
        <div
          style={{
            marginTop: '8px',
            marginBottom: '16px',
            fontSize: '0.875rem',
          }}
        >
          <div dangerouslySetInnerHTML={{ __html: desc }}></div>
        </div>
      </div>
      {/* <h6
        style={{
          fontSize: '16px',
          marginTop: '30px',
        }}
      >
        About {title}
      </h6> */}
      {/* <p>{about}</p> */}
    </div>
  )
}

export default UpcomingFeatComp
