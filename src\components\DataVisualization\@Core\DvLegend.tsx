import {
  Box,
  Checkbox,
  FormControl,
  Select,
  Spinner,
  Table,
  Tabs,
  Tooltip,
} from '@contentful/f36-components'
import { useSDK } from '@contentful/react-apps-toolkit'
import { Flex } from 'antd'
import { EditorAppSDK } from 'contentful-ui-extensions-sdk'
import React, { useContext, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { GlobalContext } from '../../../contexts/globalContext'
import { RootState } from '../../../redux/store'
import ColorsInputV2 from '../../ColorsInputV2'
import { getColorStyles } from '../../ColorsInputV2/utils'
import { defaults, getDimensions } from '../utils'
import FileUpload from './FileUpload'

function DvLegend(props: any) {
  const {
    onChange,
    changedData,
    template,
    isGlobal,
    asset,
    setDataLoading,
    dataLoading,
    axesData,
  } = props
  const sdk = useSDK<EditorAppSDK>()
  const { currentLocale } = useContext(GlobalContext)
  const [selectedRow, setSelectedRow] = useState('')
  const [chartData, setChartData] = useState<any>({})
  const [headers, setHeader] = useState<any>([])
  const [axes, setAxes] = useState<any>({})
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )

  useEffect(() => {
    const fetch = async () => {
      setDataLoading(true)
      const preferenceData =
        dvGlobalData?.DataVisualization?.[template]?.['chart'] ??
        defaults?.[template]?.['chart']
      const axesPreferences =
        dvGlobalData?.DataVisualization?.[template]?.['axes'] ??
        defaults?.[template]?.['axes']
      setAxes((pre) => {
        return {
          ...pre,
          ...axesPreferences,
          ...axesData,
        }
      })
      const dimension = await getDimensions(
        template,
        asset,
        isGlobal,
        currentLocale
      )
      setHeader(dimension)
      setChartData((pre) => {
        return {
          ...pre,
          ...preferenceData,
          ...changedData,
          asset: asset,
          colors: { ...preferenceData?.colors, ...(changedData?.colors ?? {}) },
          chartType: {
            ...preferenceData?.chartType,
            ...(changedData?.chartType ?? {}),
          },
          'x-axis': {
            ...preferenceData?.['x-axis'],
            ...(changedData?.['x-axis'] ?? {}),
          },
          'y-axis': {
            ...preferenceData?.['y-axis'],
            ...(changedData?.['y-axis'] ?? {}),
          },
          activeLegends: changedData?.activeLegends
            ? changedData?.activeLegends
            : dimension,
          lineStyle: {
            ...preferenceData?.lineStyle,
            ...(changedData?.lineStyle ?? {}),
          },
        }
      })
      setDataLoading(false)
    }
    fetch()
  }, [dvGlobalData, changedData, axesData])

  const handleChartData = async (key: string, value: any) => {
    setDataLoading(true)
    if (key === 'chartType') {
      const changedDataToUpdate = {
        ...changedData,
        chartType: {
          ...(changedData?.chartType ?? {}),
          [`dimension${value?.i + 1}`]: value?.event?.target?.value,
        },
      }
      onChange({ ...changedDataToUpdate })

      setChartData((pre) => {
        return {
          ...pre,
          chartType: {
            ...(pre?.chartType ?? {}),
            [`dimension${value?.i + 1}`]: value?.event?.target?.value,
          },
        }
      })
    } else if (key === 'x-axis') {
      const type =
        axesData?.['x-axis']?.[Number(value.event.target.value) - 1]?.type
      const changedDataToUpdate = {
        ...changedData,
        'x-axis': {
          ...(changedData?.['x-axis'] ?? {}),
          [`dimension${value.i + 1}`]: value.event.target.value,
        },
      }
      onChange({ ...changedDataToUpdate })

      setChartData((pre) => {
        return {
          ...pre,
          'x-axis': {
            ...(pre?.['x-axis'] ?? {}),
            [`dimension${value.i + 1}`]: value.event.target.value,
          },
        }
      })
    } else if (key === 'y-axis') {
      const type =
        axesData?.['y-axis']?.[Number(value.event.target.value) - 1]?.type
      const changedDataToUpdate = {
        ...changedData,
        'y-axis': {
          ...(changedData?.['y-axis'] ?? {}),
          [`dimension${value.i + 1}`]: value.event.target.value,
        },
      }
      onChange({ ...changedDataToUpdate })

      setChartData((pre) => {
        return {
          ...pre,
          'y-axis': {
            ...(pre?.['y-axis'] ?? {}),
            [`dimension${value.i + 1}`]: value.event.target.value,
          },
        }
      })
    } else if (key === 'colors') {
      const changedDataToUpdate = {
        ...changedData,
        colors: {
          ...(changedData?.colors ?? {}),
          [selectedRow]: value,
        },
      }
      onChange({ ...changedDataToUpdate })
      setChartData((pre) => {
        return {
          ...pre,
          colors: {
            ...(pre?.colors ?? {}),
            [selectedRow]: value,
          },
        }
      })
    } else if (key === 'activeLegends') {
      let updatedLegends = [...(chartData.activeLegends || [])]

      if (value?.checked) {
        // Add the legend name if it's not already in the array
        if (!updatedLegends.includes(value?.name)) {
          updatedLegends.push(value?.name)
        }
      } else {
        // Remove the legend name if it's in the array
        updatedLegends = updatedLegends.filter(
          (legend) => legend !== value?.name
        )
      }

      // Only update if the legends have changed
      if (updatedLegends.toString() !== chartData.activeLegends.toString()) {
        const changedDataToUpdate = {
          ...changedData,
          activeLegends: updatedLegends,
        }

        onChange({ ...changedDataToUpdate })
        setChartData((pre) => {
          return {
            ...pre,
            activeLegends: updatedLegends,
          }
        })
      }
    } else if (isGlobal && key === 'asset') {
      setIsLoading(true)
      const changedDataToUpdate = {
        ...changedData,
        asset: value,
      }
      const dimension = await getDimensions(
        template,
        value,
        isGlobal,
        currentLocale
      )
      setHeader(dimension)
      setChartData((pre) => {
        return {
          ...pre,
          asset: value,
        }
      })

      onChange({ ...changedDataToUpdate })
      setDataLoading(false)
    } else if (key === 'lineStyle') {
      const changedDataToUpdate = {
        ...changedData,
        lineStyle: {
          ...(changedData?.lineStyle ?? {}),
          [`dimension${value?.i + 1}`]: value?.event?.target?.value,
        },
      }
      onChange({ ...changedDataToUpdate })

      setChartData((pre) => {
        return {
          ...pre,
          lineStyle: {
            ...(pre?.lineStyle ?? {}),
            [`dimension${value?.i + 1}`]: value?.event?.target?.value,
          },
        }
      })
    } else {
      const dataToUpdate = {
        ...chartData,
        [key]: value,
      }
      const changedDataToUpdate = {
        ...changedData,
        [key]: value,
      }

      setChartData(dataToUpdate)
      onChange({ ...changedDataToUpdate })
    }

    setIsLoading(false)
  }

  return (
    <Tabs.Panel id='dv-chart' className='tabPanelDiv'>
      <Box
        style={{
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          width: '100%',
          height: '100%',
          flexDirection: 'column',
          gap: '20px',
        }}
      >
        {isGlobal && (
          <FileUpload
            asset={asset}
            onAction={(asset) => handleChartData('asset', asset)}
            sdk={sdk}
            setDataLoading={setDataLoading}
            dataLoading={dataLoading}
          />
        )}
        <Box
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'start',
            flexDirection: 'column',
            alignItems: 'start',
            gap: '10px'
          }}
        >
          <Flex className='w-100' gap={30}>
            {template !== 'BarRace chart' && <FormControl id='pri-dv-legend' className='w-50 fieldsFormControl'>
            <div className='SwithWithTooltip'>
              <Checkbox
                name='show dv legend'
                id='show-dv-pri-legend'
                onChange={() =>
                  handleChartData('showLegend', !chartData.showLegend)
                }
                className='switchRoot'
                isChecked={chartData.showLegend}
              >
                Enable legend
              </Checkbox>
            </div>
            </FormControl>}
            {(template === 'Line chart' || template === 'LineRace chart' || template === 'Combo chart') && <>
              <FormControl id='smooth vs sharp line' className='w-25 fieldsFormControl'>
                <div className='SwithWithTooltip'>
                  <Checkbox
                    name='smooth vs sharp line'
                    id='smooth vs sharp line'
                    onChange={() =>
                      handleChartData(
                        'isSmooth',
                        !chartData?.isSmooth
                      )
                    }
                    className='switchRoot'
                    isChecked={chartData?.isSmooth}
                  >
                    Enable Smooth
                  </Checkbox>
                </div>
              </FormControl>
              <FormControl id='pri-dv-tooltip' className='w-25 fieldsFormControl'>
                <div className='SwithWithTooltip'>
                  <Checkbox
                    name='show dv tooltip'
                    id='show-dv-pri-tooltip'
                    onChange={() =>
                      handleChartData(
                        'isEndLable',
                        !chartData?.isEndLable
                      )
                    }
                    className='switchRoot'
                    isChecked={chartData?.isEndLable}
                  >
                    Enable End Label
                  </Checkbox>
                </div>
              </FormControl></>}
          </Flex>
          {template !== 'BarRace chart' && (chartData?.showLegend && (
            <Flex className='w-100' gap={30}>
              <FormControl
                id='dv-pri-legend-orientation'
                className='w-100 fieldsFormControl'
              >
                <FormControl.Label>Select legend orientation</FormControl.Label>
                <Select
                  id='dv-pri-title-orientation-controlled'
                  name='dv-pri-title-orientation-controlled'
                  value={chartData.legendOrientation || ''}
                  onChange={(e) =>
                    handleChartData('legendOrientation', e.target.value)
                  }
                >
                  <Select.Option value='' isDisabled>
                    Legend orientation
                  </Select.Option>
                  {['horizontal', 'vertical'].map((option) => (
                    <Select.Option value={option}>{option}</Select.Option>
                  ))}
                </Select>
              </FormControl>
              <FormControl
                id='dv-pri-legend-Position'
                className='w-100 fieldsFormControl'
              >
                <FormControl.Label>Select legend alignment</FormControl.Label>
                <Select
                  id='dv-pri-legend-Position-controlled'
                  name='dv-pri-legend-Position-controlled'
                  value={chartData.legendPosition || ''}
                  onChange={(e) =>
                    handleChartData('legendPosition', e.target.value)
                  }
                >
                  <Select.Option value='' isDisabled>
                    Legend alignment
                  </Select.Option>
                  {chartData.legendOrientation === 'horizontal'
                    ? ['top-center', 'bottom-center'].map((option) => (
                        <Select.Option value={option}>{option}</Select.Option>
                      ))
                    : ['top-right', 'bottom-right'].map((option) => (
                        <Select.Option value={option}>{option}</Select.Option>
                      ))}
                </Select>
              </FormControl>
            </Flex>
          ))}
        </Box>
          <div className='row'>
            {' '}
            {isLoading ? (
              <Spinner customSize={20} />
            ) : (
              <Table border={0}>
                <Table.Head>
                  <Table.Row>
                    <Table.Cell>Legend</Table.Cell>
                      {(template !== 'Treemap chart' && template !== 'BarStackNormalization chart') && <Table.Cell>Enable</Table.Cell>}
                      <Table.Cell>Colour</Table.Cell>
                    {template === 'Combo chart' && (
                      <Table.Cell>Type of Chart</Table.Cell>
                    )}
                      {(template === 'Line chart' || template === 'LineRace chart') && <Table.Cell> line style</Table.Cell>}
                    {template !== 'Pie chart' &&
                      template !== 'Doughnut chart' &&
                      template !== 'Treemap chart' && (
                        <>
                          <Table.Cell>xAxis</Table.Cell>
                          <Table.Cell>yAxis</Table.Cell>
                        </>
                      )}
                  </Table.Row>
                </Table.Head>
                  <Table.Body>
                  {headers.map((name, i) => {
                    const raisingIndex = i * 2 + 1 // for Candlestick chart Raising starts with 1, 3, 5...
                    const fallingIndex = i * 2 + 2 // Falling starts with 2, 4, 6...
                    return (
                      <Table.Row key={name}>
                        <Table.Cell>{name}</Table.Cell>
                        {(template !== 'Treemap chart' && template !== 'BarStackNormalization chart') && <Table.Cell>
                          {' '}
                            <Checkbox
                              name='show dv activeLegends'
                              id='show-dv-pri-activeLegends'
                              onChange={(event) =>
                                handleChartData('activeLegends', {
                                  name,
                                  checked: event.target.checked,
                                })
                              }
                              style={{ padding: '0.5rem 1rem' }}
                              isChecked={chartData.activeLegends?.includes(
                                name
                              )}
                          ></Checkbox>
                        </Table.Cell>}
                        <Table.Cell>
                          {template === 'Candlestick chart' ? (
                            <>
                              <Tooltip
                                placement='top'
                                id='dv-pri-candle-stick-color-tooltip'
                                content={
                                  'Select color for bullish candle stick.'
                                }
                              >

                                <ColorsInputV2
                                  heading={`${name}- bullish candle stick.`}
                                  onClose={() => {
                                    setSelectedRow('')
                                  }}
                                  onOpen={() => {
                                    setSelectedRow(`dimension${raisingIndex}`)
                                  }}
                                  activeColor={
                                    chartData?.colors?.[
                                      `dimension${raisingIndex}`
                                    ]
                                  }
                                  onChange={(v: any) => {
                                    const color = getColorStyles(v).background
                                    handleChartData('colors', color)

                                    // setShowColorPicker(false)
                                  }}
                                  pickerType='Colorama'
                                  // pickerType='Button'
                                  disabled={
                                    {
                                      // Accents: true,
                                      // Primaries: true,
                                      // Gradients: true,
                                      // Neutrals: true,
                                      // Secondaries: true,
                                    }
                                  }
                                  pickerdisabled={
                                    selectedRow &&
                                    selectedRow !== `dimension${raisingIndex}`
                                  }
                                />
                              </Tooltip>
                              <Tooltip
                                placement='top'
                                id='dv-pri-candle-stick-color-tooltip'
                                content={
                                  'Select color for bearish candle stick'
                                }
                              >

                                <ColorsInputV2
                                  heading={`${name}-bearish candle stick`}
                                  onClose={() => {
                                    setSelectedRow('')
                                  }}
                                  onOpen={() => {
                                    setSelectedRow(`dimension${fallingIndex}`)
                                  }}
                                  activeColor={
                                    chartData?.colors?.[
                                      `dimension${fallingIndex}`
                                    ]
                                  }
                                  onChange={(v: any) => {
                                    const color = getColorStyles(v).background
                                    handleChartData('colors', color)

                                    // setShowColorPicker(false)
                                  }}
                                  pickerType='Colorama'
                                  // pickerType='Button'
                                  disabled={
                                    {
                                      // Accents: true,
                                      // Primaries: true,
                                      // Gradients: true,
                                      // Neutrals: true,
                                      // Secondaries: true,
                                    }
                                  }
                                  pickerdisabled={
                                    selectedRow &&
                                    selectedRow !== `dimension${fallingIndex}`
                                  }
                                />
                              </Tooltip>
                            </>
                          ) : (
                              <>{template === 'Treemap chart' ? <ColorsInputV2
                                heading={`${name}`}
                                onClose={() => {
                                  setSelectedRow('')
                                }}
                                onOpen={() => {
                                  setSelectedRow(`dimension${name}`)
                                }}
                                activeColor={
                                  chartData?.colors?.[`dimension${name}`] ?? chartData?.colors?.[`dimension${i + 1}`]
                                }
                                onChange={(v: any) => {
                                  const color = getColorStyles(v).background
                                  handleChartData('colors', color)

                                  // setShowColorPicker(false)
                                }}
                                pickerType='Colorama'
                                // pickerType='Button'
                                disabled={{
                                  // Accents: true,
                                  // Primaries: true,
                                  Gradients:
                                    template === 'Treemap chart' ? true : false,
                                  // Neutrals: true,
                                  // Secondaries: true,
                                }}
                                pickerdisabled={
                                  selectedRow &&
                                  selectedRow !== `dimension${name}`
                                }
                              />
                                : <ColorsInputV2
                              heading={`${name}`}
                              onClose={() => {
                                setSelectedRow('')
                              }}
                              onOpen={() => {
                                setSelectedRow(`dimension${i + 1}`)
                              }}
                              activeColor={
                                chartData?.colors?.[`dimension${i + 1}`]
                              }
                              onChange={(v: any) => {
                                const color = getColorStyles(v).background
                                handleChartData('colors', color)

                                // setShowColorPicker(false)
                              }}
                              pickerType='Colorama'
                              // pickerType='Button'
                              disabled={{
                                // Accents: true,
                                // Primaries: true,
                                Gradients:
                                  template === 'Treemap chart' ? true : false,
                                // Neutrals: true,
                                // Secondaries: true,
                              }}
                              pickerdisabled={
                                selectedRow &&
                                selectedRow !== `dimension${i + 1}`
                              }
                              />}
                            </>)}
                        </Table.Cell>

                        {template === 'Combo chart' && (
                          <Table.Cell>
                            <Select
                              style={{ textTransform: 'capitalize' }}
                              id={`optionSelect-${i}`}
                              name={`optionSelect-${i}`}
                              value={
                                chartData?.chartType?.[`dimension${i + 1}`] ||
                                ''
                              }
                              onChange={(event) =>
                                handleChartData('chartType', { event, i })
                              }
                            >
                              <Select.Option value='' isDisabled>
                                Please select an option...
                              </Select.Option>
                              {['bar', 'line'].map((type) => (
                                <Select.Option
                                  key={type}
                                  value={type}
                                  style={{ textTransform: 'capitalize' }}
                                >
                                  {type}
                                </Select.Option>
                              ))}
                            </Select>
                          </Table.Cell>
                        )}
                        {(template === 'Line chart' || template === 'LineRace chart') && (
                          <Table.Cell>
                            <Select
                              id='Linestyle-controlled'
                              name='LineStyle-controlled'
                              value={chartData.lineStyle?.[`dimension${i + 1}`] || ''}
                              onChange={(event) => handleChartData('lineStyle', { event, i })}
                              style={{
                                width: '100%',
                              }}
                            >
                              <Select.Option value='' isDisabled>
                                Please select an option...
                              </Select.Option>
                              {['solid', 'dotted', 'dashed'].map((type) => (
                                <Select.Option value={type}>{type}</Select.Option>
                              ))}
                            </Select>
                          </Table.Cell>
                        )}
                        {template !== 'Pie chart' &&
                          template !== 'Doughnut chart' &&
                          template !== 'Treemap chart' && (
                            <>
                              <Table.Cell>
                                <Select
                                  id={`x-axis optionSelect-${i}`}
                                  name={`x-axis optionSelect-${i}`}
                                  value={
                                    chartData?.['x-axis']?.[
                                      `dimension${i + 1}`
                                    ] || ''
                                  }
                                  onChange={(event) =>
                                    handleChartData('x-axis', {
                                      event,
                                      i,
                                      name,
                                    })
                                  }
                                >
                                  <Select.Option value='' isDisabled>
                                    Please select an option...
                                  </Select.Option>
                                  {Array.from(
                                    {
                                      length:
                                        Number(axesData?.['x-axisCount']) || 1,
                                    },
                                    (_, type) => (
                                      <Select.Option
                                        key={type + 1}
                                        value={type + 1}
                                      >
                                        {type + 1}
                                      </Select.Option>
                                    )
                                  )}
                                </Select>
                              </Table.Cell>
                              <Table.Cell>
                                <Select
                                  id={`y-axis optionSelect-${i}`}
                                  name={`y-axis optionSelect-${i}`}
                                  value={
                                    chartData?.['y-axis']?.[
                                      `dimension${i + 1}`
                                    ] || ''
                                  }
                                  onChange={(event) =>
                                    handleChartData('y-axis', {
                                      event,
                                      i,
                                      name,
                                    })
                                  }
                                >
                                  <Select.Option value='' isDisabled>
                                    Please select an option...
                                  </Select.Option>
                                  {Array.from(
                                    {
                                      length:
                                        Number(axes?.['y-axisCount']) || 1,
                                    },
                                    (_, type) => (
                                      <Select.Option
                                        key={type + 1}
                                        value={type + 1}
                                      >
                                        {type + 1}
                                      </Select.Option>
                                    )
                                  )}
                                </Select>
                              </Table.Cell>
                            </>
                          )}
                      </Table.Row>
                    )
                  })}
                </Table.Body>
              </Table>
            )}
        </div>
      </Box>
    </Tabs.Panel>
  )
}

export default DvLegend
