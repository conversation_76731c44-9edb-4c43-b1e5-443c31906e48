import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Button, Flex, Select, Spinner } from '@contentful/f36-components'
import { useSDK } from '@contentful/react-apps-toolkit'
import React, { useContext, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import RightArrow from '../assets/icons/RightArrow'
import CrossPostingModal from '../components/Crosspost'
import { Domains, domainsConfig, getDomainFullName, getDomainShortName } from '../components/Crosspost/utils'
import DataTable from '../components/DataTable'
import DataTablePreview from '../components/DataTable/DataTablePreview'
import DataVisualization from '../components/DataVisualization'
import Visualizer from '../components/DataVisualization/@Core/Visualizer'
import InputFieldsRenderer from '../components/InputFields/InputFieldsRenderer'
import PageSettingsModal from '../components/PageSettings'
import fieldsConfig, { globalCommonFields } from '../configs/fields.config'
import { GlobalContext } from '../contexts/globalContext'
import { getConfigurationsCollectionQuery } from '../globals/queries'
import { fetchGraphQL, updateCurrentEntry } from '../globals/utils'
import { fetchDVTemplateData, fetchGlobalConfigData } from '../redux/slices/dashboard/dvSlices'
import { fetchTableData } from '../redux/slices/dataTables'
import { fetchCrossPostData, getContentTypesForEnvironment } from '../redux/slices/pageSettings'
import { AppDispatch, RootState } from '../redux/store'

const Entry = () => {
  /*
     To use the cma, inject it as follows.
     If it is not needed, you can remove the next line.
  */
  // const cma = useCMA();

  const sdk = useSDK<EditorAppSDK>()
  const sdkFields = sdk.entry.fields
  const entryId = sdk.ids.entry
  const contentTypeId = sdk.entry.getSys().contentType.sys.id
  const dispatch = useDispatch<AppDispatch>()
  const isDvSource = sdkFields?.['source']?.getValue()
  const isDvTemplate = sdkFields?.['template']?.getValue()
  const isDvData = sdkFields?.['data']?.getValue()
  const isTabelSource = sdkFields?.['source']?.getValue()
  const isTableData = sdkFields?.['data']?.getValue()
  const tableInternalName = sdkFields?.['internalName']?.getValue()
  const [template, setTemplate] = useState()

  const tags = sdk.entry.getMetadata()?.tags

  const [selectedDomain, setSelectedDomain] = useState<string>('')

  const [allowedLocales, setAllowedLocales] = useState<string[]>(['en-CA'])

  const localeFromSession = sessionStorage.getItem('domainLocale')

  // const [globalJson, setGlobalJson] = useState<any>({})

  const pageConfigurations = sdkFields?.['configurations']?.getValue() || {}

  const {
    currentLocale,
    isCrossPostingModalOpen,
    setIsCrossPostingModalOpen,
    chartFileModal,
    setCharFileModal,
    isPageSettingModalOpen,
    setIsPageSettingModalOpen,
    isDataTableModalOpen,
    setIsDataTableModalOpen,
  } = useContext(GlobalContext)

  const dvTemplate: any = useSelector(
    (state: RootState) => state.dvDashboard.dvTemplate,
  )
  const dvChartId: any = useSelector(
    (state: RootState) => state.dvDashboard.dvChartId,
  )
  const dvAsset: any = useSelector(
    (state: RootState) => state.dvDashboard.dvAsset,
  )
  const dvUpdatedData: any = useSelector(
    (state: RootState) => state.dvDashboard.dvUpdatedData,
  )
  const tableAsset: any = useSelector(
    (state: RootState) => state.tableData.tableAsset,
  )
  const tableData: any = useSelector(
    (state: RootState) => state.tableData.tableData,
  )

  // const entryCrossPostData = useSelector(
  //   (state: RootState) => state.pageData.crossPostData,
  // )

  /**
   * Fetches the allowed languages for the given domain from the 'configurations' collection.
   * @param {string} selectedDomain The domain for which to fetch the allowed languages.
   */
  const fetchData = async (selectedDomain: string) => {
    const res = await fetchGraphQL(getConfigurationsCollectionQuery()).then(
      (res: any) => res?.data?.configurationsCollection?.items,
    )

    const matchedData = res?.find((item: any) => {
      return (
        item?.scope === selectedDomain?.toUpperCase() && item?.type === 'Locale'
      )
    })?.data?.json?.content?.[0]?.content?.[0]?.value

    if (!matchedData) return

    const data = JSON.parse(matchedData)
    const allowedLanguages = data?.allowedLanguages || ['en-CA']
    setAllowedLocales(allowedLanguages)
  }

/**
 * Replaces the domain tag in the entry's metadata with a new domain.
 * 
 * @param {string} value - The domain name to replace the existing domain tag with.
 * @returns {Array} - An array of updated tags with the new domain tag included.
 */

  const handleReplaceDomain = (value: string) => {
    const domainArray = tags?.filter((tag) => tag.sys.id.includes('domain'))
    if (domainArray && domainArray?.length > 0) {
      domainArray[0].sys.id = getDomainFullName(value as Domains)
    }

    const nonDomainArray = tags?.filter((tag) => !tag.sys.id.includes('domain'))
    if (nonDomainArray && domainArray) {
      const newTags = [...nonDomainArray, ...domainArray]
      return newTags
    }

    return []
  }

  /**
   * Creates a new domain tag and adds it to the existing tags in the entry's metadata.
   * If no tags exist, it will create a new array with the domain tag.
   * 
   * @param {string} value - The domain name to create the tag for.
   * @returns {Array} - An array of tags including the new domain tag.
   */
  const handleAddDomain = (value: string) => {
    const domainTag = {
      sys: {
        type: 'Link',
        linkType: 'Tag',
        id: getDomainFullName(value as Domains),
      },
    }

    const newTags = tags ? [...tags, domainTag] : [domainTag]

    return newTags
  }

  /**
   * Handles updating the domain name in the entry's metadata and in the page's configurations data.
   * 
   * @param {string} value - The domain name to update to.
   * @returns {Promise<void>} - A promise that resolves after the entry's metadata and configurations data have been updated.
   */
  const handleUpdateDomain = async (value: string) => {
    setSelectedDomain(value)
    let updatedTags
    const x = {
      ...pageConfigurations,
      domain: value,
    }


    const isDomainTagPresent = tags?.find((tag) => tag.sys.id.includes('domain'))

    if (!isDomainTagPresent)
      updatedTags = handleAddDomain(value)
    else updatedTags = handleReplaceDomain(value)

    await updateCurrentEntry(entryId, updatedTags, {})

    sdkFields['configurations'].setValue(x)
  }

  useEffect(() => {
    // listen to template change
    sdkFields['template']?.onValueChanged((value) => {
      setTemplate(value)
    })

    if (sdkFields['template'] && contentTypeId !== 'dataVisualization') {
      !globalCommonFields.includes('template') &&
      globalCommonFields.push('template')
    }
  }, [sdkFields])

  useEffect(() => {

    // get the domain from the entry's tags and configurations and set the selected domain

    if (contentTypeId !== 'page' && contentTypeId !== 'componentErrorPage')
      return

    dispatch(getContentTypesForEnvironment())

    const pageTags = sdk.entry.getMetadata()?.tags || []

    let pageDomains = pageTags
      ?.filter((item) => {
        return item.sys.id.includes('domain')
      })
      .map((item) => getDomainShortName(item.sys.id))

    const domainFromConf = sdkFields['configurations']?.getValue()?.domain

    pageDomains = Array.from(new Set([domainFromConf, ...pageDomains])).filter(
      (item) => item,
    )

    setSelectedDomain(pageDomains[0])
  }, [sdk.entry, sdkFields, contentTypeId])

  useEffect(() => {
    // fetch the allowed locales for the selected domain
    selectedDomain && fetchData(selectedDomain)
  }, [selectedDomain])

  useEffect(() => {
    // save the allowed locales in session storage
    allowedLocales.length > 1 &&
    sessionStorage.setItem('domainLocale', JSON.stringify(allowedLocales))
  }, [allowedLocales])

  useEffect(() => {
    const id = contentTypeId === 'dataVisualization' ? entryId : ''
    dispatch(fetchGlobalConfigData())
    dispatch(fetchDVTemplateData({ entryId: id, currentLocale }))
  }, [chartFileModal, dispatch])

  useEffect(() => {
    const id = contentTypeId === 'table' ? entryId : ''
    dispatch(fetchTableData({ entryId: id, currentLocale }))
  }, [isDataTableModalOpen, dispatch])

  useEffect(() => {
    // check if the allowed locales are different from the session storage and update the local state
    if (localeFromSession !== JSON.stringify(allowedLocales)) {
      const parseData = JSON.parse(localeFromSession || 'null')

      setAllowedLocales(parseData || ['en-CA'])
    }
  }, [localeFromSession])

  // useEffect(() => {
  //   dispatch(fetchCrossPostData(entryId))
  // }, [])

  // useEffect(() => {
  //   if (entryCrossPostData && Object.keys(entryCrossPostData).length > 0) {
  //     setGlobalJson(entryCrossPostData)
  //   }
  // }, [entryCrossPostData])

  return (
    <div style={{ width: '70%', margin: '10px auto 20px auto' }}>
      <Flex justifyContent={'start'} gap="16px">
        {contentTypeId === 'page' && isCrossPostingModalOpen && (
          <CrossPostingModal
            sdk={sdk}
            onClose={() => setIsCrossPostingModalOpen(false)}
            entryId={entryId}
          />
        )}

        {contentTypeId === 'page' && (
          <>
            <Button
              variant="primary"
              onClick={() => setIsPageSettingModalOpen(true)}
              endIcon={<RightArrow colour="#fff" />}
            >
              Page Settings
            </Button>
            <Box
              className="flex items-center border border-[#CFD9E0] rounded-[6px] shadow-[inset_0px_2px_0px_rgba(225,228,232,0.2)] px-3">
              <p className="pb-[1px] w-fit">Website:</p>
              <Select
                onChange={(e) => handleUpdateDomain(e.target.value)}
                value={selectedDomain}
                size="small"
                testId="original-domain-select"
                className="customSelect"
              >
                <Select.Option value={''}>Select Domain</Select.Option>
                {domainsConfig?.map((item) => (
                  <Select.Option key={item.key} value={item.key}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            </Box>

            {isPageSettingModalOpen && (
              <PageSettingsModal sdk={sdk} entryId={entryId} />
            )}
          </>
        )}
      </Flex>

      {globalCommonFields.map((fieldId) => (
        <InputFieldsRenderer
          key={`${fieldId}-${'en-CA'}`}
          fieldId={fieldId}
          currentLocale={'en-CA'}
        />
      ))}

      {contentTypeId === 'dataVisualization' &&
        (chartFileModal ? (
          <DataVisualization sdk={sdk} entryId={entryId} />
        ) : (
          <Flex flexDirection="column" gap="15px" style={{ marginTop: '15px' }}>
            {isDvSource && isDvTemplate ? (
              dvAsset ? (
                <Visualizer
                  template={dvTemplate}
                  changedData={dvUpdatedData}
                  id={dvChartId}
                  asset={dvAsset}
                />
              ) : (
                <Spinner customSize={20} />
              )
            ) : (
              <></>
            )}

            <Button
              variant="primary"
              /*  endIcon={<RightArrow colour='#fff' />} */
              size="medium"
              onClick={() => setCharFileModal(true)}
              isDisabled={isDvSource ? (dvAsset ? false : true) : false}
            >
              {!isDvSource && !isDvTemplate && !isDvData
                ? 'Create new'
                : 'Edit'}
            </Button>
          </Flex>
        ))}

      {(template
          ? fieldsConfig[contentTypeId][template]
          : fieldsConfig?.[contentTypeId]?.length > 0
            ? fieldsConfig?.[contentTypeId]
            : Object.keys(sdkFields)
      )?.map((fieldId: string) => {
        if (
          globalCommonFields.includes(fieldId) ||
          fieldId === 'internalName' ||
          fieldId === 'template' ||
          fieldId === 'configurations'
        )
          return null

        return (
          <React.Fragment key={fieldId}>
            {allowedLocales.map((locale) => (
              <InputFieldsRenderer
                key={`${fieldId}-${locale}`}
                fieldId={fieldId}
                currentLocale={locale}
              />
            ))}
          </React.Fragment>
        )
      })}
      {contentTypeId === 'table' &&
        (isDataTableModalOpen ? (
          <DataTable sdk={sdk} entryId={entryId} />
        ) : (
          <Flex flexDirection="column" gap="15px" style={{ marginTop: '15px' }}>
            {isTabelSource ? (
              tableAsset ? (
                <DataTablePreview
                  data={tableData}
                  asset={tableAsset}
                  internalName={tableInternalName}
                />
              ) : (
                <Spinner customSize={20} />
              )
            ) : (
              <></>
            )}

            <Button
              variant="primary"
              size="medium"
              onClick={() => setIsDataTableModalOpen(true)}
              isDisabled={isTabelSource ? (tableAsset ? false : true) : false}
            >
              {!isTabelSource && !isTableData ? 'Create new table' : 'Edit'}
            </Button>
          </Flex>
        ))}
      <div
        style={{
          height: '80px',
        }}
      />
    </div>
  )
}

export default Entry
