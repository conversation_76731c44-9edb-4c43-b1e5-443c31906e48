import React, { useState } from 'react'
import { Button, Input, List, message, Modal, Popconfirm } from 'antd'

const StepParameterValues = ({
  parameterName,
  parameterData,
  setParameterData,
  references,
}: {
  parameterName: string
  parameterData: ParameterDataType
  setParameterData: SetParameterDataType
  references: string[]
}) => {
  const [newValue, setNewValue] = useState('')
  const [isRenameModalVisible, setIsRenameModalVisible] = useState(false)
  const [renameInput, setRenameInput] = useState('')
  const [renameTarget, setRenameTarget] = useState<string | null>(null)

  const handleAdd = () => {
    const val = newValue.trim()
    if (!val) return message.error('Value cannot be empty.')
    if (parameterData[parameterName]?.[val]) {
      return message.error('Value already exists.')
    }

    setParameterData((prev) => ({
      ...prev,
      [parameterName]: { ...(prev[parameterName] || {}), [val]: references },
    }))
    setNewValue('')
  }

  const handleRemove = (key: string) => {
    setParameterData((prev) => {
      const updated = { ...(prev[parameterName] || {}) }
      delete updated[key]
      return { ...prev, [parameterName]: updated }
    })
  }

  const showRenameModal = (key: string) => {
    setRenameTarget(key)
    setRenameInput(key)
    setIsRenameModalVisible(true)
  }

  const handleRename = () => {
    const newKey = renameInput.trim()
    if (!newKey) return message.error('New name cannot be empty.')
    if (parameterData[parameterName]?.[newKey]) {
      return message.error('Name already exists.')
    }

    setParameterData((prev) => {
      const currentParameter = { ...(prev[parameterName] || {}) }
      const value = currentParameter[renameTarget!]
      delete currentParameter[renameTarget!]
      currentParameter[newKey] = value
      return { ...prev, [parameterName]: currentParameter }
    })

    setIsRenameModalVisible(false)
    setRenameTarget(null)
    setRenameInput('')
  }

  const values = Object.keys(parameterData[parameterName] || {})

  return (
    <>
      <Input
        placeholder='Add parameter value (e.g. red)'
        value={newValue}
        onChange={(e) => setNewValue(e.target.value)}
        onPressEnter={handleAdd}
        style={{ marginBottom: 8 }}
      />
      <Button onClick={handleAdd} type='primary'>
        Add
      </Button>

      <List
        header={`Parameter Values for "${parameterName}"`}
        dataSource={values}
        style={{ marginTop: 16 }}
        renderItem={(val) => (
          <List.Item
            actions={[
              <Button size='small' onClick={() => showRenameModal(val)}>
                Rename
              </Button>,
              <Popconfirm
                title='Are you sure you want to remove this value?'
                onConfirm={() => handleRemove(val)}
                okText='Yes'
                cancelText='No'
              >
                <Button size='small' danger>
                  Remove
                </Button>
              </Popconfirm>,
            ]}
          >
            <div>{val}</div>
          </List.Item>
        )}
      />

      <Modal
        title={`Rename "${renameTarget}"`}
        open={isRenameModalVisible}
        onOk={handleRename}
        onCancel={() => setIsRenameModalVisible(false)}
        okText='Save'
      >
        <Input
          value={renameInput}
          onChange={(e) => setRenameInput(e.target.value)}
          placeholder='New name'
        />
      </Modal>
    </>
  )
}

export default StepParameterValues

// import React from 'react'
// import { Button, Input, List, message } from 'antd'
// import { useState } from 'react'

// const StepParameterValues = ({
//   parameterName,
//   parameterData,
//   setParameterData,
//   references,
// }: {
//   parameterName: string
//   parameterData: Record<string, Record<string, string[]>>
//   setParameterData: (data: Record<string, Record<string, string[]>>) => void
//   references: string[]
// }) => {
//   const [newValue, setNewValue] = useState('')

//   const handleAdd = () => {
//     const val = newValue.trim()
//     if (!val) return message.error('Value cannot be empty.')
//     if (parameterData[parameterName]?.[val]) {
//       return message.error('Value already exists.')
//     }

//     setParameterData((prev) => ({
//       ...prev,
//       [parameterName]: { ...(prev[parameterName] || {}), [val]: references },
//     }))
//     setNewValue('')
//   }

//   const values = Object.keys(parameterData[parameterName] || {})

//   return (
//     <>
//       <Input
//         placeholder='Add parameter value (e.g. red)'
//         value={newValue}
//         onChange={(e) => setNewValue(e.target.value)}
//         onPressEnter={handleAdd}
//         style={{ marginBottom: 8 }}
//       />
//       <Button onClick={handleAdd} type='primary'>
//         Add
//       </Button>

//       <List
//         header={`Parameter Values for "${parameterName}"`}
//         dataSource={values}
//         renderItem={(val) => <List.Item>{val}</List.Item>}
//         style={{ marginTop: 16 }}
//       />
//     </>
//   )
// }

// export default StepParameterValues
