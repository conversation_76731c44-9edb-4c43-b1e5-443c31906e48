import { Icon } from '@contentful/f36-components'
import React from 'react'

function LinkOut(props: { colour?: string }) {
  const { colour = 'none' } = props
  return (
    <Icon {...props} size={'tiny'}>
      <svg
        xmlns='http://www.w3.org/2000/svg'
        width='22'
        height='22'
        viewBox='0 0 24 24'
      >
        <path
          fill={colour}
          stroke='currentColor'
          stroke-linecap='round'
          stroke-linejoin='round'
          stroke-width='2'
          d='M13.5 10.5L21 3m-5 0h5v5m0 6v5a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5'
        />
      </svg>
    </Icon>
  )
}

export default LinkOut
