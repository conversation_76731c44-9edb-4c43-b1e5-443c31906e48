// DuplicateComponentButton.tsx
import React, { useState } from 'react'
import { useDuplicateComponent } from '../../../../globals/experimentation-util'
import CustomButton from '../../../Buttons/CustomButton'
import GlobalTable from '../../../Table'
import DuplicateModal from './DuplicateModal'

const DuplicateComponentButton: React.FC = ({
  entryId,
}: {
  entryId: string
}) => {
  const { isLoading, progress, duplicateComponent } = useDuplicateComponent()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedEntryId, setSelectedEntryId] = useState<string>('')

  const columns = [
    { id: 'contentType', label: 'Content Type' },
    { id: 'internalName', label: 'Internal Name' },
    {
      id: 'id',
      label: 'Original Component',
      render: (value, row) => {
        return (
          <a
            href={`https://app.contentful.com/spaces/8jgyidtgyr4v/environments/${process.env.REACT_APP_CONTENTFUL_ENVIRONMENT ?? 'dev'}/entries/${value}`}
            target='_blank'
            className='hover:scale-105 text-[#3355ad] '
          >
            {value}
          </a>
        )
      },
    },
    {
      id: 'newNestedId',
      label: 'Duplicated Component',
      render: (value, row) => {
        return value ? (
          <a
            href={`https://app.contentful.com/spaces/8jgyidtgyr4v/environments/${process.env.REACT_APP_CONTENTFUL_ENVIRONMENT ?? 'dev'}/entries/${value}`}
            target='_blank'
            className='hover:scale-105 text-[#3355ad] '
          >
            {value}
          </a>
        ) : (
          'In Progress'
        )
      },
    },
  ]

  return (
    <div className='h-full w-full flex gap-10'>
      <div className='flex gap-10 items-center justify-center overflow-auto w-full flex-col'>
        <div className='w-full flex justify-start'>
          {/* <button
          onClick={() => {
            setSelectedEntryId(entryId) // Replace with actual entry ID
            setIsModalOpen(true)
          }}
          className='px-4 py-2 bg-blue-500 text-white rounded'
        >
          Open Duplicate Modal
        </button> */}
        </div>
        <GlobalTable
          columns={columns}
          data={progress?.status}
          defaultPageSize={5}
          pageSizeOptions={[5, 10, 15]}
          searchableColumns={['internalName', 'contentType']}
          height='400px'
          // paginationremove
          extraConetntAfterSearch={
            <>
              <div className='w-2/3 flex justify-end gap-5 py-4'>
                {isLoading && (
                  <div className='flex justify-center items-center'>
                    <div className='animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#56e5a9] mb-4'></div>
                  </div>
                )}
                {progress && (
                  <div className=''>
                    <p className='text-xl text-[#339bbc]'>
                      Total Components to Create:{' '}
                      <span className='font-extrabold'>
                        {progress?.status?.length || 0}
                      </span>
                    </p>
                    <p className='text-xl text-[#56e5a9]'>
                      Components Completed:
                      <span className='font-extrabold'>
                        {' '}
                        {progress?.status?.filter((el) => el?.newNestedId)
                          ?.length || 0}
                      </span>
                    </p>
                  </div>
                )}
                <CustomButton
                  onClick={() => {
                    setSelectedEntryId(entryId) // Replace with actual entry ID
                    setIsModalOpen(true)
                  }}

                  // className='px-4 py-0 h-auto bg-[#3355ad] text-white rounded'
                >
                  Clone Component
                </CustomButton>
              </div>
            </>
          }
        />
      </div>
      <DuplicateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        entryId={selectedEntryId}
        isLoading={isLoading}
        progress={progress}
        duplicateComponent={duplicateComponent}
      />
    </div>
  )
}

export default DuplicateComponentButton

// import React, { useCallback, useEffect, useState } from 'react'
// import { useDuplicateComponent } from '../../../../globals/experimentation-util'
// import { getEntryDataById } from '../../../../globals/utils'
// import EntryPreviewById from '../../../EntryPreview/EntryPreviewById'
// import GlobalTable from '../../../Table'

// interface ComponentReference {
//   id: string
// }

// const DuplicateComponentButton = ({ entryId }: { entryId: string }) => {
//   const { isLoading, progress, duplicateComponent } = useDuplicateComponent()
//   console.log(progress, 'Master Progress Log')
//   const [componentReferences, setComponentReferences] = useState<
//     ComponentReference[]
//   >([])
//   const [selectedIdsToDuplicate, setSelectedIdsToDuplicate] = useState<
//     string[]
//   >([])

//   // Utility to extract references recursively from entry fields
//   const extractReferences = (fields: any): ComponentReference[] => {
//     const references: ComponentReference[] = []

//     const traverseFields = (obj: any) => {
//       if (Array.isArray(obj)) {
//         obj.forEach(traverseFields)
//       } else if (typeof obj === 'object' && obj !== null) {
//         if (obj.sys?.type === 'Link' && obj.sys.linkType === 'Entry') {
//           references.push({ id: obj.sys.id })
//         } else {
//           Object.values(obj).forEach(traverseFields)
//         }
//       }
//     }

//     traverseFields(fields)
//     return references
//   }

//   // Fetch component references from the entry fields
//   const fetchComponentReferences = useCallback(async () => {
//     try {
//       const entry = await getEntryDataById(entryId)
//       const references = extractReferences(entry.fields)
//       setComponentReferences(references)
//     } catch (error) {
//       console.error('Failed to fetch component references:', error)
//     }
//   }, [entryId])

//   useEffect(() => {
//     if (entryId) {
//       fetchComponentReferences()
//     }
//   }, [entryId, fetchComponentReferences])

//   // Trigger the duplication process based on the type
//   const performDuplication = useCallback(async () => {
//     if (!entryId) return

//     // Determine the duplication type based on selected components
//     const type = selectedIdsToDuplicate.length > 0 ? 'duplicate' : 'reference'
//     // const selectedIdsToAvoid = componentReferences.filter(el => selectedIdsToDuplicate.findIndex(elp => el.id === elp) === -1)
//     const selectedIdsToAvoid = componentReferences
//       ?.filter(({ id }) => !selectedIdsToDuplicate?.includes(id))
//       ?.map((el) => el?.id)
//     try {
//       const newEntryId = await duplicateComponent(
//         entryId,
//         type,
//         selectedIdsToAvoid
//       )
//       console.log(`Duplication completed (${type}). New entry ID:`, newEntryId)
//     } catch (error) {
//       console.error(`Duplication failed (${type}):`, error)
//     }
//   }, [entryId, duplicateComponent, selectedIdsToDuplicate])

//   // Toggle selected ID to avoid duplication
//   const handleCheckboxChange = (id: string) => {
//     setSelectedIdsToDuplicate((prev) =>
//       prev.includes(id)
//         ? prev.filter((selectedId) => selectedId !== id)
//         : [...prev, id]
//     )
//   }
//   const columns = [
//     { id: 'contentType', label: 'content Type' },
//     { id: 'internalName', label: 'internal Name' },
//     { id: 'id', label: 'Original Component' },
//     { id: 'newNestedId', label: 'Duplicated Component' },
//   ]
//   return (
//     <div className=' h-full w-full flex gap-10'>
//       <div className='flex gap-10 items-center justify-center overflow-auto w-1/2 flex-col '>
//         {isLoading ? (
//           <div className='flex justify-center items-center flex-col'>
//             <div className='flex flex-col items-center'>
//               <div className='animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4'></div>
//             </div>
//             <p className='text-xl text-blue-600'>
//               Total Components to Create and Counting:{' '}
//               {progress?.status?.length || 1}
//             </p>
//             <p className='text-xl text-green-500'>
//               Components Completed:{' '}
//               {progress?.status?.filter((el) => el?.newNestedId)?.length}
//             </p>
//           </div>
//         ) : (
//           <>
//             <div className='flex flex-col items-start gap-3'>
//               <h3>Select Components to Duplicate :</h3>
//               {componentReferences.map((component) => (
//                 <div
//                   key={component.id}
//                   className='flex items-center w-full gap-3'
//                 >
//                   <input
//                     type='checkbox'
//                     checked={selectedIdsToDuplicate.includes(component.id)}
//                     onChange={() => handleCheckboxChange(component.id)}
//                   />
//                   <span>
//                     <EntryPreviewById entryId={component.id} />
//                   </span>
//                 </div>
//               ))}
//             </div>
//             <div className='flex mb-10 gap-3'>
//               <button onClick={performDuplication} disabled={isLoading}>
//                 Duplicate
//               </button>
//             </div>
//           </>
//         )}
//       </div>
//       <div className='flex gap-10 items-center justify-center h-full w-1/2 flex-col overflow-auto mt-10'>
//         <GlobalTable
//           columns={columns}
//           data={progress?.status}
//           defaultPageSize={5}
//           pageSizeOptions={[5, 10, 15]}
//           searchableColumns={['internalName', 'contentType']}
//           height='300px'
//         />
//       </div>
//     </div>
//   )
// }

// export default DuplicateComponentButton
