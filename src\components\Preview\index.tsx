import { <PERSON>barAppSDK } from '@contentful/app-sdk'
import { Box, Button } from '@contentful/f36-components'
import React, { useEffect } from 'react'
import { ENV_VARIABLES } from '../../constant/variables'
import { getUpdatedBranchFrontName } from '../PageSettings/utils'

function PreviewUrls({ sdk }: { sdk: SidebarAppSDK }) {
  const [isPagePublished, setIsPagePublished] = React.useState(false)

  const [pageDomain, setPageDomain] = React.useState('')

  let slug = sdk.entry.fields['slug'].getValue()

  const MainPreviewBranch = ENV_VARIABLES.MainPreviewBranch
  const mainBranch = ENV_VARIABLES.mainBranch

  useEffect(() => {
    sdk.entry.onSysChanged(() => {
      const isDraft = sdk.entry.getSys().fieldStatus['*']['en-CA'] === 'draft'

      setIsPagePublished(!isDraft)
    })
  }, [sdk])

  useEffect(() => {
    const handleFieldChange = (value: any) => {
      let domain = value?.domain // Get the value of the "domain" field

      if (!domain) {
        const pageTags = sdk.entry.getMetadata()?.tags.map((tag) => tag.sys.id)

        domain = pageTags?.find((tag) => tag.startsWith('domain'))

        if (domain) domain = getDomainShortName(domain)
      }

      setPageDomain(domain)
    }

    const handleTagsChange = () => {
      const pageTags = sdk.entry.getMetadata()?.tags.map((tag) => tag.sys.id)

      let domain = pageTags?.find((tag) => tag.startsWith('domain'))

      if (domain) domain = getDomainShortName(domain)
      if (domain) setPageDomain(domain)
    }

    // Listen for field value changes
    const fieldUnsubscribe =
      sdk.entry.fields['configurations'].onValueChanged(handleFieldChange)

    // Listen for tag changes
    const tagsUnsubscribe = sdk.entry.onMetadataChanged(handleTagsChange)

    // Cleanup on unmount
    return () => {
      fieldUnsubscribe()
      tagsUnsubscribe()
    }
  }, [sdk])

  const handlePreviewUrlByBranch = (branch: string) => {
    const previewUrl = getPreviewUrlByBranch(branch, pageDomain || 'agl')
    window.open(previewUrl + slug, '_blank')
  }

  return (
    <Box
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'start',
        alignItems: 'start',
        gap: '0.5rem',
        width: '100%',
      }}
    >
      <h2
        style={{
          width: '100%',
          marginBottom: '0.5rem',
          fontSize: '0.75rem',
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid rgb(174, 193, 204)',
          alignItems: 'center',
          fontWeight: 600,
          color: '#67728a',
          lineHeight: '1.5rem',
        }}
      >
        Preview Urls
      </h2>
      <Box className='flex flex-col gap-3 w-full'>
        <Button
          variant='secondary'
          className='w-full'
          isDisabled={!pageDomain}
          onClick={() => handlePreviewUrlByBranch(MainPreviewBranch)}
        >
          Open {getUpdatedBranchFrontName(MainPreviewBranch)}
        </Button>
        <Button
          variant='secondary'
          className='w-full'
          isDisabled={!isPagePublished || !pageDomain}
          onClick={() => handlePreviewUrlByBranch(mainBranch)}
        >
          Open {mainBranch}
        </Button>
      </Box>
      {!pageDomain && (
        <p
          style={{
            fontSize: '0.75rem',
            color: '#ff1313',
          }}
        >
          Please select a domain tag
        </p>
      )}
    </Box>
  )
}

export default PreviewUrls

function getDomainShortName(domain: string) {
  switch (domain) {
    case 'domainAltusGroupCom':
      return 'agl'
    case 'domainFinanceActiveCom':
      return 'fia'
    case 'domainReonomyCom':
      return 'reo'
    case 'domainVerifinoCom':
      return 'ver'
    case 'domainOne11Com':
      return 'o11'
    default:
      return ''
  }
}

export const getPreviewUrlByBranch = (branch: string, domain: string) => {
  if (branch === 'main') {
    switch (domain) {
      case 'agl':
        return 'https://www.altusgroup.com/'
      case 'fia':
        return 'https://financeactive.com/'
      case 'reo':
        return 'https://reonomy.com/'
      case 'ver':
        return 'https://verifino.com/'
      case 'o11':
        return 'https://msa-o11-v3w-git-main-altus.vercel.app'
      default:
        return 'https://www.altusgroup.com/'
    }
  } else {
    return `https://msa-${domain}-v3w-git-${branch}-altus.vercel.app/`
  }
}
