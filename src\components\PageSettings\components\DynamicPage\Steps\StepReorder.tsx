import React from 'react'
import {
  DndContext,
  closestCenter,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import { Select } from 'antd'
import { useState } from 'react'
import SortableItem from '../SortableItem'

const StepReorder = ({
  parameterName,
  parameterData,
  setParameterData,
}: {
  parameterName: string
  parameterData: ParameterDataType
  setParameterData: SetParameterDataType
}) => {
  const parameterValues = Object.keys(parameterData[parameterName] || {})
  const [selectedValue, setSelectedValue] = useState(parameterValues[0])
  const items = parameterData[parameterName]?.[selectedValue] || []

  const sensors = useSensors(useSensor(PointerSensor))

  const handleDragEnd = (event: any) => {
    const { active, over } = event
    if (active.id !== over?.id) {
      const oldIndex = items.indexOf(active.id)
      const newIndex = items.indexOf(over.id)
      const reordered = arrayMove(items, oldIndex, newIndex)

      setParameterData((prev) => ({
        ...prev,
        [parameterName]: {
          ...prev[parameterName],
          [selectedValue]: reordered,
        },
      }))
    }
  }

  return (
    <>
      <Select
        style={{ width: 240, marginBottom: 16 }}
        value={selectedValue}
        onChange={(val) => setSelectedValue(val)}
        options={parameterValues.map((val) => ({ label: val, value: val }))}
      />
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext items={items} strategy={verticalListSortingStrategy}>
          {items.map((id) => (
            <SortableItem key={id} id={id} />
          ))}
        </SortableContext>
      </DndContext>
    </>
  )
}

export default StepReorder
