import React from 'react'
// import { sdk } from '@contentful/app-sdk' // or however you access SDK
import { Input } from '../../../../atoms'
const StepParameterName = ({
  parameterName,
  setParameterName,
  setParameterData,
  dynamicPageConfiguration,
  isActiveDynamicPage,
}: {
  parameterName: string
  isActiveDynamicPage: boolean
  setParameterName: (val: string) => void
  dynamicPageConfiguration: ParameterDataType
  setParameterData: SetParameterDataType
}) => {
  // const [parameterOptions, setParameterOptions] = useState<string[]>([])

  // useEffect(() => {
  //   const fetchConfigs = async () => {
  //     try {
  //       const configurations =
  //         (await sdk.entry.fields['configurations'].getValue()) || {}
  //       const dynamicPage = configurations.dynamicpage || {}
  //       setParameterOptions(Object.keys(dynamicPage))
  //     } catch (err) {
  //       message.error('Failed to load configurations')
  //     }
  //   }

  //   fetchConfigs()
  // }, [])
  // useEffect(() => {
  //   const options = Object.keys(dynamicPageConfiguration)
  //   // setParameterOptions(Object.keys(dynamicPageConfiguration))
  //   const val = options?.[0]
  //   setParameterName(val)
  //   const selectedParameter = dynamicPageConfiguration[val] || {}
  //   setParameterData({
  //     [val]: selectedParameter,
  //   })
  // }, [dynamicPageConfiguration])

  // const handleChange = async (val: string) => {
  //   setParameterName(val)
  //   try {
  //     const selectedParameter = dynamicPageConfiguration[val] || {}

  //     setParameterData({
  //       [val]: selectedParameter,
  //     })
  //   } catch (err) {
  //     message.error('Failed to load selected parameter data')
  //   }
  // }
  return (
    <>
      <Input
        placeholder='Enter parameter name (e.g. page)'
        value={parameterName}
        onChange={(e) => {
          setParameterName(e.target.value)
          setParameterData({})
        }}
        disabled={isActiveDynamicPage}
      />
      {isActiveDynamicPage && (
        <p className='text-xs text-gray-500 mt-1'>
          This field is disabled because campaigns rely on this parameter and
          changes could affect their accuracy.
        </p>
      )}
    </>
  )
  // return (
  //   <Select
  //     placeholder='Select or type parameter name (e.g. page)'
  //     value={parameterName || undefined}
  //     onChange={handleChange}
  //     showSearch
  //     allowClear
  //     onClear={() => setParameterName('')}
  //     options={parameterOptions.map((g) => ({ label: g, value: g }))}
  //     style={{ width: '100%' }}
  //   />
  // )
}

export default StepParameterName

// import React from 'react'
// import { Input } from '../../../../atoms'

// const StepParameterName = ({
//   parameterName,
//   setParameterName,
//   setParameterData,
// }: {
//   parameterName: string
//   setParameterName: (val: string) => void
//   setParameterData: (data: Record<string, Record<string, string[]>>) => void
// }) => (
// <Input
//   placeholder='Enter parameter name (e.g. page)'
//   value={parameterName}
//   onChange={(e) => setParameterName(e.target.value)}
// />
// )

// export default StepParameterName
