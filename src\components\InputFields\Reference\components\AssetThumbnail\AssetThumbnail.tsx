import * as React from 'react'

import { File } from '../../types'

interface AssetThumbnailProps {
  file: File
}

const dimensions = { width: 70, height: 70 }

/**
 * A component to render a thumbnail of an asset.
 *
 * @param props - AssetThumbnailProps
 * @returns A JSX element representing the thumbnail.
 */
export function AssetThumbnail(props: AssetThumbnailProps) {
  return (
    <img
      alt={props.file.fileName}
      src={`${props.file.url}?w=${dimensions.width}&h=${dimensions.height}&fit=thumb`}
      height={dimensions.height}
      width={dimensions.width}
    />
  )
}
