//Define global vars (eg. fonts, colours, paddings, margins, etc.) that are common to all the components here.

$fonts: (
        'Cambon Bold': 'Cambon-Bold',
        'Cambon BoldItalic': 'Cambon-BoldItalic',
        'Aeonik Regular': 'Aeonik-Regular',
        'Aeonik Medium': 'Aeonik-Medium',
        'Aeonik MediumItalic': 'Aeonik-MediumItalic',
        'Aeonik Light': 'Aeonik-Light',
        'Aeonik LightItalic': 'Aeonik-LightItalic',
        'Aeonik RegularItalic': 'Aeonik-RegularItalic',
        'Aeonik Bold': 'Aeonik-Bold',
        'Aeonik BoldItalic': 'Aeonik-BoldItalic',
);

@function fontDir($fontFileName) {
  @return ('../fonts/') + $fontFileName;
}

@mixin theFont($fontName, $fontFileName) {
  font-family: $fontName;
  src: url(fontDir($fontFileName + ('.woff'))) format('woff'),
  url(fontDir($fontFileName + ('.woff2'))) format('woff2');
}

@each $fontName, $fontFileName in $fonts {
  @font-face {
    @include theFont($fontName, $fontFileName);
  }
}

/**
Breakpoints
 */
$mScreenSize: 1200px; // for selected components such as (ex - Heroes, Full width carousels, containers, etc.) width should be >= 1200.
$smScreenSize: 733px; // screen size <= 733px is for mobile screen and screen size > 733px is for tablet/ desktop.
/*Content width should 76% of 390px (the minimum viewport we want to target, leaving a margin of 12% on both sides
 */
$minWidth: 296px;
/**
Max content width should not exceed 1450px, regardless of the viewport.
 */
$maxWidth: 1450px;
/**
 Fonts and families
 */
$serifFonts: Serif;
$fSerif: 'Cambon Bold', inspect($serifFonts);
$serifItalic: 'Cambon BoldItalic', inspect($serifFonts);
$fSans: sans-serif;
$fSansReg: 'Aeonik Regular', inspect($fSans);
$fSansRegItalic: 'Aeonik RegularItalic', inspect($fSans);
$fSansLight: 'Aeonik Light', inspect($fSans);
$fSansLightItalic: 'Aeonik LightItalic', inspect($fSans);
$fSansMed: 'Aeonik Medium', inspect($fSans);
$fSansMedItalic: 'Aeonik MediumItalic', inspect($fSans);
$fSansBld: 'Aeonik Bold', inspect($fSans);
$fSansBldItalic: 'Aeonik BoldItalic', inspect($fSans);

/**
 Font sizes
 */
$fs1: 12px;

$fs2: 14px;
$fs3: 16px;

$fs4: 18px;

$fs5: 20px;
$fs6: 24px;
$fs7: 80px;

/**
 Heading font sizes for large screen
 */
$h6FontSize: $fs4;
$h5FontSize: $fs5;
$h4FontSize: $fs6;
$h3FontSize: 32px;
$h2FontSize: 42px;
$h1FontSize: 64px;

/**
  Heading font sizes for small screen
  */
$sH6FontSize: $fs4;
$sH5FontSize: $fs5;
$sH4FontSize: 22px;
$sH3FontSize: $fs6;
$sH2FontSize: 30px;
$sH1FontSize: 40px;

$sfMax: 50px;

/**
Primary colours
 */
$cp1: #000b3d;
$cp2: #0028d7;
$cp3: #001c99;
$cp4: #8a8fa6;
$cp5: #333c64;

/**
 Secondary colours
  */
$cs1: #c9f344;
$cs2: #fff;
$cs3: #0082ab;
$cs4: #e6f9a9;
$cs5: #54661d;
$cs6: #8ecbd2;
$cs7: #000622;

/**
 Neutral colours
  */

//black
$cn1: #000;

// text - Primary
$cn2: #333;

// text - Secondary
$cn3: #757575;

// gray - 4
$cn4: #c6c6c6;

// gray - 3
$cn5: #e5e5e5;

// gray - 2
$cn6: #f2f2f2;

// gray - 1
$cn7: #f9f9f9;

// white
$cn8: #fff;

/**
 Accent colours
  */
$ca1: #2cde93;
$ca2: #00ad23;
$ca3: #009980;
$ca4: #0674e8;
$ca5: #00268b;
$ca6: #854dff;
$ca7: #d04dff;
$ca8: #ff4dd5;
$ca9: #ff4d7c;
$ca10: #ff854d;
$ca11: #ff5f05;
$ca12: #e2222b;

/**
 Overlay colours
  */
$co1: #0a2c37;

@import "vars2";