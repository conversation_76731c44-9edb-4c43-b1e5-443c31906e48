import { Box, Button, Modal, TextInput } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Done from '../../../assets/icons/Done'
import ColorsInput from '../../ColorsInput'
import { colorsValues } from '../../ColorsInput/@core/colorsValues'
interface Props {
  handleClose: () => void
  onColorPick: () => void
  selectedColors?: string
  isOpen?: boolean
  isHexInput?: boolean
}
function ColorPickerPopup(props: props) {
  const {
    isOpen,
    handleClose,
    selectedColors,
    onColorPick,
    isHexInput = false,
  } = props
  const findColorByHex = (hexValue: string): boolean =>
    !!colorsValues
      .flatMap((category) => category?.colors)
      .find((color) => color?.hex?.toUpperCase() === hexValue?.toUpperCase())

  const isColorFound = findColorByHex(selectedColors)
  const [hexValue, setHexValue] = useState<string>(
    isColorFound ? '' : selectedColors
  )

  useEffect(() => {
    setHexValue(isColorFound ? '' : selectedColors)
  }, [selectedColors])

  // Handler to update the input value
  const handleHexChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setHexValue(e.target.value)
  }
  // Handler for when the user confirms the HEX value
  const handleHexSubmit = () => {
    onColorPick({ value: hexValue })
  }

  return (
    <Modal
      onClose={() => handleClose()}
      isShown={isOpen}
      position='center'
      shouldCloseOnOverlayClick={false}
      shouldCloseOnEscapePress={false}
      className='colorPickerModal'
    >
      {() => (
        <>
          <Modal.Header title='' onClose={() => handleClose()}>
            <Box>
              <h2 className='modalTitle'>Color Picker</h2>
            </Box>
          </Modal.Header>

          <Modal.Content
            style={{
              paddingBottom: '20px',
            }}
          >
            <Box
              style={{
                padding: '10px',
              }}
            >
              <ColorsInput
                name={isColorFound && selectedColors}
                onClick={onColorPick}
                activeColor={isColorFound && selectedColors}
                showUndo={false}
              />
              {isHexInput && (
                <TextInput.Group
                  style={{
                    width: '50%',
                    margin: '10px 20px 0 20px',
                  }}
                >
                  <TextInput
                    aria-label='dv-color-input'
                    id='dv-color-input'
                    placeholder='Enter HEX value (e.g. #FF5733)'
                    value={hexValue}
                    onChange={handleHexChange}
                  />
                  <Button
                    variant='primary'
                    onClick={handleHexSubmit}
                    startIcon={<Done />}
                  ></Button>
                </TextInput.Group>
              )}
            </Box>
          </Modal.Content>
        </>
      )}
    </Modal>
  )
}

export default ColorPickerPopup
