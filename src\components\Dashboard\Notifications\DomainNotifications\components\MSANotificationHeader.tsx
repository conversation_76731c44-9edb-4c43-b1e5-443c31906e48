import { Box } from '@contentful/f36-components'
import React from 'react'
import ProgressB<PERSON> from '../../../../ProgressBar'

function MSANotificationHeader({ activeStep }: { activeStep: number }) {
  return (
    <>
      <Box className="h-1" />
      <ProgressBar completedSteps={activeStep} totalSteps={4} />
      <Box className="h-5" />
      {activeStep !== 3 && (

        <Box className="flex flex-col items-start justify-start w-full gap-1">
          <h2 className="modalTitle">
            {
              activeStepData?.[activeStep as keyof typeof activeStepData]
                .title
            }
          </h2>
          <Box>
            <p className="modalSubTitle">
              <span className="subtitleHeading">Description: </span>
              <span>
                {activeStepData?.[
                  activeStep as keyof typeof activeStepData
                  ]?.desc}
              </span>
            </p>
          </Box>
          <hr className="endHorizontalLine w-full" />
        </Box>
      )}
    </>
  )
}

export default MSANotificationHeader

const activeStepData = {
  // 0: {
  //   title: 'Type of Notification',
  //   desc: 'Select which type of notification you want to create',
  // },
  0: {
    title: 'Add Source & Details',
    desc: 'Select the Notification Source and add related details',
  },
  1: {
    title: 'Add Notification Settings',
    desc: 'Provide notification settings.',
  },
  2: {
    title: 'Add Notification Settings',
    desc: 'Provide notification settings.',
  },
}
