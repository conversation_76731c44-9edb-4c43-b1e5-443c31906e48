import { notification } from 'antd'
import type { ArgsProps, IconType } from 'antd/es/notification'
import React from 'react'
import {
  AiOutlineCloseCircle,
  AiOutlineInfoCircle,
  AiOutlineWarning,
} from 'react-icons/ai'
import { BsCheckCircle } from 'react-icons/bs'
import { GrClose } from 'react-icons/gr'

const getIcon = (type: IconType) => {
  switch (type) {
    case 'success':
      return <BsCheckCircle size={20} />
    case 'info':
      return <AiOutlineInfoCircle size={20} />
    case 'warning':
      return <AiOutlineWarning size={20} />
    case 'error':
      return <AiOutlineCloseCircle size={20} />
    default:
      return null
  }
}

const CustomNotification = {
  success: (config: ArgsProps) => {
    notification.success({
      icon: getIcon('success'),
      closeIcon: (
        <div className='btnRoot'>
          <GrClose size={20} />
        </div>
      ),
      duration: 5,
      showProgress: true,
      ...config,
    })
  },
  error: (config: ArgsProps) => {
    notification.error({
      icon: getIcon('error'),
      closeIcon: (
        <div className='btnRoot'>
          <GrClose size={20} />
        </div>
      ),
      duration: 5,
      showProgress: true,
      ...config,
    })
  },
  info: (config: ArgsProps) => {
    notification.info({
      icon: getIcon('info'),
      closeIcon: (
        <div className='btnRoot'>
          <GrClose size={20} />
        </div>
      ),
      duration: 5,
      showProgress: true,
      ...config,
    })
  },
  warning: (config: ArgsProps) => {
    notification.warning({
      icon: getIcon('warning'),
      closeIcon: (
        <div className='btnRoot'>
          <GrClose size={20} />
        </div>
      ),
      duration: 5,
      showProgress: true,
      ...config,
    })
  },
  open: (config: ArgsProps) => {
    notification.open({
      closeIcon: (
        <div className='btnRoot'>
          <GrClose size={20} />
        </div>
      ),
      duration: 5,
      showProgress: true,
      ...config,
    })
  },
  destroy: (key?: React.Key) => {
    notification.destroy(key)
  },
}

export default CustomNotification
