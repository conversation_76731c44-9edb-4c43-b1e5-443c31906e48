import { EditorAppSDK } from '@contentful/app-sdk'
import { entityHelpers } from '@contentful/field-editor-shared'
import { Asset, createClient } from 'contentful-management'
import Papa from 'papaparse'
import * as XLSX from 'xlsx'
import { ENV_VARIABLES } from '../../constant/variables'
import { colorsValues } from '../ColorsInput/@core/colorsValues'
import area from './assets/area-chart.webp'
import bar from './assets/bar-chart.jpeg'
import barRace from './assets/bar-race.webp'
import barStackNormalization from './assets/bar-stack-normalization.webp'
import boxplot from './assets/boxplot.webp'
import candlestick from './assets/candlestick.webp'
import doughnut from './assets/doughnut-chart.webp'
import lineRace from './assets/line-race.webp'
import line from './assets/line-stack.jpg'
import mixLineBar from './assets/mix-line-bar.webp'
import pie from './assets/pie.jpeg'
import scatter from './assets/scatter.webp'
import treemap from './assets/treemap.webp'
import waterfall from './assets/waterfall.webp'
interface series {
  name: string
  data: (string | number)[]
}
export interface DVTemplateData {
  id: string
  template: templateName
  changedData: any
  asset: Asset | undefined
}
export interface ParseResult {
  dimension: string[]
  categories: string[]
  series: series[]
}
export const dataUnits = [
  { value: ' ', label: 'Default' },
  { value: 'K', label: 'K - Thousand (X,000)' },
  { value: 'M', label: 'M - Million (X,000,000)' },
  { value: 'B', label: 'B - Billion (X,000,000,000)' },
  { value: 'T', label: 'T - Trillion (X,000,000,000,000)' },
]

export type templateName =
  | 'Line chart'
  | 'Bar chart'
  | 'Combo chart'
  | 'Area chart'
  | 'Pie chart'
  | 'Doughnut chart'
  | 'Candlestick chart'
  | 'Waterfall chart'
  | 'Treemap chart'
  | 'Scatter chart'
  | 'Boxplot chart'
  | 'LineRace chart'
  | 'BarRace chart'
  | 'BarStackNormalization chart'
  | undefined

export interface ChartModalPropsI {
  sdk: EditorAppSDK
  entryId: string
}
export interface templateI {
  name: templateName
  imageUrl: string
  fileUrl: string
  pattern: string
}

export const templates: templateI[] = [
  {
    name: 'Line chart',
    imageUrl: line,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/3fGnNPrdlmtRGH2AJNubhX/1ac7ee29504bc11a17ec0639b1f7a96b/Line-Chart.xlsx',
    pattern: 'p1',
  },
  {
    name: 'Bar chart',
    imageUrl: bar,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/1LS7CxLw2c0nWMlrcjSYUT/097e511de063eb77fca1b73a4c30f39a/Bar-Chart.xlsx',
    pattern: 'p1',
  },
  {
    name: 'Area chart',
    imageUrl: area,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/4GH9uE93XtPGSuMIQ0hhyy/572d42a4b0adbf7d111221995b9234ca/Area-Chart.xlsx',
    pattern: 'p1',
  },
  {
    name: 'Combo chart',
    imageUrl: mixLineBar,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/2982qr11mSg62Ujf0ZZDoM/f4c4162dd87d8c5c5713133db9617c88/Combo-Chart.xlsx',
    pattern: 'p1',
  },
  {
    name: 'Pie chart',
    imageUrl: pie,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/1rweBHFYKk2BGSf8wjrKLe/ab1e7858112c050c11de40c1813495e6/Pie.xlsx',
    pattern: 'p2',
  },
  {
    name: 'Doughnut chart',
    imageUrl: doughnut,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/1rweBHFYKk2BGSf8wjrKLe/ab1e7858112c050c11de40c1813495e6/Pie.xlsx',
    pattern: 'p2',
  },
  {
    name: 'Candlestick chart',
    imageUrl: candlestick,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/40raVDdInoNWNP349xNHCw/2cb06905390785d9ab94c29ea0ef0d07/Candlestick-chart.xlsx',
    pattern: 'p3',
  },
  {
    name: 'Waterfall chart',
    imageUrl: waterfall,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/2N5LQf3qDSqYFQ2zAMyFNp/d4c05fc5222dce09165cd4b37869012d/Waterfall-Chart.csv',
    pattern: 'p4',
  },
  {
    name: 'Treemap chart',
    imageUrl: treemap,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/4mpEzSEagNyJQ8n5YCMygU/031093562c7260f56b85c2e60f410cba/treemap.xlsx',
    pattern: 'p5',
  },
  {
    name: 'Scatter chart',
    imageUrl: scatter,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/1JsufoXqAIMVETKCEJzfBJ/acafa553d6c31c89b1dd8bea0bef4b36/Scatter-chart.csv',
    pattern: 'p6',
  },
  {
    name: 'Boxplot chart',
    imageUrl: boxplot,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/1QcYVg6L1FgzV5dpONgj1u/b5eab6eb0b574d9cd80249412c3f4baf/Boxplot.xlsx',
    pattern: 'p7',
  },
  {
    name: 'LineRace chart',
    imageUrl: lineRace,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/77PfrmmttJCxxhpKREMb1k/47fb1226b8de40e8f52ce259f1b8c807/Line_Race_chart.csv',
    pattern: 'p1',
  },
  {
    name: 'BarRace chart',
    imageUrl: barRace,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/1xTGAV5xQ4yKqaEOj2Ctn4/da7b3441ddc686b124575e3a207aef95/BarRace_chart.xlsx',
    pattern: 'p8',
  },
  {
    name: 'BarStackNormalization chart',
    imageUrl: barStackNormalization,
    fileUrl:
      'https://assets.ctfassets.net/8jgyidtgyr4v/1LS7CxLw2c0nWMlrcjSYUT/097e511de063eb77fca1b73a4c30f39a/Bar-Chart.xlsx',
    pattern: 'p9',
  },
]

const allColors = colorsValues
  .flatMap((group) => group.colors)
  .map((color) => color.hex)
const dimensionColors: Record<string, string> = allColors.reduce(
  (acc, color, index) => {
    acc[`dimension${index + 1}`] = color
    return acc
  },
  {} as Record<string, string>
)

const axisIndex = Array.from({ length: 10 }).reduce((acc, _, i) => {
  acc[`dimension${i + 1}`] = 1
  return acc
}, {})

const chartType = Array.from({ length: 20 }, (_, i) => ({
  [`dimension${i + 1}`]: 'bar',
}))
const lineStyle = Array.from({ length: 20 }, (_, i) => ({
  [`dimension${i + 1}`]: 'solid',
}))

export const defaults = {
  'Line chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*  titleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    },
    subTitleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
    titlePosition:'top', */
      isZoomable: true,
      isTooltip: true,
      //isEndLable: true,
      //isSmooth: true,
      format: 'stacked',
      isDownloadable: true,
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*    xAxisFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*    yAxisFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      isEndLable: false,
      isSmooth: true,
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
      lineStyle: lineStyle,
    },
  },
  'Bar chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },*/
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      format: 'stacked',
      isDownloadable: true,
      showDataLable: false,
      dataLablePlacement: 'inside',
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*   xAxisFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*    yAxisFonts:{
            fontColor:'#333333',
            fontFamily:'sans-serif',
            fontSize:16,
            lineHeight:24
          }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
  'Combo chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      isDownloadable: true,
      showDataLable: false,
      dataLablePlacement: 'inside',
      //isSmooth: true,
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*   xAxisFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*      yAxisFonts:{
            fontColor:'#333333',
            fontFamily:'sans-serif',
            fontSize:16,
            lineHeight:24
          }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      isEndLable: false,
      isSmooth: true,
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      colors: dimensionColors,
      chartType: chartType,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
  'Area chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      format: 'stacked',
      isDownloadable: true,
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*   xAxisFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*      yAxisFonts:{
            fontColor:'#333333',
            fontFamily:'sans-serif',
            fontSize:16,
            lineHeight:24
          }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
  'Pie chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      isDownloadable: true,
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      radius: '50%',
      seriesName: '',
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },*/
      colors: dimensionColors,
    },
  },
  'Doughnut chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      isDownloadable: true,
      innerRadius: '40%',
      outerRadius: '80%',
      seriesName: '',
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },*/
      colors: dimensionColors,
    },
  },
  'Candlestick chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      isDownloadable: true,
      candlstickTooltipLables: 'Open, Close, High, Low',
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*   xAxisFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*      yAxisFonts:{
            fontColor:'#333333',
            fontFamily:'sans-serif',
            fontSize:16,
            lineHeight:24
          }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },*/
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
  'Waterfall chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*  titleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    },
    subTitleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
    titlePosition:'top', */
      isZoomable: true,
      isTooltip: true,
      isDownloadable: true,
      showDataLable: false,
      dataLablePlacement: 'inside',
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*    xAxisFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*    yAxisFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },*/
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
  'Treemap chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      isDownloadable: true,
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },*/
      colors: dimensionColors,
    },
  },
  'Scatter chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*  titleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    },
    subTitleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
    titlePosition:'top', */
      isZoomable: true,
      isTooltip: true,
      format: 'stacked',
      isDownloadable: true,
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'value',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*    xAxisFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*    yAxisFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
  'Boxplot chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*  titleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    },
    subTitleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
    titlePosition:'top', */
      isZoomable: true,
      isTooltip: true,
      format: 'stacked',
      isDownloadable: true,
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*    xAxisFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*    yAxisFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
  'LineRace chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*  titleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    },
    subTitleFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      /*  titleAlignment: 'left',
    titlePosition:'top', */
      isZoomable: true,
      isTooltip: true,
      format: 'stacked',
      isDownloadable: true,
      duration: 10,
      //isEndLable: true,
      //isSmooth: true,
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: false,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*    xAxisFonts:{
      fontColor:'#333333',
      fontFamily:'sans-serif',
      fontSize:16,
      lineHeight:24
    }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*    yAxisFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      isEndLable: true,
      isSmooth: true,
      /*  LegendTop:'0%', */
      /*   legendFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
      lineStyle: lineStyle,
    },
  },
  'BarRace chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },*/
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      isDownloadable: true,
      duration: 3,
      showDataLable: true,
      dataLablePlacement: 'right',
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*   xAxisFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*    yAxisFonts:{
            fontColor:'#333333',
            fontFamily:'sans-serif',
            fontSize:16,
            lineHeight:24
          }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
  'BarStackNormalization chart': {
    general: {
      showTitle: true,
      title: 'Title',
      /*     titleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },
      subTitleFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      },*/
      subtitle: 'Subtitle',
      sourceText: 'Source: Altus Group',
      titleAlignment: 'left',
      titlePosition: 'top',
      isZoomable: true,
      isTooltip: true,
      format: 'stacked',
      isDownloadable: true,
      showDataLable: false,
      dataLablePlacement: 'inside',
    },
    axes: {
      showAxes: true,
      reverseAxisType: false,
      'x-axisCount': 1,
      'y-axisCount': 1,
      'x-axis': [
        {
          id: 'x-axis-0',
          show: true,
          type: 'category',
          title: 'X-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          xAxisLineColor: '#333333',
          reverseAxis: false,
          /*   xAxisFonts:{
        fontColor:'#333333',
        fontFamily:'sans-serif',
        fontSize:16,
        lineHeight:24
      }, */
        },
      ],
      'y-axis': [
        {
          id: 'y-axis-0',
          show: true,
          type: 'value',
          title: 'Y-Title-1',
          nameLocation: 'center',
          nameGap: '40',
          nameRotate: true,
          showAxisLine: true,
          yAxisLineColor: '#333333',
          min: '',
          max: '',
          interval: '',
          reverseAxis: false,
          /*    yAxisFonts:{
            fontColor:'#333333',
            fontFamily:'sans-serif',
            fontSize:16,
            lineHeight:24
          }, */
        },
      ],
    },
    styles: {
      graphBgColor: '#f9f9f9',
      chartHeight: '400',
      borderColor: '#333333',
      borderWidth: 0,
      borderType: 'None',
      isBorderShadow: false,
    },
    grid: {
      showGrid: true,
      gridFormat: 'Horizontal',
      gridBgColor: '#f9f9f9',
      gridLineColor: '#000',
      gridLineStyle: 'solid',
      gridLeft: 32,
      gridRight: 32,
      gridTop: 32,
      gridBottom: 60,
    },
    chart: {
      showLegend: true,
      /* legendAlignment: 'right', */
      legendPosition: 'bottom-center',
      legendOrientation: 'horizontal',
      /*  LegendTop:'0%', */
      /*   legendFonts:{
          fontColor:'#333333',
          fontFamily:'sans-serif',
          fontSize:16,
          lineHeight:24
        }, */
      colors: dimensionColors,
      'x-axis': axisIndex,
      'y-axis': axisIndex,
    },
  },
}

export function getPatternByTemplateName(templateName: templateName) {
  const template = templates.find((t) => t.name === templateName)
  return template?.pattern
}
export function getDataSourceUrlByTemplateName(
  templateName: templateName
): string | undefined {
  const template = templates.find((t) => t.name === templateName)
  return template?.fileUrl
}

export async function getAssetData(
  locale: string,
  id: string,
  AssetId: string
) {
  const client = createClient({
    space: ENV_VARIABLES.contentfulSpaceID,
    accessToken: ENV_VARIABLES.contentfulToken,
  })

  try {
    const space = await client.getSpace(ENV_VARIABLES.contentfulSpaceID)
    const environment = await space.getEnvironment(
      ENV_VARIABLES.contentfulEnvironment
    )
    const entry = await environment.getEntry(id)
    const chartFileId = entry?.fields?.source?.[locale]?.sys?.id
    // Fetch the linked asset
    const asset = await environment.getAsset(AssetId ? AssetId : chartFileId)
    return asset // Adjust this according to your needs
  } catch (error) {
    console.error(error)
  }
}

export async function getFieldsData(id: string) {
  const client = createClient({
    space: ENV_VARIABLES.contentfulSpaceID,
    accessToken: ENV_VARIABLES.contentfulToken,
  })

  try {
    const space = await client.getSpace(ENV_VARIABLES.contentfulSpaceID)
    const environment = await space.getEnvironment(
      ENV_VARIABLES.contentfulEnvironment
    )
    const entry = await environment.getEntry(id)

    return entry.fields
  } catch (error) {
    console.error(error)
  }
}

export async function updateDvJsonData(data: any, id: string) {
  const client = createClient({
    space: ENV_VARIABLES.contentfulSpaceID,
    accessToken: ENV_VARIABLES.contentfulToken,
  })

  const res = await client
    .getSpace(ENV_VARIABLES.contentfulSpaceID)
    .then((space) => {
      return space
        .getEnvironment(ENV_VARIABLES.contentfulEnvironment)
        .then((environment) => {
          return environment.getEntry(id).then((entry) => {
            entry.fields = data
            return entry.update()
          })
        })
    })
    .catch((error) => console.error(error))

  return res
}

export const UpdateDVEntryData = async (
  id: string,
  data: any,
  currentLocale: string
) => {
  if (id) {
    const entryData = await getFieldsData(id)
    let jsonData = { ...entryData }
    jsonData = {
      ...jsonData,
      template: data?.dvTemplate && {
        [currentLocale]: data?.dvTemplate,
      },
      data: data?.dvUpdatedData && {
        [currentLocale]: {
          content: [
            {
              content: [
                {
                  data: {},
                  marks: [],
                  nodeType: 'text',
                  value: JSON.stringify(data?.dvUpdatedData),
                },
              ],
              data: {},
              nodeType: 'paragraph',
            },
          ],
          data: {},
          nodeType: 'document',
        },
      },
      source: data?.dvAsset && {
        [currentLocale]: {
          sys: {
            type: 'Link',
            linkType: 'Asset',
            id: data?.dvAsset?.sys?.id,
          },
        },
      },
    }
    if (!data?.dvTemplate) {
      delete jsonData.template
    }
    if (!data?.dvUpdatedData) {
      delete jsonData.data
    }
    if (!data?.dvAsset) {
      delete jsonData.source
    }
    await updateDvJsonData(jsonData, id)
  }
}

export const parseCSVFile = async (url: string) => {
  try {
    const response = await fetch(url)
    const csvText = await response.text()
    const result = Papa.parse(csvText, { header: false })
    const data = result?.data || []
    const updatedData = data
      ?.map((row) => row.filter((val) => val !== null && val !== ''))
      .filter((row) => row.length > 0)
    return updatedData
  } catch (error) {
    console.error('Error fetching the CSV file:', error)
    return null
  }
}

export const parseXLSXFile = async (url: string) => {
  try {
    const response = await fetch(url)
    const data = await response.arrayBuffer()
    const workbook = XLSX.read(new Uint8Array(data), { type: 'array' })
    const firstSheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[firstSheetName]
    const parsedData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      raw: false,
      defval: null,
    })
    const updatedData = parsedData
      ?.map((row) => row.filter((val) => val !== null))
      .filter((row) => row.length > 0)
    return updatedData
  } catch (error) {
    console.error('Error fetching the XLSX file:', error)
    return null
  }
}

export const getParsedData = async (url) => {
  const fileExtension = url?.split('.').pop().toLowerCase()
  if (fileExtension === 'csv') {
    const parsedData = await parseCSVFile(`https:${url}`)
    return parsedData
  } else if (fileExtension === 'xlsx') {
    const parsedData = await parseXLSXFile(`https:${url}`)
    return parsedData
  } else {
    console.error('Unsupported file type:', fileExtension)
  }
}

async function publishAssetIfNotPublished(assetId: string) {
  try {
    // Fetch the latest asset data
    let assetResponse = await fetch(
      `https://api.contentful.com/spaces/${ENV_VARIABLES.contentfulSpaceID}/environments/${ENV_VARIABLES.contentfulEnvironment}/assets/${assetId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${ENV_VARIABLES.contentfulToken}`,
        },
      }
    )

    if (!assetResponse.ok) {
      throw new Error(`Error fetching asset: ${assetResponse.statusText}`)
    }

    let assetData = await assetResponse.json()

    // Check if the asset has not been published
    if (!assetData?.sys?.publishedAt) {
      // Publish the asset
      const publishResponse = await fetch(
        `https://api.contentful.com/spaces/${ENV_VARIABLES.contentfulSpaceID}/environments/${ENV_VARIABLES.contentfulEnvironment}/assets/${assetData?.sys?.id}/published`,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${ENV_VARIABLES.contentfulToken}`,
            'X-Contentful-Version': `${assetData.sys.version}`, // Use the correct version
          },
        }
      )

      if (publishResponse.status === 409) {
        //Version conflict detected. Retrying...
        return await publishAssetIfNotPublished(assetId)
      } else if (!publishResponse.ok) {
        throw new Error(`Error publishing asset: ${publishResponse.statusText}`)
      } else {
        return await publishResponse.json()
      }
    } else {
      return assetData
    }
  } catch (error) {
    console.error('An error occurred:', error)
  }
}

export const createOrUpdateAsset = async (
  file: any,
  locale: string,
  fileTypePrfix?: string
) => {
  try {
    // Step 1: Upload the file to Contentful's Upload API
    const uploadResponse = await fetch(
      `https://upload.contentful.com/spaces/${ENV_VARIABLES.contentfulSpaceID}/uploads`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/octet-stream',
          Authorization: `Bearer ${ENV_VARIABLES.contentfulToken}`,
        },
        body: file, // Binary data of the file
      }
    )

    if (!uploadResponse.ok) {
      throw new Error(`Error uploading file: ${uploadResponse.statusText}`)
    }
    const uploadData = await uploadResponse.json()
    const uploadId = uploadData.sys.id // Extract the upload_id

    const client = createClient({
      space: ENV_VARIABLES.contentfulSpaceID,
      accessToken: ENV_VARIABLES.contentfulToken,
    })

    const space = await client.getSpace(ENV_VARIABLES.contentfulSpaceID)
    const domain = getDomainName(space.name)
    const type =
      file?.type === 'text/csv' ? `${fileTypePrfix}A` : `${fileTypePrfix}AX`
    const fileName = file?.name?.substring(0, file?.name?.lastIndexOf('.'))
    const title = `${fileName} (${type}) `

    // Step 2: Create an asset in Contentful using the CMA
    const assetCreationPayload = {
      fields: {
        title: {
          [locale]: title,
        },
        file: {
          [locale]: {
            contentType: file.type, // File MIME type
            fileName: file.name, // File name
            uploadFrom: {
              sys: {
                type: 'Link',
                linkType: 'Upload',
                id: uploadId, // Use the upload_id from the previous response
              },
            },
          },
        },
      },
    }

    const assetResponse = await fetch(
      `https://api.contentful.com/spaces/${ENV_VARIABLES.contentfulSpaceID}/environments/${ENV_VARIABLES.contentfulEnvironment}/assets`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/vnd.contentful.management.v1+json',
          Authorization: `Bearer ${ENV_VARIABLES.contentfulToken}`,
        },
        body: JSON.stringify(assetCreationPayload),
      }
    )

    if (!assetResponse.ok) {
      throw new Error(`Error creating asset: ${assetResponse.statusText}`)
    }

    const assetData = await assetResponse.json()

    // Step 3: Process and publish the asset
    const processedAsset = await fetch(
      `https://api.contentful.com/spaces/${ENV_VARIABLES.contentfulSpaceID}/environments/${ENV_VARIABLES.contentfulEnvironment}/assets/${assetData.sys.id}/files/${locale}/process`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${ENV_VARIABLES.contentfulToken}`,
          'X-Contentful-Version': assetData.sys.version,
        },
      }
    )

    if (!processedAsset.ok) {
      throw new Error(`Error processing asset: ${processedAsset.statusText}`)
    }

    const asset = await publishAssetIfNotPublished(assetData?.sys?.id)
    return asset // Return the created asset data
  } catch (error) {
    throw error
  }
}

export function getDomainName(name) {
  let domain = name?.split(' ')[0].trim()
  switch (domain) {
    case 'Altus':
      return 'AGL'
    case 'Finance':
      return 'FIA'
    case 'Reonomy':
      return 'REO'
    case 'Verifino':
      return 'VER'
    case 'One11':
      return 'O11'
    default:
      return 'AGL'
  }
}

export async function getAssetInfo(asset, currentLocale) {
  const status = entityHelpers.getEntryStatus(asset.sys)
  const contentType = asset?.fields?.file?.[currentLocale]?.contentType
  const title = asset?.fields?.title?.[currentLocale]
  return { status, title, contentType }
}
export const removeFieldFromEntry = async (entryId, fieldName) => {
  try {
    const client = createClient({
      accessToken: ENV_VARIABLES.contentfulToken,
    })

    const space = await client.getSpace(ENV_VARIABLES.contentfulSpaceID)
    const environment = await space.getEnvironment(
      ENV_VARIABLES.contentfulEnvironment
    )

    const entry = await environment.getEntry(entryId)

    delete entry?.fields?.[fieldName]

    const updatedEntry = await entry.update()

    return updatedEntry
  } catch (error) {
    console.error('Error removing field from entry:', error)
    throw error
  }
}
//To extract only the leaf nodes from your treemap dataset
export function getLeafNodes(data) {
  const parents = new Set(data?.map((item) => item?.[1])?.slice(1))
  return data
    ?.filter((item) => !parents?.has(item?.[0]) && item?.[0] !== 'Name')
    ?.map((item) => item?.[0])
}

export async function getDimensions(
  template: templateName,
  asset: Asset,
  isGlobal: boolean,
  currentLocale: string
): Promise<string[]> {
  const fileUrl = asset?.fields?.file?.[currentLocale]?.url
  if (fileUrl) {
    const parsedData = await getParsedData(fileUrl)
    if (parsedData && parsedData.length !== 0) {
      const headers = parsedData[0]
      const dimensions = headers.filter(
        (header) => !header.startsWith('y-axis') && !header.startsWith('x-axis')
      )
      const leafNodes = getLeafNodes(parsedData)
      const dimension =
        template === 'Treemap chart'
          ? leafNodes
          : template === 'Waterfall chart'
          ? dimensions.filter((series) => series !== 'Placeholder')
          : template === 'Pie chart' || template === 'Doughnut chart'
          ? headers?.slice(1)
          : dimensions

      return dimension
    }
    return []
  } else {
    return isGlobal
      ? Array.from({ length: 10 }, (_, i) => `Series${i + 1}`)
      : []
  }
}

export const getCategories = async (asset: Asset, currentLocale: string) => {
  const fileUrl = asset?.fields?.file?.[currentLocale]?.url
  const data = await getParsedData(fileUrl)
  if (data) {
    const headers = data[0]
    // Get all data except the headers
    const remainingData = data.slice(1)?.filter((value) => value !== null)

    const dimensions = headers.filter(
      (header) => !header.startsWith('y-axis') && !header.startsWith('x-axis')
    )

    // Initialize an empty object to store categories for x and y columns
    const categoriesData: any = {}

    // Filter columns that start with 'x' or 'y'
    const filteredHeaders = headers.filter(
      (header) => header.startsWith('x-axis') || header.startsWith('y-axis')
    )

    filteredHeaders.forEach((header) => {
      const columnData = remainingData
        .map((row) => row[headers.indexOf(header)])
        .filter((value) => value !== null) // Remove null values
      if (columnData.length > 0) {
        categoriesData[header] = columnData // Only add if there's valid data
      }
    })

    return categoriesData
  }

  return {}
}

export const getData = (data: any[], template?: string) => {
  if (!data || data.length === 0)
    return { categoriesData: {}, dimensions: [], seriesData: [] }

  // Get headers (first row) and initialize other variables
  const headers = data[0]
  const remainingData = data.slice(1) // Data excluding headers

  const headerIndexMap = headers.reduce((acc, header, index) => {
    acc[header] = index
    return acc
  }, {})

  // Get dimensions (excluding 'x-axis' and 'y-axis' columns)
  const dimensions = headers.filter(
    (header) => !header.startsWith('x-axis') && !header.startsWith('y-axis')
  )

  // Prepare categoriesData for 'x-axis' and 'y-axis' columns
  const categoriesData: Record<string, any[]> = {}
  const filteredHeaders = headers.filter(
    (header) => header.startsWith('x-axis') || header.startsWith('y-axis')
  )

  filteredHeaders.forEach((header) => {
    const index = headerIndexMap[header]
    const columnData = remainingData
      .map((row) => row[index])
      .filter((value) => value != null) // Remove null/undefined
    if (columnData.length > 0) {
      categoriesData[header.trim()] = columnData // Only add if there's valid data
    }
  })

  // Prepare seriesData for dimensions
  const seriesData = dimensions.map((col) => {
    const colIndex = headerIndexMap[col]
    return remainingData
      .map((row) => row[colIndex])
      .filter((value) => value != null) // Remove null values
  })

  // If template is 'Candlestick chart', don't apply parseValue
  const finalSeriesData =
    template === 'Candlestick chart'
      ? seriesData
      : seriesData.map((series) => series.map(parseValue)) // Assuming parseValue is a function

  return { categoriesData, dimensions, seriesData: finalSeriesData }
}

// Define a generic type for objects with an 'id' field
type ItemWithId = {
  id: number | string // Assuming 'id' can be a number or string, adjust as necessary
  [key: string]: any // Other dynamic properties
}

export const mergeById = (
  arr1: ItemWithId[],
  arr2: ItemWithId[]
): ItemWithId[] => {
  const mergedArray = [...arr1] // Start with all elements from chart (arr1)

  arr2.forEach((item2) => {
    const index = mergedArray.findIndex((item1) => item1.id === item2.id) // Check if id matches

    if (index !== -1) {
      // If found, merge the objects with the same id
      mergedArray[index] = { ...mergedArray[index], ...item2 }
    } else {
      // If not found, just add the new item
      mergedArray.push(item2)
    }
  })

  return mergedArray
}
export const getGradientColor = (color: any) => {
  const gradientContent = color.slice(
    color.indexOf('(') + 1,
    color.lastIndexOf(')')
  )
  // Step 2: Split the remaining string by commas to separate the angle and colors
  const [angle, color1, color2] = gradientContent
    .split(',')
    .map((item) => item.trim())

  const linearGradient = {
    type: 'linear',
    x: 0,
    y: 0,
    x2: angle === '90deg' ? 0 : 1,
    y2: 1,

    colorStops: [
      {
        offset: 0,
        color: color1, // start color
      },
      {
        offset: 1,
        color: color2, // end color
      },
    ],
    global: false, // local to the chart container
  }
  return linearGradient
}

type Param = {
  value: number // The numerical value to format
  prefix?: string // Optional prefix (e.g., "$")
  suffix?: string // Optional suffix (e.g., "T", "B", "%")
  decimalPlaces?: number
}

export const tools = {
  // Mapping for divisors
  divisors: { T: 1e12, B: 1e9, M: 1e6, K: 1e3, '%': 1 } as Record<
    string,
    number
  >,

  // Method to format values based on divisor and suffix
  formatValue(
    value: number,
    divisor: number,
    suffix: string,
    prefix: string = '',
    decimal: number = 0
  ): string {
    if (value !== undefined || value !== null) {
      if (suffix === '%') {
        // Handle percentage cases
        const d = (value * 100) % 1 === 0 ? 0 : 2
        const number = parseFloat((value * 100).toFixed(decimal))
        const formattedValue = number.toLocaleString('en-CA', {
          minimumFractionDigits: decimal,
          maximumFractionDigits: decimal,
        })
        return `${prefix}${formattedValue}${suffix}`
      }

      const dd = (value / divisor) % 1 === 0 ? 0 : 2
      const number = parseFloat((value / divisor).toFixed(decimal))
      const formattedValue = number.toLocaleString('en-CA', {
        minimumFractionDigits: decimal,
        maximumFractionDigits: decimal,
      })
      return `${prefix}${formattedValue}${suffix}`
    }
  },
  // Method to format tooltip values
  getTooltipFormatValue(param: Param): string {
    const { value, prefix = '', suffix = '', decimalPlaces } = param
    let divisor = this.divisors[suffix]
    if (divisor) {
      // Use formatValue to handle the suffix logic
      return this.formatValue(value, divisor, suffix, prefix, decimalPlaces)
    }

    // Default case
    const number = parseFloat(value.toFixed(decimalPlaces))
    const formattedValue = number.toLocaleString('en-CA', {
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    })

    return `${prefix}${formattedValue}${suffix}`
  },

  // Method to format axis label values
  getAxesLabelFormatValue(param: Param): string {
    const { value, prefix = '', suffix = '', decimalPlaces = 0 } = param
    // Determine the divisor and suffix based on the value

    let decimal = 0
    let divisor = this.divisors[suffix]
    if (suffix === '%') {
      decimal = (value * 100) % 1 === 0 ? 0 : 2
    } else {
      decimal = divisor
        ? (value / divisor) % 1 === 0
          ? 0
          : 2
        : value % 1 === 0
        ? 0
        : decimalPlaces
    }

    if (divisor) {
      // Use formatValue to handle the suffix logic
      return this.formatValue(value, divisor, suffix, prefix, decimal)
    }

    // Default case
    const number = parseFloat(value.toFixed(decimal))
    const formattedValue = number.toLocaleString('en-CA', {
      minimumFractionDigits: decimal,
      maximumFractionDigits: decimal,
    })

    return `${prefix}${formattedValue}${suffix}`
  },
}

// Function to parse data with prefix/suffix
export const parseValue = (value: any) => {
  const match = String(value)
    ?.trim()
    ?.match(/^([^\d,.\-]*)?(-?\d+(?:,\d{3})*(?:\.\d+)?)([\W\w]*)$/)
  if (!match) return null

  const prefix = match[1] ? match[1]?.trim() : ''
  const suffix = match[3].toUpperCase()?.trim()
  const originalValue = match[2].replace(/,/g, '')
  let numericValue = parseFloat(originalValue)
  // Store decimal places count
  const decimalPlaces = originalValue.includes('.')
    ? originalValue.split('.')[1].length
    : 0

  switch (suffix) {
    case 'T': // Trillion
      numericValue *= 1e12
      break
    case 'B': // Billion
      numericValue *= 1e9
      break
    case 'M': // Million
      numericValue *= 1e6
      break
    case 'K': // Thousand
      numericValue *= 1e3
      break
    case '%': // Percentage
      numericValue /= 100
      break
    default:
      break
  }

  return {
    value: numericValue,
    prefix,
    suffix,
    decimalPlaces,
  }
}

// Function to parse data with prefix/suffix
export const parseValueWithDataUnit = (
  value: any,
  prefix: string,
  suffix: string
) => {
  const divisor = tools?.divisors[suffix]
  let decimal = 0
  if (divisor) {
    decimal = (value / divisor) % 1 === 0 ? 0 : 2
  } else {
    decimal = value % 1 === 0 ? 0 : 2
  }
  return {
    value,
    prefix,
    suffix,
    decimalPlaces: decimal,
  }
}

export const getTooltipPieChart = (general: any) => {
  const tooltip = {
    show: general?.isTooltip,
    borderColor: '#fff',
    trigger: 'item', // The tooltip is triggered by the item
    formatter: function (params) {
      // Check if params.color is a gradient object
      let gradientColor = ''
      if (params.color && params.color.colorStops) {
        const { x, y, x2, y2, colorStops } = params.color

        // Calculate the gradient angle in degrees
        const angle = Math.atan2(y2 - y, x2 - x) * (180 / Math.PI)
        const colors = colorStops.map((stop) => `${stop.color}`).join(', ')
        gradientColor = `linear-gradient(${angle}deg, ${colors})`
      } else {
        gradientColor = params.color // Fallback for solid colors
      }

      let tooltipContent = `
      <div class="tooltip-container">
        <div class="tooltip-heading">${params?.seriesName}</div>
    `

      tooltipContent += `
        <div class="tooltip-item">
          <div class="tooltip-series">
            <span class="tooltip-marker" style="background: ${gradientColor};"></span>
            <span>${params.name}</span>
          </div>
          <span class="tooltip-value"> ${tools.getTooltipFormatValue(
            params.data
          )}</span>
        </div>
      `

      tooltipContent += `</div>`
      return tooltipContent
    },
  }

  return tooltip
}
export const getPieChartSeriesData = (
  data: any[],
  preferences: any,
  template: string
) => {
  const generalPreference = preferences?.general
  const gridPreferences = preferences?.grid
  const legendPreferences = preferences?.chart
  const colors = legendPreferences?.colors
  const radius =
    template === 'Doughnut chart'
      ? [
          `${generalPreference?.innerRadius}`,
          `${generalPreference?.outerRadius}`,
        ]
      : generalPreference?.radius

  const items = data?.[0]
  const rows = data?.slice(1)?.filter((value) => value !== null)
  const series = items?.slice(1)?.map((header, index) => {
    return {
      name: header,
      data: rows
        ?.map((row) => {
          const data = parseValue(row[index + 1])
          return generalPreference?.dataUnit
            ? parseValueWithDataUnit(
                data?.value,
                data?.prefix,
                generalPreference?.dataUnit
              )
            : data
        })
        ?.filter((value) => value !== null),
    }
  })
  const dimensions = series?.map((data) => data?.name)
  const seriesData = [
    {
      name: generalPreference?.seriesName,
      type: 'pie',
      radius: radius,
      left: gridPreferences?.gridLeft || 30,
      top: gridPreferences?.gridTop || 30,
      right: gridPreferences?.gridRight || 30,
      bottom: gridPreferences?.gridBottom || 30,
      emphasis: {
        focus: 'series',
      },
      //color: series?.map((series, i) => colors?.[`dimension${i + 1}`]),
      color: series?.map((series, i) =>
        colors?.[`dimension${i + 1}`]?.startsWith('linear-gradient')
          ? getGradientColor(colors?.[`dimension${i + 1}`])
          : colors?.[`dimension${i + 1}`]
      ),
      data: series?.map((series) => {
        return {
          value: series?.data[0]?.value,
          name: series?.name,
          prefix: series?.data[0]?.prefix,
          suffix: series?.data[0]?.suffix,
          decimalPlaces: series?.data[0]?.decimalPlaces,
        }
      }),
    },
  ]
  return { seriesData, dimensions }
}

export const getTooltipLineChart = (
  grid: any,
  general: any,
  legend: any,
  template?: string
) => {
  const tooltip = {
    show: general?.isTooltip,
    trigger: 'axis', // The tooltip is triggered by the axis
    axisPointer: {
      z: 0,
      type: 'line', // Type of pointer is a vertical line
      lineStyle: {
        color: grid?.gridLineColor || '#000', // Color of the vertical line, default is black
        width: 1, // Line width
      },
    },
    formatter: function (params) {
      // Get the axis label as heading (e.g., x-axis value)
      const axisLabel = params[0]?.axisValueLabel
      // Filter to show only selected series in the tooltip

      let tooltipContent = `
      <div class="tooltip-container">
        <div class="tooltip-heading">${axisLabel}</div>
    `
      params.forEach((item) => {
        const isSelectedLegend = legend?.activeLegends?.includes(
          item.seriesName
        )
        // Check if item.color is a gradient object
        let gradientColor = ''
        if (item.color && item.color.colorStops) {
          const { x, y, x2, y2, colorStops } = item.color

          // Calculate the gradient angle in degrees
          const angle = Math.atan2(y2 - y, x2 - x) * (180 / Math.PI)
          const colors = colorStops.map((stop) => `${stop.color}`).join(', ')
          gradientColor = `linear-gradient(${angle}deg, ${colors})`
        } else {
          gradientColor = item.color // Fallback for solid colors
        }
        const data =
          template === 'BarStackNormalization chart'
            ? {
                ...item.data,
                value: Math.round(item?.data?.value * 1000) / 10,
                suffix: '%',
              }
            : item.data
        const content =
          template === 'BarStackNormalization chart'
            ? `${data?.prefix} ${data?.value} ${data?.suffix}`
            : tools.getTooltipFormatValue(data)
        tooltipContent += `
        <div class="tooltip-item">
          <div class="tooltip-series">
            <span class="tooltip-marker" style="background: ${gradientColor};"></span>
            <span>${item.seriesName}</span>
          </div>
          <span class="tooltip-value">${content}</span>
        </div>
      `
      })

      tooltipContent += `</div>`
      return tooltipContent
    },
  }

  return tooltip
}

export const getTooltipWaterfall = (grid: any, general: any) => {
  const tooltip = {
    show: general?.isTooltip,
    trigger: 'axis',
    axisPointer: {
      z: 0,
      type: 'line',
      lineStyle: {
        color: grid?.gridLineColor, // Color of the vertical line
        width: 1, // Line width
      },
    }, // The tooltip is triggered by the item
    formatter: function (params) {
      if (!params || params.length === 0) return ''

      let target
      // Find the target bar (positive or negative)
      if (params[1] && params[1].value) {
        target = params[1]
      } else {
        target = params[2]
      }

      if (!target) return 'No data available' // Handle cases with no valid target

      // Check if tar.color is a gradient object
      let gradientColor = ''
      if (target.color && target.color.colorStops) {
        const { x, y, x2, y2, colorStops } = target.color

        // Calculate the gradient angle in degrees
        const angle = Math.atan2(y2 - y, x2 - x) * (180 / Math.PI)
        const colors = colorStops.map((stop) => `${stop.color}`).join(', ')
        gradientColor = `linear-gradient(${angle}deg, ${colors})`
      } else {
        gradientColor = target.color // Fallback for solid colors
      }

      let tooltipContent = `
      <div class="tooltip-container">
        <div class="tooltip-heading">${target.name}</div>
    `

      tooltipContent += `
        <div class="tooltip-item">
          <div class="tooltip-series">
            <span class="tooltip-marker" style="background: ${gradientColor};"></span>
            <span>${target.seriesName}</span>
          </div>
          <span class="tooltip-value"> ${tools.getAxesLabelFormatValue(
            target?.data
          )}</span>
        </div>
      `
      tooltipContent += `</div>`
      return tooltipContent
    },
  }

  return tooltip
}

export const getTooltipCandleStick = (
  grid: any,
  general: any,
  convertedData: any[]
) => {
  const defaultLabel = ['Open', 'Close', 'Low', 'High']
  let lables = general?.candlstickTooltipLables
    ? general?.candlstickTooltipLables?.split(',')
    : []

  // If there are fewer than 4 values, fill the rest from the second array
  lables = [...lables, ...defaultLabel.slice(lables?.length)]

  const tooltip = {
    show: general?.isTooltip,
    trigger: 'axis', // The tooltip is triggered by the axis
    axisPointer: {
      z: 0,
      type: 'line', // Type of pointer is a vertical line
      lineStyle: {
        color: grid?.gridLineColor || '#000', // Color of the vertical line, default is black
        width: 1, // Line width
      },
    },
    formatter: function (params) {
      // Get the axis label as heading (e.g., x-axis value)
      const axisLabel = params[0]?.axisValueLabel

      let tooltipContent = `
      <div class="tooltip-container">
        <div class="tooltip-heading">${axisLabel}</div>
    `

      params.forEach((item, i) => {
        // Check if item.color is a gradient object
        let gradientColor = ''
        if (item.color && item.color.colorStops) {
          const { x, y, x2, y2, colorStops } = item?.color

          // Calculate the gradient angle in degrees
          const angle = Math.atan2(y2 - y, x2 - x) * (180 / Math.PI)
          const colors = colorStops.map((stop) => `${stop.color}`).join(', ')
          gradientColor = `linear-gradient(${angle}deg, ${colors})`
        } else {
          gradientColor = item?.color // Fallback for solid colors
        }

        const [index, open, close, low, high] = item?.data
        const requiredData = convertedData?.[i]?.[index]

        tooltipContent += `
        <div class="tooltip-candle-series">
        <div class="tooltip-candle--item">
    ${[open, close, low, high]
      .map((value, j) => {
        return `
        <div class="tooltip-item">
         <div class="tooltip-series">
         <span class="tooltip-candle-marker" style="background: ${gradientColor};"></span>
          <span class="tooltip-label"  style="font-size: 14px;">${
            lables?.[j]
          }</span>   
          </div> 
          <span class="tooltip-value"style="font-size: 14px;"> ${tools.getTooltipFormatValue(
            requiredData?.[j]
          )}</span>
        </div>
      `
      })
      .join('')}
        </div>
        </div>
      `
      })

      tooltipContent += `</div>`
      return tooltipContent
    },
  }

  return tooltip
}

export const getTooltipTreemap = (general: any) => {
  const tooltip = {
    show: general?.isTooltip,
    trigger: 'item', // The tooltip should be triggered by the item (treemap segment)
    borderColor: '#fff',
    textStyle: {
      color: '#333',
      fontFamily: "'Aeonik', sans-serif",
      fontSize: '16',
    },
    formatter: function (params) {
      const path = params.treePathInfo
        ? params.treePathInfo.map((item) => item?.name).join(' /')
        : ''
      return `${params?.name}: ${tools.getTooltipFormatValue(params?.data)}`
    },
  }
  return tooltip
}

export const getTooltipScatter = (general: any, convertedData: any) => {
  const tooltip = {
    show: general?.isTooltip,
    trigger: 'item', // The tooltip should be triggered by the item (treemap segment)
    borderColor: '#fff',
    textStyle: {
      color: '#333',
      fontFamily: "'Aeonik', sans-serif",
      fontSize: '16',
    },
    formatter: function (params) {
      const { seriesName, data, dataIndex, seriesIndex } = params
      const requiredData = convertedData?.[seriesIndex]?.[dataIndex]
      const value = requiredData?.[2]
        ? tools.getTooltipFormatValue(requiredData?.[2])
        : `${tools.getTooltipFormatValue(
            requiredData?.[0]
          )},${tools.getTooltipFormatValue(requiredData?.[1])}`
      return `${seriesName}: ${value}`
    },
  }
  return tooltip
}

export function calculateBoxplotStatistics(rawData: any) {
  return rawData.map((data) => {
    // Step 1: Sort the data
    data = data.sort((a, b) => a - b)
    // Step 2: Calculate the Median (Q2)
    const median = (arr) => {
      const mid = Math.floor(arr.length / 2)
      return arr.length % 2 === 0 ? (arr[mid - 1] + arr[mid]) / 2 : arr[mid]
    }

    // Step 3: Calculate Q1 and Q3
    const lowerHalf = data.slice(0, Math.floor(data.length / 2))
    const upperHalf = data.slice(Math.ceil(data.length / 2))
    const q1 = median(lowerHalf)
    const q3 = median(upperHalf)

    // Step 4: Calculate Interquartile Range (IQR)
    const iqr = q3 - q1

    // Step 5: Determine outliers
    const lowerBound = q1 - 1.5 * iqr
    const upperBound = q3 + 1.5 * iqr
    const outliers = data.filter(
      (value) => value < lowerBound || value > upperBound
    )

    // Step 6: Get Min and Max (ignoring outliers)
    const min = data[0]
    const max = data[data.length - 1]
    // Return an object with all the statistics
    return [min, q1, median(data), q3, max]
  })
}
export const getTooltipBoxplot = (general: any, convertedData: any) => {
  const tooltip = {
    show: general?.isTooltip,
    trigger: 'item', // The tooltip should be triggered by the item (treemap segment)
    borderColor: '#fff',
    textStyle: {
      color: '#333',
      fontFamily: "'Aeonik', sans-serif",
      fontSize: '16',
    },
    axisPointer: {
      type: 'shadow',
    },
    formatter: function (params) {
      let tooltipContent = `
      <div class="tooltip-container">
        <div class="tooltip-heading">${params.name}</div>
    `
      // Check if color is a gradient object
      let gradientColor = ''
      if (params.color && params.color.colorStops) {
        const { x, y, x2, y2, colorStops } = params?.color

        // Calculate the gradient angle in degrees
        const angle = Math.atan2(y2 - y, x2 - x) * (180 / Math.PI)
        const colors = colorStops.map((stop) => `${stop.color}`).join(', ')
        gradientColor = `linear-gradient(${angle}deg, ${colors})`
      } else {
        gradientColor = params?.color // Fallback for solid colors
      }

      const [index, min, Q1, median, Q3, max] = params?.data || []
      const requiredData = convertedData?.[params?.seriesIndex]?.[index]
      tooltipContent += `
        <div class="tooltip-candle-series">
         <div class="tooltip-series" style="margin-bottom: 4px;">
            <span class="tooltip-marker" style="background: ${gradientColor};"></span>
            <span>${params.seriesName}</span>
          </div>
        <div class="tooltip-candle--item">
    ${[min, Q1, median, Q3, max]
      ?.map((value, j) => {
        const item = {
          prefix: requiredData?.[0]?.prefix,
          value,
          suffix: requiredData?.[0]?.suffix,
          decimalPlaces: requiredData?.[0]?.decimalPlaces,
        }
        return `
        <div class="tooltip-item">
         <div class="tooltip-series">
         <span class="tooltip-candle-marker" style="background: ${gradientColor};"></span>
          <span class="tooltip-label"  style="font-size: 14px;">${
            ['min', 'Q1', 'median', 'Q3', 'max'][j]
          }</span>   
          </div> 
          <span class="tooltip-value"style="font-size: 14px;"> ${tools.getTooltipFormatValue(
            item
          )}</span>
        </div>
      `
      })
      .join('')}
        </div>
        </div>
      `

      tooltipContent += `</div>`
      return tooltipContent
    },
  }
  return tooltip
}
export const getTooltipBarRaceChart = (general: any, time: any) => {
  const tooltip = {
    show: general?.isTooltip,
    trigger: 'axis',
    textStyle: {
      color: '#333',
      fontFamily: "'Aeonik', sans-serif",
      fontSize: '16',
    },
    formatter: function (params) {
      let gradientColor = ''
      if (params[0].color && params[0].color.colorStops) {
        const { x, y, x2, y2, colorStops } = item?.color

        // Calculate the gradient angle in degrees
        const angle = Math.atan2(y2 - y, x2 - x) * (180 / Math.PI)
        const colors = colorStops.map((stop) => `${stop.color}`).join(', ')
        gradientColor = `linear-gradient(${angle}deg, ${colors})`
      } else {
        gradientColor = params[0]?.color // Fallback for solid colors
      }
      let tooltipContent = `
      <div class="tooltip-container">
        <div class="tooltip-heading">${time}</div>
    `

      tooltipContent += `
        <div class="tooltip-item">
          <div class="tooltip-series">
            <span class="tooltip-marker" style="background: ${gradientColor};"></span>
            <span>${params[0]?.name}</span>
          </div>
          <span class="tooltip-value"> ${tools.getTooltipFormatValue(
            params[0]?.data
          )}</span>
        </div>
      `
      tooltipContent += `</div>`
      return tooltipContent
    },
  }
  return tooltip
}
