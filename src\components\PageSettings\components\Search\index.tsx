/* eslint-disable react-hooks/exhaustive-deps */
import { EditorAppSDK } from '@contentful/app-sdk'
import {
  Box,
  Button,
  Checkbox,
  Notification,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import axios from 'axios'
import React, { useContext, useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import { ENV_VARIABLES } from '../../../../constant/variables'
import { GlobalContext } from '../../../../contexts/globalContext'
import { getLocaleFullName } from '../../../Crosspost/utils'
import * as styles from '../../../InputFields/SingleLine/styles'
import {
  CharCounter,
  CharValidation,
  ConstraintsUtils,
} from '../../../InputFields/shared'
import FormControlComp from '../../../Shared/FormControlComp'

interface Props {
  sdk: EditorAppSDK
  locale: string[]
  domain: string
}

function Search(props: Props) {
  const { sdk, locale, domain } = props

  const sdkFields = sdk.entry.fields

  const { currentLocale } = useContext(GlobalContext)

  const [shortTitleByLocale, setShortTitleByLocale] = useState<any>({})

  const [reIndexLoading, setReIndexLoading] = useState(false)

  const [values, setValues] = useState<any>({})

  let url = `${ENV_VARIABLES.appBaseURL}/api/sync-algolia-index/`.replace(
    'agl',
    domain
  )

  /**
   * Function to reindex Algolia for the given domain
   * @param {object} url - The URL to call to reindex Algolia
   * @return {Promise<void>} - The promise of the reindexing call
   */
  const handleAlgoliaReIndex = async () => {
    setReIndexLoading(true)
    try {
      const res = await axios.post(
        url,
        {},
        {
          headers: {
            'x-api-key': '0413fbe9-576b-4d2b-8191-8217c3b10736',
            'x-vercel-protection-bypass': `${ENV_VARIABLES.vercelByPassSecret}`,
          },
        }
      )

      if (res?.data?.indexingResponse?.objectIDs?.length > 0) {
        Notification.success('Algolia reindexed successfully')
      } else {
        Notification.error('Something went wrong')
      }
    } catch (err) {
      Notification.error('Something went wrong')
    }

    setReIndexLoading(false)
  }

  /**
   * Given a field name, returns the field value for English locale.
   * We're using English locale here because it's the source of truth for navigation settings.
   * @param {string} fieldName - The name of the field.
   * @returns {any} The value of the field.
   */
  const getFieldValueByFieldName = (fieldName: string) =>
    sdkFields?.[fieldName]?.getForLocale('en-CA')?.getValue()

/**
 * Updates the value of a checkbox field for the current locale and saves the entry.
 * Also updates the state with the new value.
 * @param {string} fieldName - The name of the field to update.
 * @param {any} value - The new value to set for the checkbox.
 */

  const handleCheckBox = (fieldName: string, value: any) => {
    sdkFields?.[fieldName]?.getForLocale(currentLocale)?.setValue(value)

    setValues((prev: any) => {
      return {
        ...prev,
        [fieldName]: value,
      }
    })
    sdk.entry.save()
  }

  // Fetch the shortTitle data for each locale and set the state
  useEffect(() => {
    if (sdk) {
      const updateLocaleData = () => {
        const translatedValues = locale
          .map((item) => ({
            [item]: sdk.entry.fields['shortTitle']
              .getForLocale(item)
              .getValue(),
          }))
          .reduce((acc, val) => ({ ...acc, ...val }), {})

        setShortTitleByLocale(translatedValues)
      }

      // Initial fetch of data
      updateLocaleData()

      // SDK's built-in method to listen for change in the field value
      const unsubscribeFunctions = locale.map((item) =>
        sdk.entry.fields['shortTitle'].getForLocale(item).onValueChanged(() => {
          updateLocaleData()
        })
      )

      // Cleanup all subscriptions on unmount
      return () => {
        unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
      }
    }
  }, [sdk, locale])

  // Fetch the hideFromAlgolia data and set the state
  useEffect(() => {
    if (sdk) {
      const hideFromAlgolia = getFieldValueByFieldName('hideFromAlgolia')

      setValues({
        hideFromAlgolia,
      })
    }
  }, [sdk])

  const handleCopyFrom = (field: string) => {
    let x = { ...shortTitleByLocale }

    locale.forEach(async (locale) => {
      const value = sdk.entry.fields?.[field]?.getForLocale(locale)?.getValue()
      const oldValue = shortTitleByLocale?.[locale]

      if (oldValue === value) return
      sdk.entry.fields['shortTitle'].getForLocale(locale).setValue(value)
      sdk.entry.save()
      x = {
        ...x,
        [locale]: value,
      }
    })

    setShortTitleByLocale(x)

    sdk.entry.save()
  }

  /**
   * Handler for debounced change of shortTitle field.
   * If the new value is the same as the existing value, do nothing.
   * Otherwise, update the field's value, mark configurations as not translated,
   * and set the state to the new value.
   * @param {string} value - New value of the field
   * @param {string} locale - Locale of the field
   */
  const handleDebouncedChange = async (value: string, locale: string) => {
    const oldValue = shortTitleByLocale?.[locale]

    if (oldValue === value) return

    sdk.entry.fields['shortTitle'].getForLocale(locale).setValue(value)

    handleFieldValueChangeToUpdateConfigurations(value, 'shortTitle', locale)

    setShortTitleByLocale({
      ...shortTitleByLocale,
      [locale]: value,
    })

    sdk.entry.save()
  }

/**
 * Updates the configurations field to mark it as not translated when the value of a specified field changes.
 * 
 * This function checks if the new value of a field is different from its current value for a given locale.
 * If the values differ, it updates the 'isTranslated' property of the configurations field to false and saves the entry.
 *
 * @param {any} value - The new value of the field.
 * @param {string} fieldId - The ID of the field to update.
 * @param {string} locale - The locale of the field.
 */

  const handleFieldValueChangeToUpdateConfigurations = (
    value: any,
    fieldId: string,
    locale: string
  ) => {
    const fieldValue = sdkFields[fieldId].getForLocale(locale).getValue()

    const isValueSame = JSON.stringify(fieldValue) === JSON.stringify(value)

    if (isValueSame) return

    let x = sdkFields?.['configurations']?.getValue()

    x = {
      ...x,
      isTranslated: false,
    }

    sdkFields?.['configurations']?.setValue(x)

    sdk.entry.save()
  }

  const constraints = ConstraintsUtils.fromFieldValidations(
    sdk.entry.fields['shortTitle'].validations,
    'Symbol'
  )

  const checkConstraint = ConstraintsUtils.makeChecker(constraints)

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-8 p-3 flex-col'>
      <Box className='flex flex-col gap-8'>
        {locale.map((locale) => {
          return (
            <Box
              key={locale}
              className='w-full flex items-center justify-start gap-3'
            >
              <FormControlComp
                label={`Algolia Title (${locale?.split('-')[0].toUpperCase()})`}
                tooltip={`Add the ${getLocaleFullName(
                  locale.split('-')[0]
                )} version of the page title displayed in Algolia search results.`}
                isRequired={
                  locale === 'en-CA' && sdkFields?.['shortTitle']?.required
                }
              >
                <TextInput
                  value={shortTitleByLocale?.[locale] || ''}
                  onChange={(e) =>
                    handleDebouncedChange(e.target.value, locale)
                  }
                />
                <div className={styles.validationRow}>
                  <CharCounter
                    value={shortTitleByLocale?.[locale] || ''}
                    checkConstraint={checkConstraint}
                  />
                  <CharValidation constraints={constraints} />
                </div>
              </FormControlComp>
            </Box>
          )
        })}
        <Box className='flex gap-3'>
          <Button
            variant='primary'
            size='small'
            onClick={() => handleCopyFrom('title')}
          >
            Copy from Page Title
          </Button>
          <Button
            variant='primary'
            size='small'
            onClick={() => handleCopyFrom('seoTitle')}
          >
            Copy from SEO Title
          </Button>
        </Box>
      </Box>

      <Box className='flex items-center justify-start w-full gap-1.5 pt-1.5'>
        <Checkbox
          isChecked={values?.['hideFromAlgolia']}
          onChange={(e) =>
            handleCheckBox('hideFromAlgolia', !values?.['hideFromAlgolia'])
          }
          className='p-0'
        >
          Hide From Algolia
        </Checkbox>
        <Tooltip
          placement='right'
          content='Enable to exclude this page from appearing in Algolia search results.'
        >
          <Info />
        </Tooltip>
      </Box>
      <FormControlComp
        label='Re-index Algolia'
        tooltip='Click "Confirm" to re-index this page on Algolia search results.'
      >
        <Button
          variant='primary'
          isLoading={reIndexLoading}
          onClick={handleAlgoliaReIndex}
          isDisabled={reIndexLoading}
        >
          Confirm
        </Button>
      </FormControlComp>
    </Box>
  )
}

export default Search
