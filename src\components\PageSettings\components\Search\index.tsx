/* eslint-disable react-hooks/exhaustive-deps */
import { EditorAppSDK } from '@contentful/app-sdk'
import {
  Box,
  Button,
  Checkbox,
  Notification,
  Tooltip,
} from '@contentful/f36-components'
import axios from 'axios'
import React, { useContext, useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import { GlobalContext } from '../../../../contexts/globalContext'
import { getLocaleFullName } from '../../../Crosspost/utils'
import InputFieldsRenderer from '../../../InputFields/InputFieldsRenderer'
import FormControlComp from '../../../Shared/FormControlComp'

interface Props {
  sdk: EditorAppSDK
  locale: string[]
  domain: string
}

function Search(props: Props) {
  const { sdk, locale, domain } = props

  const sdkFields = sdk.entry.fields

  const { currentLocale } = useContext(GlobalContext)

  const [shortTitleByLocale, setShortTitleByLocale] = useState<any>({})

  const [reIndexLoading, setReIndexLoading] = useState(false)

  const [values, setValues] = useState<any>({})

  let url = `${process.env.REACT_APP_BASE_URL}/api/sync-algolia-index/`.replace(
    'agl',
    domain
  )

  /**
   * Function to reindex the Algolia index for the given domain.
   *
   * The function will post a request to the specified URL with the API key and
   * the secret key in the headers. The response will be checked if the
   * indexingResponse object has a objectIDs property with at least one item.
   * If yes, a success notification will be shown, otherwise an error notification
   * will be shown.
   *
   * If the request throws an error, an error notification will be shown.
   *
   * The state of the component will be updated with the loading state.
   */
  const handleAlgoliaReIndex = async () => {
    setReIndexLoading(true)
    try {
      const res = await axios.post(
        url,
        {},
        {
          headers: {
            'x-api-key': '0413fbe9-576b-4d2b-8191-8217c3b10736',
            'x-vercel-protection-bypass': `${process.env.REACT_APP_VERCEL_BYPASS_SECRET}`,
          },
        }
      )

      if (res?.data?.indexingResponse?.objectIDs?.length > 0) {
        Notification.success('Algolia reindexed successfully')
      } else {
        Notification.error('Something went wrong')
      }
    } catch (err) {
      Notification.error('Something went wrong')
    }

    setReIndexLoading(false)
  }

  /**
   * Retrieves the value of a field for the specified field name in the 'en-CA' locale.
   *
   * @param {string} fieldName - The name of the field whose value is to be retrieved.
   * @returns {any} The value of the field for the 'en-CA' locale, or undefined if not available.
   */

  const getFieldValueByFieldName = (fieldName: string) =>
    sdkFields?.[fieldName]?.getForLocale('en-CA')?.getValue()

  /**
   * Handles the change event of a checkbox in the search settings component.
   *
   * Sets the value of the corresponding field in the sdk for the current locale.
   * Updates the state with the new field value.
   * Saves the entry.
   *
   * @param {string} fieldName - The name of the field whose value is to be updated.
   * @param {any} value - The new value of the field.
   */
  const handleCheckBox = (fieldName: string, value: any) => {
    sdkFields?.[fieldName]?.getForLocale(currentLocale)?.setValue(value)

    setValues((prev: any) => {
      return {
        ...prev,
        [fieldName]: value,
      }
    })
    sdk.entry.save()
  }

  useEffect(() => {
    if (sdk) {
      // func to update the local state with the latest data from the entry on load

      const updateLocaleData = () => {
        const translatedValues = locale
          .map((item) => ({
            [item]: sdk.entry.fields['shortTitle']
              .getForLocale(item)
              .getValue(),
          }))
          .reduce((acc, val) => ({ ...acc, ...val }), {})

        setShortTitleByLocale(translatedValues)
      }

      // Initial fetch of data
      updateLocaleData()

      // Set up onValueChanged for each locale
      const unsubscribeFunctions = locale.map((item) =>
        sdk.entry.fields['shortTitle'].getForLocale(item).onValueChanged(() => {
          updateLocaleData()
        })
      )

      // Cleanup all subscriptions on unmount
      return () => {
        unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
      }
    }
  }, [sdk, locale])

  useEffect(() => {
    if (sdk) {
      //  get hideFromAlgolia value from the sdk

      const hideFromAlgolia = getFieldValueByFieldName('hideFromAlgolia')

      setValues({
        hideFromAlgolia,
      })
    }
  }, [sdk])

  /**
   * Copies the value from a specified field to the 'shortTitle' field for each locale.
   *
   * This function iterates over all locales, retrieves the value of the specified field,
   * and sets it to the 'shortTitle' field if the values are different. It then updates
   * the component state with the new values and saves the entry.
   *
   * @param {string} field - The field id from which to copy the value.
   */

  const handleCopyFrom = (field: string) => {
    let x = { ...shortTitleByLocale }

    locale.forEach(async (locale) => {
      const value = sdk.entry.fields?.[field]?.getForLocale(locale)?.getValue()
      const oldValue = shortTitleByLocale?.[locale]

      if (oldValue === value) return
      sdk.entry.fields['shortTitle'].getForLocale(locale).setValue(value)
      sdk.entry.save()
      x = {
        ...x,
        [locale]: value,
      }
    })

    setShortTitleByLocale(x)

    sdk.entry.save()
  }

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-8 p-3 flex-col'>
      <Box className='flex flex-col gap-8'>
        {locale.map((locale) => {
          return (
            <InputFieldsRenderer
              currentLocale={locale}
              fieldId='shortTitle'
              isPageSettingsFields={true}
              label={`Algolia Title (${locale?.split('-')[0].toUpperCase()})`}
              tooltip={`Add the ${getLocaleFullName(
                locale.split('-')[0]
              )} version of the page title displayed in Algolia search results.`}
              isRequired={
                locale === 'en-CA' && sdkFields?.['shortTitle']?.required
              }
              key={'shortTitle-' + locale}
            />
          )
        })}
        <Box className='flex gap-3'>
          <Button
            variant='primary'
            size='small'
            onClick={() => handleCopyFrom('seoTitle')}
          >
            Copy from SEO Title
          </Button>
        </Box>
      </Box>

      <Box className='flex items-center justify-start w-full gap-1.5 pt-1.5'>
        <Checkbox
          isChecked={values?.['hideFromAlgolia']}
          onChange={(e) =>
            handleCheckBox('hideFromAlgolia', !values?.['hideFromAlgolia'])
          }
          className='p-0'
        >
          Hide From Algolia
        </Checkbox>
        <Tooltip
          placement='right'
          content='Enable to exclude this page from appearing in Algolia search results.'
        >
          <Info />
        </Tooltip>
      </Box>
      <FormControlComp
        label='Re-index Algolia'
        tooltip='Click "Confirm" to re-index this page on Algolia search results.'
      >
        <Button
          variant='primary'
          isLoading={reIndexLoading}
          onClick={handleAlgoliaReIndex}
          isDisabled={reIndexLoading}
        >
          Confirm
        </Button>
      </FormControlComp>
    </Box>
  )
}

export default Search
