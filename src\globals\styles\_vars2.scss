/** This file is made for v4, it uses v4 naming convention for future.
There are instances where variable and classNames are used from "_vars.scss" but in future when we revisit,
we will consolidate both vars and vars2 into one and "_vars2.scss" will replace old "_vars.scss" */


/**
Primary colour variants
*/
$cp1: #000b3d;
$cp1-50: #e6e7ec;
$cp1-100: #b0b3c3;
$cp1-200: #8a8fa6;
$cp1-300: #545c7d;
$cp1-400: #333c64;
$cp1-600: #000a38;
$cp1-700: #00082b;
$cp1-800: #000622;
$cp1-900: #00051a;

$cp2: #0028d7;
$cp2-50: #e6eafb;
$cp2-100: #b0bcf3;
$cp2-200: #8a9ced;
$cp2-300: #546fe4;
$cp2-400: #3353df;
$cp2-600: #0024c4;
$cp2-700: #001c99;
$cp2-800: #001676;
$cp2-900: #00115a;

/**
Secondary colour variants
*/
$cs1: #c9f344;
$cs1-50: #fafeec;
$cs1-100: #eefbc5;
$cs1-200: #e6f9a9;
$cs1-300: #dbf782;
$cs1-400: #d4f569;
$cs1-600: #b7dd3e;
$cs1-700: #8fad30;
$cs1-800: #6f8625;
$cs1-900: #54661d;

// $cs2: #0082ab;
$cs2-50: #e6f3f7;
$cs2-100: #b0d8e5;
$cs2-200: #8ac6d8;
$cs2-300: #54abc7;
$cs2-400: #339bbc;
$cs2-600: #00769c;
$cs2-700: #005c79;
$cs2-800: #00485e;
$cs2-900: #00374b;

/**
Neutral colour variants
*/
$cn1: #000;
$cn2: #333;
$cn3: #757575;
$cn4: #c6c6c6;
$cn5: #e5e5e5;
$cn6: #f2f2f2;
$cn7: #f9f9f9;
$cn8: #fff;

/**
Accent colour variants
*/
$ca1: #2cde93;
$ca2: #009980;
$ca3: #00ad23;
$ca4: #002a99;
$ca5: #854dff;
$ca6: #d04dff;
$ca7: #ff4dd5;
$ca8: #ff4d7c;
$ca9: #ff5f05;
$ca10: #e2222b;

$ca1-50: #eafcf4;
$ca1-100: #bef5de;
$ca1-200: #9ef0cd;
$ca1-300: #72e9b7;
$ca1-400: #56e5a9;
$ca1-600: #28ca86;
$ca1-700: #1f9e68;
$ca1-800: #187a51;
$ca1-900: #125d3e;

$ca2-50: #e6f5f2;
$ca2-100: #b0dfd8;
$ca2-200: #8ad0c5;
$ca2-300: #54bbaa;
$ca2-400: #33ad99;
$ca2-600: #008b74;
$ca2-700: #006d5b;
$ca2-800: #005446;
$ca2-900: #004036;

$ca3-50: #e6f7e9;
$ca3-100: #b0e6bb;
$ca3-200: #8ad99a;
$ca3-300: #54c86c;
$ca3-400: #33bd4f;
$ca3-600: #009d20;
$ca3-700: #007b19;
$ca3-800: #005f13;
$ca3-900: #00490f;

$ca4-50: #e6eaf5;
$ca4-100: #b0bddf;
$ca4-200: #8a9dd0;
$ca4-300: #5470bb;
$ca4-400: #3355ad;
$ca4-600: #00268b;
$ca4-700: #001e6d;
$ca4-800: #001754;
$ca4-900: #001240;

$ca5-50: #f3edff;
$ca5-100: #d9c8ff;
$ca5-200: #c7adff;
$ca5-300: #ad88ff;
$ca5-400: #9d71ff;
$ca5-600: #7946e8;
$ca5-700: #5e37b5;
$ca5-800: #492a8c;
$ca5-900: #38206b;

$ca6-50: #faedff;
$ca6-100: #f0c8ff;
$ca6-200: #e9adff;
$ca6-300: #e088ff;
$ca6-400: #d971ff;
$ca6-600: #bd46e8;
$ca6-700: #9437b5;
$ca6-800: #722a8c;
$ca6-900: #57206b;

$ca7-50: #ffedfb;
$ca7-100: #ffc8f2;
$ca7-200: #ffadec;
$ca7-300: #ff88e3;
$ca7-400: #ff71dd;
$ca7-600: #e846c2;
$ca7-700: #b53797;
$ca7-800: #8c2a75;
$ca7-900: #6b2059;

$ca8-50: #ffedf2;
$ca8-100: #ffc8d6;
$ca8-200: #ffadc3;
$ca8-300: #ff88a7;
$ca8-400: #ff7196;
$ca8-600: #e84671;
$ca8-700: #b53758;
$ca8-800: #8c2a44;
$ca8-900: #6b2034;

$ca9-50: #ffefe6;
$ca9-100: #ffcdb2;
$ca9-200: #ffb58c;
$ca9-300: #ff9458;
$ca9-400: #ff7f37;
$ca9-600: #e85605;
$ca9-700: #b54304;
$ca9-800: #8c3403;
$ca9-900: #6b2802;

$ca10-50: #fce9ea;
$ca10-100: #f6babd;
$ca10-200: #f2999d;
$ca10-300: #ec6b71;
$ca10-400: #e84e55;
$ca10-600: #ce1f27;
$ca10-700: #a0181f;
$ca10-800: #7c1318;
$ca10-900: #5f0e12;

$accent-colors-map: (
        1: $ca1,
        2: $ca2,
        3: $ca3,
        4: $ca4,
        5: $ca5,
        6: $ca6,
        7: $ca7,
        8: $ca8,
        9: $ca9,
        10: $ca10
);


$accent-colors: (
        1: (
                "50": $ca1-50,
                "100": $ca1-100,
                "200": $ca1-200,
                "300": $ca1-300,
                "400": $ca1-400,
                "600": $ca1-600,
                "700": $ca1-700,
                "800": $ca1-800,
                "900": $ca1-900
        ),
        2: (
                "50": $ca2-50,
                "100": $ca2-100,
                "200": $ca2-200,
                "300": $ca2-300,
                "400": $ca2-400,
                "600": $ca2-600,
                "700": $ca2-700,
                "800": $ca2-800,
                "900": $ca2-900
        ),
        3: (
                "50": $ca3-50,
                "100": $ca3-100,
                "200": $ca3-200,
                "300": $ca3-300,
                "400": $ca3-400,
                "600": $ca3-600,
                "700": $ca3-700,
                "800": $ca3-800,
                "900": $ca3-900
        ),
        4: (
                "50": $ca4-50,
                "100": $ca4-100,
                "200": $ca4-200,
                "300": $ca4-300,
                "400": $ca4-400,
                "600": $ca4-600,
                "700": $ca4-700,
                "800": $ca4-800,
                "900": $ca4-900
        ),
        5: (
                "50": $ca5-50,
                "100": $ca5-100,
                "200": $ca5-200,
                "300": $ca5-300,
                "400": $ca5-400,
                "600": $ca5-600,
                "700": $ca5-700,
                "800": $ca5-800,
                "900": $ca5-900
        ),
        6: (
                "50": $ca6-50,
                "100": $ca6-100,
                "200": $ca6-200,
                "300": $ca6-300,
                "400": $ca6-400,
                "600": $ca6-600,
                "700": $ca6-700,
                "800": $ca6-800,
                "900": $ca6-900
        ),
        7: (
                "50": $ca7-50,
                "100": $ca7-100,
                "200": $ca7-200,
                "300": $ca7-300,
                "400": $ca7-400,
                "600": $ca7-600,
                "700": $ca7-700,
                "800": $ca7-800,
                "900": $ca7-900
        ),
        8: (
                "50": $ca8-50,
                "100": $ca8-100,
                "200": $ca8-200,
                "300": $ca8-300,
                "400": $ca8-400,
                "600": $ca8-600,
                "700": $ca8-700,
                "800": $ca8-800,
                "900": $ca8-900
        ),
        9: (
                "50": $ca9-50,
                "100": $ca9-100,
                "200": $ca9-200,
                "300": $ca9-300,
                "400": $ca9-400,
                "600": $ca9-600,
                "700": $ca9-700,
                "800": $ca9-800,
                "900": $ca9-900
        ),
        10: (
                "50": $ca10-50,
                "100": $ca10-100,
                "200": $ca10-200,
                "300": $ca10-300,
                "400": $ca10-400,
                "600": $ca10-600,
                "700": $ca10-700,
                "800": $ca10-800,
                "900": $ca10-900
        )
);