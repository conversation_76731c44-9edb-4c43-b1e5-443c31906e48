'use client'
import { AnimatePresence, AnimatePresenceProps, motion, MotionProps } from 'framer-motion'
import React from 'react'

export type animationProps = {
  /**
   * Type of HTML node to use.
   */
  as: keyof React.JSX.IntrinsicElements | React.JSXElementConstructor<any> | boolean
  children?: React.ReactNode | React.ReactElement
  /**
   * The props that need to be animated using FramerMotion.
   * @see [FramerMotion]{@link https://www.framer.com/motion/}
   */
  animatedProps: MotionProps

  /**
   * The props that need to be passed to the `AnimatePresence` component.
   * @see [AnimatePresence]{@link https://www.framer.com/motion/animate-presence/}
   */
  AnimatePresenceProps?: AnimatePresenceProps

  /**
   * A boolean value that determines whether the child components should be wrapped in `AnimatePresence` or not.
   */
  isAnimatedNodesGroup?: boolean
  /**
* `as` specific HTML attributes that need to be passed to the component.
* Only works when `isAsIs` is false.
*/
  htmlAttr?: React.ComponentProps<T>

}

export default function TheAnimatedNode({ ...props }: animationProps): React.ReactNode {

  //@ts-ignore
  const Motion = motion[props?.as]
  try {
    let TheNode
    if (props?.isAnimatedNodesGroup) {

      /* if isAnimatedNodesGroup=true, 
       Wrap the child in `AnimatePresence`. 
       AnimatePresence handles the mounting and unmounting of components with exit animations. */
      TheNode = <AnimatePresence {...props?.AnimatePresenceProps as AnimatePresenceProps}>
        {props?.children}
      </AnimatePresence>
    } else {
      TheNode = <Motion {...props?.htmlAttr} {...props?.animatedProps as MotionProps}  >
        {props?.children}
      </Motion>
    }
    return TheNode
  }
  catch (e) {
    throw e
  }
}
