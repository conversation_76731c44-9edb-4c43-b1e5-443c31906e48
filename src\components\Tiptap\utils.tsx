/**
 * Tiptap Utility Functions
 *
 * This file contains utility functions that support the Tiptap editor functionality.
 * These utilities provide common operations needed across different Tiptap components
 * for text manipulation, selection handling, editor state management, and Contentful
 * integration operations.
 *
 * Key Functions:
 * - Entry management (adding block and inline entries)
 * - Contentful SDK integration utilities
 * - Editor state manipulation helpers
 * - Content insertion and formatting utilities
 */

import { EditorAppSDK } from '@contentful/app-sdk'
import { Editor } from '@tiptap/react'

/**
 * Add Entry to WYSIWYG Editor
 *
 * This function adds a Contentful entry (either block or inline) to the Tiptap editor.
 * It integrates with the Contentful SDK to allow users to select and embed entries
 * directly within the rich text content.
 *
 * @param isBlockEntry - Boolean to determine if the entry should be block-level or inline
 * @param editor - The Tiptap editor instance where the entry will be inserted
 * @param sdk - The Contentful SDK instance for entry selection and management
 *
 * Usage:
 * - Called from MenuBar when users click entry insertion buttons
 * - Handles both embedded entry blocks and inline entry references
 * - Integrates with Contentful's entry selection dialog
 * - Maintains proper editor focus and cursor position
 *
 * Implementation Details:
 * - Uses Contentful SDK's entry selection interface
 * - Inserts appropriate node type based on isBlockEntry parameter
 * - Handles async operations for entry selection
 * - Maintains editor state consistency during insertion
 */
export async function addEntry(
  isBlockEntry: boolean,
  editor: Editor,
  sdk: EditorAppSDK
) {
  const selectedEntry = await sdk.dialogs.selectSingleEntry()
  if (!selectedEntry) return
  const { id, type, contentType } = selectedEntry.sys

  editor
    .chain()
    .focus()
    .insertContent({
      type: isBlockEntry ? 'reactBlockComponent' : 'reactInlineComponent',
      attrs: {
        sys: {
          id,
          type: isBlockEntry ? 'BlockEntry' : 'InlineEntry',
          linkType: type,
          typeName: contentType.sys.id,
        },
        //@ts-ignore
        // entryFields: selectedEntry.fields, //@todo: filter fields according to locale, currently getting all fields.
        // entryTitle: selectedEntry?.fields?.internalName['en-CA'],
        // entryId: selectedEntry?.sys?.id,
        // contentType: selectedEntry?.sys.contentType.sys.id,
        // status: selectedEntry?.sys.publishedAt
        //   // if publishedAt and updatedAt are the same, then the entry is published
        //   ? selectedEntry?.sys.publishedAt === selectedEntry?.sys.updatedAt
        //     ? 'published'
        //     : 'changed'
        //   : 'draft',
        // title: selectedEntry?.fields?.internalName['en-CA'],
      },
    })
    .run()
}


/**
 * Gets the selected text from the editor.
 * @param editor - The editor to get the selected text from.
 * @returns The selected text.
 */
export function getSelectedText(editor: Editor | null) {
  const { from, to } = editor?.state.selection
  const selectedText = editor?.state.doc.textBetween(from, to)
  return selectedText
}

/**
 * Gets the selected HTML from the editor.
 * @param editor - The editor to get the selected HTML from.
 * @returns The selected HTML.
 */
export function getSelectedHTML(editor: Editor | null) {
  const { from, to } = editor?.view.state.selection
  const { dom } = editor?.view
  const range = document.createRange()

  const start = editor?.view.domAtPos(from)
  range.setStart(start.node, start.offset)

  const end = editor?.view.domAtPos(to)
  range.setEnd(end.node, end.offset)

  const fragment = range.cloneContents()
  const div = document.createElement('div')
  div.appendChild(fragment)

  const selectedHTML = div.innerHTML
  return selectedHTML
}

/**
 * Wrap the selected text with a div element with the given className
 * @param editor Tiptap editor
 * @param className class name to add to the div element
 */
export function wrapSelectedTextWithDiv(
  editor: Editor | null,
  className: string
) {
  const selection = editor?.view.state.selection
  // get range from selection

  const range = selection?.$from.blockRange(selection.$to)
  // if (range) {
  //
  //   const tr = editor?.view.state.tr.wrap(range, editor?.view.state.schema.nodes.div.create())
  //   editor?.view.dispatch(tr)
  // }
  //
}
