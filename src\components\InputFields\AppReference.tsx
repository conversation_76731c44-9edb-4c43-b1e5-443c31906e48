import { FieldAppSDK } from '@contentful/app-sdk'
import { MultipleEntryReferenceEditor } from '@contentful/field-editor-reference'
import React from 'react'

interface AppReferenceI{
    sdk:FieldAppSDK
}

function AppReference(props:AppReferenceI) {
    const { sdk} = props
    console.log("SDK :",sdk);
    
    return (
        <MultipleEntryReferenceEditor sdk={sdk} viewType='link'
            parameters={{
                instance:
                {                   
                    showLinkEntityAction: true,
                    showCreateEntityAction:true
                }
            }} />
    )
}

export default AppReference