import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Checkbox, Tooltip } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import { getLocaleFullName } from '../../../Crosspost/utils'
import InputFieldsRenderer from '../../../InputFields/InputFieldsRenderer'

interface Props {
  sdk: EditorAppSDK
  locale: string[]
  domain: string
}

function SEO(props: Props) {
  const { sdk, locale } = props

  const sdkFields = sdk.entry.fields

  const [values, setValues] = useState<any>({})

  /**
   * A helper function to get the value of a field with the given name from the sdk, localized to en-CA.
   * @param fieldName The name of the field to fetch the value for
   * @returns The value of the fetched field, or undefined if the field does not exist or the sdk does not have data for the field
   */
  const getFieldValueByFieldName = (fieldName: string) =>
    sdkFields?.[fieldName]?.getForLocale('en-CA')?.getValue()

  /**
   * Handles the change event of a checkbox in the SEO settings component.
   *
   * Sets the value of the corresponding field in the sdk for the current locale.
   * Updates the state with the new field value.
   * Saves the entry.
   *
   * @param {string} fieldName - The name of the field whose value is to be updated.
   * @param {any} value - The new value of the field.
   */
  const handleCheckBox = (fieldName: string, value: any) => {
    sdkFields?.[fieldName]?.getForLocale('en-CA')?.setValue(value)

    setValues((prev: any) => {
      return {
        ...prev,
        [fieldName]: value,
      }
    })
    sdk.entry.save()
  }

  useEffect(() => {
    if (sdk) {
      // get noFollow and noIndex values from the sdk and set them in the local state
      const noFollow = getFieldValueByFieldName('noFollow')

      const noIndex = getFieldValueByFieldName('noIndex')

      setValues({
        noFollow,
        noIndex,
      })
    }
  }, [sdk])

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-5 p-3 flex-col'>
      <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <InputFieldsRenderer
            currentLocale={locale}
            fieldId='seoTitle'
            isPageSettingsFields={true}
            label={`SEO Title (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the SEO title displayed on search engines.`}
            isRequired={locale === 'en-CA' && sdkFields?.['seoTitle']?.required}
            key={'seoTitle-' + locale}
          />
        ))}
      </Box>

      <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <InputFieldsRenderer
            currentLocale={locale}
            fieldId='seoDescription'
            isPageSettingsFields={true}
            label={`SEO Description (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the SEO meta desciption that will display on search engines.`}
            isRequired={
              locale === 'en-CA' && sdkFields?.['seoDescription']?.required
            }
            key={'seoDescription-' + locale}
          />
        ))}
      </Box>

      <Box className='flex justify-start items-center pb-2.5'>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['noFollow']}
            onChange={(e) => handleCheckBox('noFollow', !values?.['noFollow'])}
            className='p-0'
          >
            Add no follow tag
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to instruct crawlers not to follow links from the page as part of crawling.'
          >
            <Info />
          </Tooltip>
        </Box>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['noIndex']}
            onChange={(e) => handleCheckBox('noIndex', !values?.['noIndex'])}
            className='p-0'
          >
            Add no index tag
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to prevent this page from being indexed and appearing on search engines.'
          >
            <Info />
          </Tooltip>
        </Box>
      </Box>
      {/* <FormControlComp
        label={`Robots.txt`}
        isRequired={true}
        tooltip='Enable to allow search engine crawlers to access and crawl this URL.'
      >
        <Box className='flex items-end justify-start w-full gap-3'>
          <Textarea
            value={robotsTxt}
            placeholder='Start typing...'
            onChange={(e) => setRobotsTxt(e.target.value)}
            rows={4}
          />
          <Button variant='primary' onClick={handleSave} isLoading={isLoading}>
            Save
          </Button>
        </Box>
      </FormControlComp> */}
    </Box>
  )
}

export default SEO
