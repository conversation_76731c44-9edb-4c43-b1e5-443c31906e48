import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Checkbox, Tooltip } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import { getLocaleFullName } from '../../../Crosspost/utils'
import { SingleLineEditor } from '../../../InputFields/SingleLine'
import FormControlComp from '../../../Shared/FormControlComp'

interface Props {
  sdk: EditorAppSDK
  locale: string[]
  domain: string
}

function SEO(props: Props) {
  const { sdk, locale } = props

  const sdkFields = sdk.entry.fields

  // const [robotsTxt, setRobotsTxt] = useState('')

  // const [isLoading, setIsLoading] = useState(false)

  const [values, setValues] = useState<any>({})

  /**
   * Retrieves the value of a specified field for the 'en-CA' locale.
   * @param {string} fieldName - The name of the field to retrieve the value for.
   * @returns {any} The value of the field for the specified locale.
   */

  const getFieldValueByFieldName = (fieldName: string) =>
    sdkFields?.[fieldName]?.getForLocale('en-CA')?.getValue()

  /**
   * Handles the change of a checkbox field and updates the entry.
   * The method also updates the `values` state with the new checkbox value.
   * @param {string} fieldName The name of the field.
   * @param {any} value The checkbox value.
   */
  const handleCheckBox = (fieldName: string, value: any) => {
    sdkFields?.[fieldName]?.getForLocale('en-CA')?.setValue(value)

    setValues((prev: any) => {
      return {
        ...prev,
        [fieldName]: value,
      }
    })
    sdk.entry.save()
  }

  // const handleSave = async () => {
  //   if (!domain)
  //     Notification.error('Please select domain from the domains dropdown')
  //   setIsLoading(true)
  //   await updateRobotsTxt(robotsTxt, domain)
  //   setIsLoading(false)
  // }

  /**
   * Updates the isTranslated field of the configurations field
   * whenever a field value changes and is different from the
   * existing value.
   *
   * @param {any} value - The new value of the field
   * @param {string} fieldId - The ID of the field
   * @param {string} locale - The locale of the field
   */
  const handleFieldValueChangeToUpdateConfigurations = (
    value: any,
    fieldId: string,
    locale: string
  ) => {
    const fieldValue = sdkFields?.[fieldId]?.getForLocale(locale)?.getValue()

    const isValueSame = JSON.stringify(fieldValue) === JSON.stringify(value)

    if (isValueSame) return

    let x = sdkFields?.['configurations']?.getValue()

    x = {
      ...x,
      isTranslated: false,
    }

    sdkFields?.['configurations']?.setValue(x)

    sdk.entry.save()
  }

  // Fetch the noFollow and noIndex data and set the state
  useEffect(() => {
    if (sdk) {
      const noFollow = getFieldValueByFieldName('noFollow')

      const noIndex = getFieldValueByFieldName('noIndex')

      setValues({
        noFollow,
        noIndex,
      })
    }
  }, [sdk])

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-5 p-3 flex-col'>
      {/* <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <FormControlComp
            label={`Page Title (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the page title displayed on the users browser.`}
            key={locale}
            isRequired={locale === 'en-CA' && sdkFields?.['title']?.required}
          >
            <SingleLineEditor
              locales={sdk.locales}
              field={sdkFields?.['title']?.getForLocale(locale)}
              onValueChange={handleFieldValueChangeToUpdateConfigurations}
            />
          </FormControlComp>
        ))}
      </Box> */}

      <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <FormControlComp
            label={`SEO Title (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the SEO title displayed on search engines.`}
            key={locale}
            isRequired={locale === 'en-CA' && sdkFields?.['seoTitle']?.required}
          >
            <SingleLineEditor
              locales={sdk.locales}
              field={sdkFields?.['seoTitle']?.getForLocale(locale)}
              onValueChange={handleFieldValueChangeToUpdateConfigurations}
            />
          </FormControlComp>
        ))}
      </Box>

      <Box className='flex flex-col items-start justify-start w-full gap-5'>
        {locale.map((locale) => (
          <FormControlComp
            label={`SEO Description (${locale.split('-')?.[0]?.toUpperCase()})`}
            tooltip={`Add the ${getLocaleFullName(
              locale.split('-')?.[0]
            )} version of the SEO meta desciption that will display on search engines.`}
            key={locale}
            isRequired={
              locale === 'en-CA' && sdkFields?.['seoDescription']?.required
            }
          >
            <SingleLineEditor
              locales={sdk.locales}
              field={sdkFields?.['seoDescription']?.getForLocale(locale)}
              onValueChange={handleFieldValueChangeToUpdateConfigurations}
            />
          </FormControlComp>
        ))}
      </Box>

      {/* <FormControlComp
        label='SEO Tags'
        isRequired={sdkFields?.['seoKeywords']?.required}
        tooltip='Add relevant keywords as tags that describe the content of the page, helping improve its search engine visibility.'
      >
        <TagsEditor
          field={sdkFields?.['seoKeywords']?.getForLocale('en-CA')}
          isInitiallyDisabled
        />
      </FormControlComp> */}
      <Box className='flex justify-start items-center pb-2.5'>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['noFollow']}
            onChange={(e) => handleCheckBox('noFollow', !values?.['noFollow'])}
            className='p-0'
          >
            Add no follow tag
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to instruct crawlers not to follow links from the page as part of crawling.'
          >
            <Info />
          </Tooltip>
        </Box>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['noIndex']}
            onChange={(e) => handleCheckBox('noIndex', !values?.['noIndex'])}
            className='p-0'
          >
            Add no index tag
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to prevent this page from being indexed and appearing on search engines.'
          >
            <Info />
          </Tooltip>
        </Box>
      </Box>
      {/* <FormControlComp
        label={`Robots.txt`}
        isRequired={true}
        tooltip='Enable to allow search engine crawlers to access and crawl this URL.'
      >
        <Box className='flex items-end justify-start w-full gap-3'>
          <Textarea
            value={robotsTxt}
            placeholder='Start typing...'
            onChange={(e) => setRobotsTxt(e.target.value)}
            rows={4}
          />
          <Button variant='primary' onClick={handleSave} isLoading={isLoading}>
            Save
          </Button>
        </Box>
      </FormControlComp> */}
    </Box>
  )
}

export default SEO
