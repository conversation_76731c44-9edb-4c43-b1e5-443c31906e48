/* eslint-disable react-hooks/exhaustive-deps */
import { Badge, Box } from '@contentful/f36-components'
import React from 'react'

interface CopyFromList {
  fieldId: string
  value: {
    [key: string]: string
  }
}

interface Props {
  copyFromList: CopyFromList[]
  handleCopyFrom: (
    value: {
      [key: string]: string
    },
    fieldId: string
  ) => void
  fieldId: string
  children: any
  focusField: {
    fieldId: string
    locale: string
  }
}

function CopyFromListComponent(props: Props) {
  const { copyFromList, handleCopyFrom, fieldId, children, focusField } = props

  const getMaxWidth = () => {
    let maxWidth = copyFromList.map((copyFrom) =>
      getTextWidth(keyMapping(copyFrom?.fieldId))
    )

    return Math.max(...maxWidth) + 30
  }

  return (
    <div className='flex items-start gap-3 w-full'>
      {children}
      {focusField?.fieldId === fieldId && (
        <div className='flex items-start justify-start gap-1 flex-col w-[500px]'>
          <p>Copy Values From</p>
          <Box className='flex bg-white rounded-lg flex-col gap-1 w-full group'>
            {copyFromList.map((copyFrom) => {
              return (
                <Box
                  key={copyFrom?.fieldId}
                  className='w-full flex items-start gap-0.5 h-full'
                  onClick={() => handleCopyFrom(copyFrom?.value, fieldId)}
                >
                  <Box
                    className='max-w-1/3 p-1 select-none'
                    style={{
                      minWidth: getMaxWidth(),
                    }}
                  >
                    <Badge variant='primary-filled'>
                      {keyMapping(copyFrom?.fieldId)}:
                    </Badge>
                  </Box>
                  <Box className='cursor-pointer text-[13px] bg-slate-200 p-1 rounded-[5px] w-full truncate hover:whitespace-normal hover:overflow-visible transition-all duration-300'>
                    {copyFrom?.value?.['en-CA']}
                  </Box>
                </Box>
              )
            })}
          </Box>
        </div>
      )}
    </div>
  )
}

export default CopyFromListComponent

const keyMapping = (key: string) => {
  switch (key) {
    case 'title':
      return 'Page Title'
    case 'afsCardTitle':
      return 'Afs Card Title'
    case 'seoTitle':
      return 'SEO Title'
    case 'seoDescription':
      return 'SEO Description'

    default:
      return key
  }
}

function getTextWidth(text: string, fontSize: string = '12px'): number {
  const fontFamily =
    '-apple-system,BlinkMacSystemFont,Segoe UI,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol'

  // Create an off-screen canvas for measuring text
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')

  if (context) {
    // Set the font with the provided size and family
    context.font = `${fontSize} ${fontFamily}`

    // Measure the width of the text
    const metrics = context.measureText(text)
    return metrics.width
  }

  return 0 // Return 0 if context is not available
}
