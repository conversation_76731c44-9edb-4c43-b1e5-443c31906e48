import { EntryCard, MenuItem } from '@contentful/f36-components'
import React from 'react'
import { AssetThumbnail } from '../../../InputFields/Reference'

/**
 * A component that renders a card for a Contentful entry.
 *
 * @param {Object} props The component props.
 * @param {Object} props.data The Contentful entry to be rendered.
 * @param {function} [props.onRemoveEntry] The function to be called when the user clicks the "Remove" button.
 * @param {string} [props.field] The ID of the field to be used when calling `onRemoveEntry`.
 * @param {Object} [props.file] The file to be rendered as the card's thumbnail.
 * @param {boolean} [props.isActions=true] Whether to render the "Edit" and "Remove" actions.
 *
 * @returns {React.ReactElement} The rendered component.
 */
function CustomEntryCard({ data, onRemoveEntry, field, file, isActions=true }: any) {
  const handleOnEdit = () => {
    let url = data.sys.urn.split(':::content:')[1]

    const final = 'https://app.contentful.com/' + url

    window.open(final, '_blank')
  }

  let actions:any[] = []

  if(isActions) {
    actions = [
      <MenuItem key='copy' onClick={handleOnEdit}>
        Edit
      </MenuItem>,
      <MenuItem
        key='delete'
        onClick={() => onRemoveEntry(field, data?.sys?.id)}
      >
        Remove
      </MenuItem>,
    ]
  }

  return (
    <EntryCard
      status={data?.sys?.fieldStatus?.['*']?.['en-CA']}
      contentType={data?.sys?.contentType?.sys?.id}
      title={data?.fields?.internalName?.['en-CA']}
      actions={actions}
      thumbnailElement={file ? <AssetThumbnail file={file} /> : <></>}
      description={data?.fields?.template?.['en-CA']}
    />
  )
}

export default CustomEntryCard
