import * as React from 'react'

import { Button } from '@contentful/f36-components'
import { ChevronDownIcon, PlusIcon } from '@contentful/f36-icons'
import tokens from '@contentful/f36-tokens'
import { css } from 'emotion'
import get from 'lodash/get'

import { ContentType } from '../../types'
import { CreateEntryMenuTrigger } from './CreateEntryMenuTrigger'

const standardStyles = {
  spinnerMargin: css({
    marginRight: tokens.spacingXs,
  }),
  action: undefined,
}
const redesignStyles = {
  ...standardStyles,
  action: css({
    textDecoration: 'none',
    fontWeight: 'bold',
    maxWidth: '300px',
  }),
}

interface CreateEntryLinkButtonProps {
  contentTypes: ContentType[]
  suggestedContentTypeId?: string
  onSelect: (contentTypeId: string) => Promise<unknown>
  customDropdownItems?: React.ReactNode
  disabled?: boolean
  hasPlusIcon?: boolean
  useExperimentalStyles?: boolean
  text?: string | React.ReactElement
  testId?: string
  dropdownSettings?: {
    isAutoalignmentEnabled?: boolean
    position: 'bottom-left' | 'bottom-right'
  }
}

/**
 * Component for rendering a button that facilitates creating and linking new entries.
 *
 * Props:
 * - `contentTypes` (ContentType[]): List of content types to be displayed in the dropdown.
 * - `suggestedContentTypeId` (string | undefined): ID of the suggested content type to be displayed as default.
 * - `onSelect` (function): Callback function invoked when a content type is selected.
 * - `customDropdownItems` (React.ReactNode | undefined): Custom items to include in the dropdown menu.
 * - `disabled` (boolean): Flag to disable the button.
 * - `hasPlusIcon` (boolean): Determines if a plus icon should be displayed on the button.
 * - `useExperimentalStyles` (boolean): Flag to use experimental styles for the component.
 * - `text` (string | React.ReactElement | undefined): Custom text or element to display on the button.
 * - `testId` (string | undefined): Test ID for the button.
 * - `dropdownSettings` (object | undefined): Configuration settings for the dropdown menu alignment.
 *
 * The button displays a dropdown when there are multiple content types or custom dropdown items.
 * It shows a loading state when a content type is being selected.
 */

export const CreateEntryLinkButton = ({
  contentTypes,
  onSelect,
  customDropdownItems,
  text,
  testId,
  hasPlusIcon = false,
  useExperimentalStyles,
  suggestedContentTypeId,
  dropdownSettings,
  disabled = false,
}: CreateEntryLinkButtonProps) => {
  contentTypes = contentTypes.sort((a, b) => a.name.localeCompare(b.name))
  const suggestedContentType = contentTypes.find(
    (ct) => ct.sys.id === suggestedContentTypeId
  )
  const buttonText =
    text ||
    `Add ${get(
      suggestedContentType ||
        (contentTypes.length === 1 ? contentTypes[0] : {}),
      'name',
      'entry'
    )}`
  const hasDropdown = contentTypes.length > 1 || customDropdownItems

  // TODO: Introduce `icon: string` and remove `hasPlusIcon` or remove "Plus" if we keep new layout.
  const plusIcon = hasPlusIcon ? <PlusIcon /> : undefined
  // TODO: Always use "New content" here if we fully switch to new layout.
  const contentTypesLabel = useExperimentalStyles ? 'New content' : undefined
  const styles = useExperimentalStyles ? redesignStyles : standardStyles

  return (
    <CreateEntryMenuTrigger
      contentTypes={contentTypes}
      suggestedContentTypeId={suggestedContentTypeId}
      contentTypesLabel={contentTypesLabel}
      onSelect={onSelect}
      testId={testId}
      dropdownSettings={dropdownSettings}
      customDropdownItems={customDropdownItems}
    >
      {({ isSelecting }) => (
        <Button
          endIcon={hasDropdown ? <ChevronDownIcon /> : undefined}
          variant='secondary'
          className={styles.action}
          isDisabled={disabled || isSelecting} // (contentTypes && contentTypes.length === 0)}
          startIcon={isSelecting ? undefined : plusIcon}
          size='small'
          testId='create-entry-link-button'
          isLoading={isSelecting}
        >
          {buttonText}
        </Button>
      )}
    </CreateEntryMenuTrigger>
  )
}
