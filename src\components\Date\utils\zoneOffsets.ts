export const defaultZoneOffset = '+00:00'

export const zoneOffsets = [
  '-12:00',
  '-11:00',
  '-10:00',
  '-09:30',
  '-09:00',
  '-08:00',
  '-07:00',
  '-06:00',
  '-05:00',
  '-04:30',
  '-04:00',
  '-03:30',
  '-03:00',
  '-02:00',
  '-01:00',
  '+00:00',
  '+01:00',
  '+02:00',
  '+03:00',
  '+03:30',
  '+04:00',
  '+04:30',
  '+05:00',
  '+05:30',
  '+05:45',
  '+06:00',
  '+06:30',
  '+07:00',
  '+08:00',
  '+08:45',
  '+09:00',
  '+09:30',
  '+10:00',
  '+10:30',
  '+11:00',
  '+11:30',
  '+12:00',
  '+12:45',
  '+13:00',
  '+14:00',
]

export const zoneOffsetsWithRegions = [
  { offset: '-12:00', region: 'Baker Island' },
  { offset: '-11:00', region: 'Pago Pago' },
  { offset: '-10:00', region: 'Hawaii' },
  { offset: '-09:30', region: 'Marquesas Islands' },
  { offset: '-09:00', region: 'Alaska' },
  { offset: '-08:00', region: 'Los Angeles' },
  { offset: '-07:00', region: 'Denver' },
  { offset: '-06:00', region: 'Chicago' },
  { offset: '-05:00', region: 'New York / Toronto' },
  { offset: '-04:30', region: 'Caracas' },
  { offset: '-04:00', region: 'Santiago / La Paz' },
  { offset: '-03:30', region: `St. John’s` },
  { offset: '-03:00', region: 'Buenos Aires' },
  { offset: '-02:00', region: 'South Georgia' },
  { offset: '-01:00', region: 'Azores' },
  { offset: '+00:00', region: 'London / Dublin' },
  { offset: '+01:00', region: 'Paris / Berlin' },
  { offset: '+02:00', region: 'Athens / Cairo' },
  { offset: '+03:00', region: 'Moscow / Riyadh' },
  { offset: '+03:30', region: 'Tehran' },
  { offset: '+04:00', region: 'Dubai / Baku' },
  { offset: '+04:30', region: 'Kabul' },
  { offset: '+05:00', region: 'Karachi / Tashkent' },
  { offset: '+05:30', region: 'New Delhi' },
  { offset: '+05:45', region: 'Kathmandu' },
  { offset: '+06:00', region: 'Dhaka / Almaty' },
  { offset: '+06:30', region: 'Yangon' },
  { offset: '+07:00', region: 'Bangkok / Jakarta' },
  { offset: '+08:00', region: 'Beijing / Singapore' },
  { offset: '+08:45', region: 'Eucla' },
  { offset: '+09:00', region: 'Tokyo / Seoul' },
  { offset: '+09:30', region: 'Adelaide / Darwin' },
  { offset: '+10:00', region: 'Sydney / Brisbane' },
  { offset: '+10:30', region: 'Lord Howe Island' },
  { offset: '+11:00', region: 'Solomon Islands' },
  { offset: '+11:30', region: 'Norfolk Island' },
  { offset: '+12:00', region: 'Auckland / Fiji' },
  { offset: '+12:45', region: 'Chatham Islands' },
  { offset: '+13:00', region: 'Samoa / Tonga' },
  { offset: '+14:00', region: 'Kiritimati' },
];
