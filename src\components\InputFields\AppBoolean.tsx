import { FieldAppSDK } from '@contentful/app-sdk';
import { Radio, Stack } from '@contentful/f36-components';
import React from 'react';

// Define the prop types for the component
interface AppBooleanI {
  sdk: FieldAppSDK;
  fieldId: string;
  setValues: (newValue: any) => void; // Update the argument type to void
  values: any;
}

const AppBoolean = (props: AppBooleanI) => {
  const { sdk, fieldId, setValues, values } = props;

  // Get the current value from the 'values' object
  const value = values[fieldId];

  // Handle the change event when a radio button is selected
  const handleChange = (newValue: boolean) => {
    setValues(prev => {

      // Update the value of the selected radio button
      const updatedVal = { ...prev, [fieldId]: !!newValue };
      
      // Update the field value using the Contentful SDK
      sdk.field.setValue(updatedVal);
      return updatedVal;
    });
  };

  return (
    <Stack flexDirection="row">
      {/* First radio button for 'Yes' */}
      <Radio
        id="radio1"
        name="radio-controlled"
        value={true}
        isChecked={value === true}
        onChange={() => handleChange(true)}
      >
        Yes
      </Radio>

      {/* Second radio button for 'No' */}
      <Radio
        id="radio2"
        name="radio-controlled"
        value={false}
        isChecked={value === false}
        onChange={() => handleChange(false)}
      >
        No
      </Radio>
    </Stack>
  );
};

export default AppBoolean;
