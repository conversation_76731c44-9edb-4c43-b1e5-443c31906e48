interface Daum {
  masterPage: string
  experimentDescription: string
  experimentTitle: string
  experimentationPages: ExperimentationPage[]
  experimentationId: string
  date?: string
  isPublished?: boolean
  updatedAt?: string
  isDeleted?: boolean
  isShiped?: boolean
  shipedPageId?: string
  revisionHistory?: RevisionHistory[] // New field for tracking revisions
}

interface RevisionHistory {
  revisionId: string // Unique ID for this revision
  date: string // When this revision was made
  previousVariants?: ExperimentationPage[] // Previous variant states
  updatedVariant: ExperimentationPage // Selected winner variant
}

interface PageEntry {
  slug: string
}
interface FilterCondition {
  [key: string]: any
}

interface ExperimentationPage {
  title?: string
  slug?: string
  internalName: string
  link: string
  id: string
  url?: string
  status?: string // Example: 'active', 'inactive'
  PageStatusExpWise?: string
  isActive?: boolean // Indicates whether the variant is active
}

interface ExperimentationData {
  experimentationId: string
  experimentationType?: string
  experimentationPages?: ExperimentationPage[]
  historyCount?: number
  [key: string]: any
}

interface ExperimentRootData {
  data?: ExperimentationData[]
}

interface ResponseExperimentCRUD<T> {
  data: T // The response data
  status: number // Status code of the response
}

interface ExperimentInputData {
  masterPage: string
  experimentDescription: string
  experimentTitle: string
  // end_time: string;
  // start_time: string;
  experimentationPages: ExperimentationPage[]
}

interface StatsigExperimentGroup {
  name: string
  size: number
  // variables: Record<string, string>
}

interface StatsigExperimentData {
  name: string
  description: string
  type: string
  hypothesis?: string
  groups?: StatsigExperimentGroup[]
  // start_time: string;
  // end_time: string;
}

interface ExperimentGroup {
  name: string
  size: number
  variables: Record<string, string>
}

interface ExperimentData {
  name: string
  description: string
  type: string
  groups: ExperimentGroup[]
  // start_time: string;
  // end_time: string;
}
