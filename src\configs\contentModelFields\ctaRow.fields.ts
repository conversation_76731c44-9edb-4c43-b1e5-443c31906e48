export const CTARowFields = {
  Multiple: [
    'heading',
    'buttonGroup',
    'description',
    'calloutInfoCard',
    'isLightMode',
    'htmlAttr',
  ],
  'Single - Color Fill': [
    'heading',
    'description',
    'buttonGroup',
    'isLightMode',
    'htmlAttr',
  ],
  'Single - Background Image': [
    'heading',
    'description',
    'buttonGroup',
    'backgroundImage',
    'isBlur',
    'htmlAttr',
  ],
  'Insight - Primary': [
    'heading',
    'description',
    'link',
    'backgroundImage',
    'isLightMode',
    'htmlAttr',
  ],
  'Insight - Secondary': [
    'heading',
    'description',
    'link',
    'image',
    'isLightMode',
  ],
  'Insight - Single Col': [
    'heading',
    'description',
    'link',
    'backgroundImage',
    'isLightMode',
    'htmlAttr',
  ],
  'CTA Row - Home': [
    'heading',
    'subheading',
    'description',
    'link',
    'backgroundImage',
  ],
  'Insight - Home': ['heading', 'description', 'link', 'isLightMode'],
  'CTARow - Featured': [
    'heading',
    'subheading',
    'description',
    'buttonGroup',
    'backgroundImage',
    'isLightMode',
    'isBlur',
  ],
  'CTARow - Solutions': [
    'description',
    'form',
    'backgroundImage',
    'htmlAttr',
    'isFormFloating'
  ]
}
