export const heroFields = {
  SingleDisplay: [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'buttons',
    'bgImage',
    'showDropdown',
    'showDropdownIcon',
    'showBreadcrumbs',
    'dropdownList',
    'isLightMode',
    'isBgImageEffect',
    'height',
    'htmlAttr',
  ],
  SingleGeneric: [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'buttons',
    'bgImage',
    'showDropdown',
    'showDropdownIcon',
    'showBreadcrumbs',
    'dropdownList',
    'isLightMode',
    'isBgImageEffect',
    'height',
    'htmlAttr',
  ],
  SingleBGColour: [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'buttons',
    'showDropdown',
    'showDropdownIcon',
    'showBreadcrumbs',
    'dropdownList',
    'isLightMode',
    'height',
    'htmlAttr',
  ],
  SingleLower: [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'buttons',
    'bgImage',
    'showDropdown',
    'showDropdownIcon',
    'showBreadcrumbs',
    'dropdownList',
    'isBgImageEffect',
    'isLightMode',
    'isHeroOverlay',
    'height',
    'htmlAttr',
  ],
  SingleDynamic: [
    'breadCrumb',
    'heading',
    'subHeading',
    'dynamicRichText',
    'buttons',
    'dynamicImages',
    'showBreadcrumbs',
    'isLightMode',
    'height',
    'htmlAttr',
    'isBgImageEffect',
  ],
  'Twocolumn - Primary': [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'logo',
    'buttons',
    'bgImage',
    'showDropdown',
    'showDropdownIcon',
    'showBreadcrumbs',
    'dropdownList',
    'isLightMode',
    'height',
    'htmlAttr',
  ],
  'Twocolumn - Secondary': [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'buttons',
    'bgImage',
    'showDropdown',
    'showDropdownIcon',
    'showBreadcrumbs',
    'dropdownList',
    'isLightMode',
    'height',
    'htmlAttr',
  ],
  'Twocolumn - Tertiary': [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'bgImage',
    'buttons',
    'bgImage',
    'objectFit',
    'showDropdown',
    'showDropdownIcon',
    'showBreadcrumbs',
    'dropdownList',
    'isLightMode',
    'height',
    'htmlAttr',
  ],
  CustomerStories: [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'buttons',
    'logo',
    'menuList',
    'showBreadcrumbs',
    'isLightMode',
    'height',
    'htmlAttr',
  ],
  InsightArticle: [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'buttons',
    'bgImage',
    'showBreadcrumbs',
    'isLightMode',
    'height',
    'htmlAttr',
  ],
  InsightsGeneric: [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'insightsCarouselData',
    'subHeading',
    'insightsCard',
    'showBreadcrumbs',
    'isLightMode',
    'height',
  ],
  Person: [
    'breadCrumb',
    'heading',
    'subHeading',
    'richDescription',
    'buttons',
    'description',
    'bgImage',
    'showBreadcrumbs',
    'isLightMode',
    'height',
    'htmlAttr',
  ],
  Dropdown: [
    'dropdownList',
    'heading',
    'subHeading',
    'richDescription',
    'description',
    'buttons',
    'bgImage',
    'isLightMode',
    'height',
    'htmlAttr',
  ],
  HeroHome: [
    'heading',
    'subHeading',
    'richDescription',
    'buttons',
    'bgImage',
    'height',
  ],
  HeroArgus: [
    'richDescription',
    'button',
    'bgImage',
    'videoPlayer',
    'afs'
  ],
  HeroHomePage: ['heading', 'description', 'button', 'height'],
  'Software - Form': [
    'heading',
    'richDescription',
    'bgImage',
    'videoPlayer',
    'logoShowcase',
    'imageTabs',
    'form',
    'isHeroOverlay',
    'htmlAttr'
  ],
  'Software - Button Group': [
    'heading',
    'richDescription',
    'buttons',
    'bgImage',
    'videoPlayer',
    'logoShowcase',
    'imageTabs',
    'isHeroOverlay',
    'htmlAttr'
  ],
  'Software - Form Inline': [
    'richDescription',
    'form',
    'bgImage',
    'videoPlayer',
    'logoShowcase',
    'htmlAttr',
    'isFormFloating',
    'enableExtended',
    'showDropdown',
'showDropdownIcon',
    'dropdownList',
    'isLightMode'
  ],
}