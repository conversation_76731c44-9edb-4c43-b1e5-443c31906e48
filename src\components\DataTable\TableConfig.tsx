import {
  Box,
  Checkbox,
  FormControl,
  Select,
  TextInput,
  Tooltip
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../assets/icons/Info'
import './index.scss'
import { tableDefaults } from './utils'
  
  function TableConfig(props: any) {
    const { onChange, changedData } = props
  
    const [data, setData] = useState<any>({})

    useEffect(() => {
      setData((pre) => {
        return {
          ...pre,
          ...tableDefaults,
          ...changedData,
        }
      })
    }, [])
  
    const handleTableDataChange = (key: string, value: string | boolean) => {
      const dataToUpdate = {
        ...data,
        [key]: value,
      }
  
      setData(dataToUpdate)
  
      const changedDataToUpdate = {
        ...changedData,
        [key]: value,
      }
     
      onChange({ ...changedDataToUpdate })
    }
  
    return (
          <Box>
        <FormControl
          id='dv-pri-table-id'
          className='w-100 fieldsFormControl'
        >
          <div className='SwithWithTooltip'>
            <FormControl.Label>Table ID</FormControl.Label>
            <Tooltip
              placement='top'
              id='dv-pri-table-id-tooltip'
              content='Add anchor text able to share this table with external users.'
            >
              <Info />
            </Tooltip>
          </div>
          <TextInput
            value={data?.tableId}
            onChange={(e) =>
              handleTableDataChange('tableId', e.target.value)
            }
            placeholder='insert-table-id-in-this-format'
          />
        </FormControl>
            <FormControl id='dv-border-type' className='w-100 fieldsFormControl'>
              <FormControl.Label>Size</FormControl.Label>
              <Select
                id='borderStyle-controlled'
                name='borderStyle-controlled'
                value={data.size || ''}
                onChange={(e) =>
                  handleTableDataChange('size', e.target.value)
                }
                style={{
                  width: '100%',
                }}
              >
                <Select.Option value='' isDisabled>
                  size
                </Select.Option>
                {['xl', 'lg', 'md', 'sm' ,'xs'].map((type) => (
                  <Select.Option value={type}>{type}</Select.Option>
                ))}
              </Select>
            </FormControl>
            <FormControl
          id='dv-isSortable'
          className='w-100 fieldsFormControl'
        >
          <div className='SwithWithTooltip'>
            <Checkbox
              name='dv-isSortable-Checkbox'
              id='dv-isSortable-Checkbox'
              onChange={() =>
                handleTableDataChange(
                  'isSortable',
                  !data.isSortable
                )
              }
              className='switchRoot'
              isChecked={data.isSortable}
            >
              Enable Sortable
            </Checkbox>
          </div>
        </FormControl>
        <FormControl
            id='dv-isSelectable'
            className='w-100 fieldsFormControl'
          >
            <div className='SwithWithTooltip'>
              <Checkbox
                name='dv-isSelectable-Checkbox'
                id='dv-isSelectable-Checkbox'
                onChange={() =>
                  handleTableDataChange(
                    'isSelectable',
                    !data.isSelectable
                  )
                }
                className='switchRoot'
                isChecked={data.isSelectable}
              >
              Enable Selectable
              </Checkbox>
            </div>
          </FormControl>
          <FormControl
            id='dv-isExpandable'
            className='w-100 fieldsFormControl'
          >
            <div className='SwithWithTooltip'>
              <Checkbox
                name='dv-isExpandable-Checkbox'
                id='dv-isExpandable-Checkbox'
                onChange={() =>
                  handleTableDataChange(
                    'isExpandable',
                    !data.isExpandable
                  )
                }
                className='switchRoot'
                isChecked={data.isExpandable}
              >
              Enable Expandable
              </Checkbox>
            </div>
          </FormControl>
          <FormControl
            id='dv-isZebraStrip'
            className='w-100 fieldsFormControl'
          >
            <div className='SwithWithTooltip'>
              <Checkbox
                name='dv-isZebraStrip-Checkbox'
                id='dv-isZebraStrip-Checkbox'
                onChange={() =>
                  handleTableDataChange(
                    'isZebraStrip',
                    !data.isZebraStrip
                  )
                }
                className='switchRoot'
                isChecked={data.isZebraStrip}
              >
              Enable ZebraStripe
              </Checkbox>
            </div>
          </FormControl>
        <FormControl
          id='dv-isPagination'
          className='w-100 fieldsFormControl'
        >
          <div className='SwithWithTooltip'>
            <Checkbox
              name='dv-isPagination-Checkbox'
              id='dv-isPagination-Checkbox'
              onChange={() =>
                handleTableDataChange(
                  'isPagination',
                  !data.isPagination
                )
              }
              className='switchRoot'
              isChecked={data.isPagination}
            >
              Enable pagination
            </Checkbox>
          </div>
        </FormControl>
        <FormControl
          id='dv-pri-noDataErrorMsg'
          className='w-100 fieldsFormControl'
        >
          <div className='SwithWithTooltip'>
            <FormControl.Label>No Data Error Message</FormControl.Label>
          </div>
          <TextInput
            value={data?.noDataErrorMsg}
            onChange={(e) =>
              handleTableDataChange('noDataErrorMsg', e.target.value)
            }
            placeholder='No data found'
          />
        </FormControl>
          </Box>
    )
  }
  
  export default TableConfig
  