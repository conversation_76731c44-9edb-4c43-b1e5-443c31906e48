import { TextInput } from '@contentful/f36-components'
import {
  Button,
  Modal,
  notification,
  Radio,
  Table,
  TableProps,
  Tooltip,
} from 'antd'
import React, { useEffect, useState } from 'react'
import { BiSolidTrashAlt } from 'react-icons/bi'
import { FaExternalLinkAlt } from 'react-icons/fa'
import { FaPlus } from 'react-icons/fa6'
import { LuSend } from 'react-icons/lu'
import { ENV_VARIABLES } from '../../../../constant/variables'
import {
  generateRandomId,
  sendMessage,
} from '../../../../globals/firebase/utils'
import {
  getDomainDataByDomainName,
  SaveConfigurationandData,
} from '../../../../globals/utils'
import {
  fetchNotificationHistory,
  setIsAddNewNotification,
} from '../../../../redux/slices/dashboard/notificationsSlice'
import { useAppDispatch, useAppSelector } from '../../../../redux/store'
import PushNotificationDetailedView from '../components/PushNotificationDetailedView'
import {
  DELETE_NOTIFICATION_BODY,
  DELETE_NOTIFICATION_HEAD,
  DELETE_NOTIFICATION_SUCCESS,
  DELETE_NOTIFICATION_TOOLTIP,
  RESEND_NOTIFICATION_BODY,
  RESEND_NOTIFICATION_HEAD,
  RESEND_NOTIFICATION_SUCCESS,
  RESEND_NOTIFICATION_TOOLTIP,
} from '../components/constants'

type NotiType = 'realTime' | 'sticky'

function DomainNotificationHistoryTable({
  domain,
  updateTypeOfNotification,
}: {
  domain: string
  updateTypeOfNotification: (x: NotiType) => void
}) {
  const { dataSource, id } = useAppSelector((state) => state.notificationData)

  const [domainNotifications, setDomainNotifications] = React.useState([])

  const [notificationType, setNotificationType] =
    React.useState<NotiType>('realTime')

  const spaceId = ENV_VARIABLES.contentfulSpaceID

  const environment = ENV_VARIABLES.contentfulEnvironment

  const dispatch = useAppDispatch()

  const [search, setSearch] = useState('')

  useEffect(() => {
    // fetch notification history
    dispatch(fetchNotificationHistory('MSA'))
  }, [])

  useEffect(() => {
    // update the type of notification when user select realtime or sticky
    updateTypeOfNotification(notificationType)
  }, [notificationType])

  useEffect(() => {
    // effect to extract selected notifications type history from current domain
    if (dataSource?.[domain] && domain) {
      if (search !== '') {
        setDomainNotifications(
          dataSource?.[domain]?.[notificationType]?.filter((item: any) =>
            item?.title?.toLowerCase()?.includes(search?.toLowerCase())
          )
        )
      } else {
        setDomainNotifications(dataSource?.[domain]?.[notificationType] || [])
      }
    }
  }, [search, dataSource, domain, notificationType])

  /**
   * Deletes a notification from history in the selected domain, and updates the local state.
   * @param {string} notiId - The id of the notification to delete.
   */
  const handleDeleteNotificationById = async (notiId: string) => {
    const payload = {
      ...dataSource,
      [domain]: {
        ...dataSource?.[domain],
        [notificationType]: dataSource?.[domain]?.[notificationType].filter(
          (item: any) => item.id !== notiId
        ),
      },
    }

    await SaveConfigurationandData(payload, id ? id : '')

    // show success notification
    notification.success({
      message: DELETE_NOTIFICATION_SUCCESS,
      placement: 'topRight',
      duration: 5,
      showProgress: true,
    })
    dispatch(fetchNotificationHistory('MSA'))
  }

  /**
   * Confirmation modal to delete a notification from history.
   * @param {string} notiId - The id of the notification to delete.
   */
  const deleteModal = (notiId: string) => {
    Modal.confirm({
      title: DELETE_NOTIFICATION_HEAD,
      content: <p>{DELETE_NOTIFICATION_BODY}</p>,
      async onOk() {
        try {
          await handleDeleteNotificationById(notiId)
          Modal.destroyAll()
        } catch (error) {
          console.error('Deletion failed:', error)
          // Optionally throw to prevent modal from closing
          throw error
        }
      },
    })
  }

  /**
   * Confirmation modal to re-send a notification from history.
   * @param {Object} data - The notification data to re-send.
   */
  const reSendModal = (data: any) => {
    Modal.confirm({
      title: RESEND_NOTIFICATION_HEAD,
      content: <p>{RESEND_NOTIFICATION_BODY}</p>,
      async onOk() {
        try {
          await reSendNotification(data)
          Modal.destroyAll()
        } catch (error) {
          console.error('Deletion failed:', error)
          throw error
        }
      },
    })
  }

  /**
   * Resends a notification from history.
   * @param {Object} data - The notification data to re-send.
   * @returns {Promise<void>}
   */
  const reSendNotification = async (data: any) => {
    const res = await sendMessage(
      { ...data, timeStamp: new Date(), id: generateRandomId() },
      data?.domain
    )

    if (res?.message) {
      notification.success({
        message: RESEND_NOTIFICATION_SUCCESS,
        placement: 'topRight',
        duration: 5,
        showProgress: true,
      })
    }
  }

  // columns for realtime notifications
  const realTimeColumns: TableProps['columns'] = [
    {
      title: '',
      dataIndex: 'index',
      key: 'name',
      width: '10%',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      sorter: (a, b) => a.title.localeCompare(b.title),
      width: '40%',
    },
    {
      title: 'Description',
      dataIndex: 'body',
      key: 'body',
      sorter: (a, b) => a.body.localeCompare(b.body),
      width: '40%',
    },
    {
      title: 'Action',
      key: 'action',
      width: '10%',
      render: (_, record) => (
        <div className={'flex items-center'}>
          <PushNotificationDetailedView noti={record} />
          <Tooltip title={RESEND_NOTIFICATION_TOOLTIP}>
            <Button
              size={'large'}
              type='primary'
              className='text-neutral-950 bg-transparent border-none'
              icon={<LuSend />}
              onClick={() => {
                reSendModal(record)
              }}
            />
          </Tooltip>
          <Tooltip title={DELETE_NOTIFICATION_TOOLTIP}>
            <Button
              size={'large'}
              type='primary'
              className='text-neutral-950 bg-transparent border-none'
              icon={<BiSolidTrashAlt />}
              onClick={() => deleteModal(record?.id)}
            />
          </Tooltip>
        </div>
      ),
    },
  ]

  // columns for sticky notifications
  const stickyColumns: TableProps['columns'] = [
    {
      title: '',
      dataIndex: 'index',
      key: 'name',
      width: '60px',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      sorter: (a, b) => a.title.localeCompare(b.title),
      width: '300px',
    },
    {
      title: 'Template',
      dataIndex: 'stickyNotificationTemplate',
      key: 'stickyNotificationTemplate',
      sorter: (a, b) => a.body.localeCompare(b.body),
      width: '100px',
    },
    {
      title: 'Action',
      key: 'action',
      width: '100px',
      render: (_, record) => (
        <div className={'flex w-full items-center'}>
          <Tooltip title='Delete Notification'>
            <Button
              size={'large'}
              type='primary'
              className='text-neutral-950 bg-transparent border-none'
              icon={<BiSolidTrashAlt />}
              onClick={() => deleteModal(record?.id)}
            />
          </Tooltip>
          <Tooltip title='View Details'>
            <Button
              size={'large'}
              type='primary'
              className='text-neutral-950 bg-transparent border-none'
              icon={<FaExternalLinkAlt size={15} />}
              onClick={() => {
                window.open(
                  `https://app.contentful.com/spaces/${spaceId}/environments/${environment}/entries/${record.id}`
                )
              }}
            />
          </Tooltip>
        </div>
      ),
    },
  ]

  return (
    <div className={'w-full flex flex-col py-10 px-2'}>
      <div className='w-full flex flex-col xl:flex-row gap-3'>
        <div className='w-full xl:w-2/3 flex flex-col lg:flex-row items-start lg:items-center justify-start mb-0 xl:mb-5 gap-3'>
          <h3>{getDomainDataByDomainName(domain)?.label} Notifications</h3>
          <div className='flex justify-center items-center gap-3'>
            <Button
              type='primary'
              onClick={() => {
                dispatch(setIsAddNewNotification(true))
              }}
            >
              <FaPlus /> Create Notification
            </Button>
          </div>
        </div>
        <div className='w-1/3 flex justify-between mb-5'>
          <TextInput
            placeholder='Search'
            onChange={(e) => {
              setSearch(e?.target?.value ?? '')
            }}
          />
        </div>
      </div>
      <div>
        <Radio.Group
          value={notificationType}
          onChange={(e) => {
            setNotificationType(e.target.value as NotiType)
          }}
          style={{ marginBottom: 16 }}
        >
          <Radio.Button value='realTime'>Push Notification</Radio.Button>
          <Radio.Button value='sticky'>Sticky Notification</Radio.Button>
        </Radio.Group>
      </div>
      <Table
        columns={
          notificationType === 'realTime' ? realTimeColumns : stickyColumns
        }
        dataSource={domainNotifications}
        size={'small'}
        pagination={{
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
          defaultPageSize: 10,
          showQuickJumper: true,
          showPrevNextJumpers: true,
        }}
      />
    </div>
  )
}

export default DomainNotificationHistoryTable
