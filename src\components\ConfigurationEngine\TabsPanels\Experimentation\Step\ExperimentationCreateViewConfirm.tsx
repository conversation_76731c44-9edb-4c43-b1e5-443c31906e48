import { Box, Radio } from '@contentful/f36-components'
import React, { useEffect, useMemo } from 'react'
import { AiFillInfoCircle } from 'react-icons/ai'
import Info from '../../../../../assets/icons/Info'
import { Alert, Table, Tooltip } from '../../../../atoms'
import { EntryLink } from '../experiment-const'
import { ExperimentationCRUD } from '../experimentation-helper'

const ExperimentationStepTwo = ({
  data,
  payload,
  carouselData,
  isLoading,
  setIsLoading,
  ActiveDomain,
  setSelectedMetaDataIndex,
  selectedMetaDataIndex,
}: {
  data: ExperimentRootData
  carouselData?: any[]
  isLoading?: boolean
  setIsLoading?: Function
  ActiveDomain?: string
  payload?: Partial<ExperimentationData>
  setSelectedMetaDataIndex: Function
  selectedMetaDataIndex?: string | null
}) => {
  // const [selectedPage,setSelectedPage]= useState("")
  const historyCount = useMemo(() => {
    const dataOfExp = ExperimentationCRUD.getAll(data, {
      masterPage: payload?.masterPage?.toLowerCase(),
    })
    if (
      !dataOfExp?.data ||
      !Array.isArray(dataOfExp.data) ||
      !dataOfExp.data.length
    ) {
      return 0
    }
    return (
      dataOfExp?.data
        ?.map(({ experimentationPages = [] }) => experimentationPages)
        ?.flat(2)?.length ?? 0
    )
  }, [data, payload?.masterPage])
  useEffect(() => {
    setSelectedMetaDataIndex(null)
    return () => setSelectedMetaDataIndex(null)
  }, [])
  const handleRadioChange = (index: string) => {
    setSelectedMetaDataIndex(index) // Set the selected page as the promoted one
  }
  return (
    <Box
      className='summaryRoot formRoot'
      style={{ width: '100%', height: '96%' }}
    >
      <Alert
        icon={
          <AiFillInfoCircle className='!bg-accent9-100 !text-accent9-800' />
        }
        showIcon
        className='!bg-accent9-100 !text-accent9-800'
        message='Please review the experiment details are correct before continuing'
        // type='warning'
      />
      <div className='pr-5 mb-5'>
        <h3 className='text-xl font-semibold flex items-center gap-4 justify-between'>
          {payload?.experimentTitle}
        </h3>
        <p className='text-gray-500 mt-2'>{payload?.experimentDescription}</p>
        <div className='text-sm text-gray-400 mt-4'>
          <p className='font-medium text-gray-700'>
            <b>Routing URL : </b> {payload?.masterPage}
          </p>
        </div>
      </div>
      <Table
        dataSource={carouselData
          ?.filter((el) => el)
          ?.map((el) => ({
            ...el,
            id: el?.selected?.sys?.id,
            title: el?.selected?.fields?.title?.['en-CA'],
            description: el?.selected?.fields?.seoDescription?.['en-CA'],
            slug: el?.selected?.fields?.slug?.['en-CA'],
            link: el?.selected?.fields?.slug?.['en-CA'],
            internalName: el?.selected?.fields?.internalName?.['en-CA'],
          }))}
        pagination={false}
        scroll={{ x: 'auto' }}
        locale={{ emptyText: 'No data For Page To View' }}
        columns={[
          {
            title: 'Title',
            dataIndex: 'internalName',
            key: 'internalName',
            width: 150,
            render: (link: string, row) =>
              row?.id ? (
                <a
                  href={`${EntryLink}/${row?.id}`}
                  target='_blank'
                  rel='noopener noreferrer'
                  className='hover:text-blue-400 flex justify-between items-center'
                >
                  {`${row?.internalName ?? 'N/A'}`}
                </a>
              ) : (
                'N/A'
              ),
          },
          // {
          //   title: 'Internal URL',
          //   dataIndex: 'link',
          //   key: 'link',
          // render: (link: string, row) =>
          //   row?.slug ? (
          //     <a
          //       // href={row?.slug}
          //       href={`${ActiveDomain}${row?.slug}`}
          //       target='_blank'
          //       rel='noopener noreferrer'
          //       className='hover:text-blue-400 flex justify-between items-center'
          //     >
          //       {`${row?.slug}`}
          //     </a>
          //   ) : (
          //     'N/A'
          //   ),
          //   width: 200,
          // },
          {
            title: 'External URL',
            dataIndex: 'slug',
            key: 'slug',
            width: 10,
            render: (_, __, index) => {
              // return (
              //   <a
              //     href={`${ActiveDomain}${payload?.masterPage}?variant=${index+historyCount}`}
              //     target='_blank'
              //     rel='noopener noreferrer'
              //     className='hover:text-blue-400 flex justify-between items-center'
              //   >
              //     {`${payload?.masterPage}?variant=${index+historyCount}`}

              //   </a>
              // )
              return `${payload?.masterPage}?variant=${index + historyCount}`
            },
          },
          {
            title: (
              <Tooltip
                title={
                  'Select the variant that will be chosen as the refrence for title , description , keywords and SEO related things.'
                }
              >
                <p className='flex gap-3 justify-center items-center'>
                  SEO
                  <Info />
                </p>
              </Tooltip>
            ),
            dataIndex: 'id',
            key: 'id',

            render: (id: string) => (
              <div className='flex justify-center items-center'>
                <Radio
                  isChecked={selectedMetaDataIndex === id}
                  onChange={(e) => handleRadioChange(id)}
                />
              </div>
            ),
            width: 100,
          },
        ]}
      />
    </Box>
  )
}

export const handleUrlClick = (data: any) => {
  let url = data?.sys?.urn?.split(':::content:')?.[1]
  return 'https://app.contentful.com/' + url
}

export default ExperimentationStepTwo
