/* eslint-disable react-hooks/exhaustive-deps */
import { EditorAppSDK } from '@contentful/app-sdk'
import { Modal } from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { GlobalContext } from '../../contexts/globalContext'
import { getConfigurationsCollectionQuery } from '../../globals/queries'
import { fetchGraphQL } from '../../globals/utils'
import {
  fetchPageDataById,
  getAllCategorizedTags,
  getContentTypesForEnvironment,
} from '../../redux/slices/pageSettings'
import { RootState, useAppDispatch } from '../../redux/store'
import { getDomainShortName } from '../Crosspost/utils'
import { EntityProvider } from '../InputFields/Reference'
import ModalContent from './components/SubComponents/ModalContent'
import ModalHeader from './components/SubComponents/ModalHeader'
import './index.scss'
import {
  categoryFieldKeyToTranslate,
  HandleGroupTranslate,
} from './utils/SidebarGroupTranslationsHandler'
import { DYNAMIC_PAGE_PREFIX } from './components/DynamicPage/utils'

interface PageSettingsModalProps {
  sdk: EditorAppSDK
  entryId: string
}

function PageSettingsModal(props: PageSettingsModalProps) {
  const { entryId, sdk } = props

  const sdkFields = sdk.entry.fields

  const pageConfigurations = sdkFields?.['configurations']?.getValue() || {}

  let domainCode = pageConfigurations?.domain || ''

  const [groupTranslationLoading, setGroupTranslationLoading] = useState(false)

  // get domain code from page domain tag
  const getDomainFromPageDomainTag = () => {
    const tags = sdk.entry.getMetadata()?.tags
    const domainArray = tags?.filter((tag) => tag.sys.id.includes('domain'))
    if (domainArray && domainArray?.length > 0)
      return getDomainShortName(domainArray[0].sys.id)

    return ''
  }

  if (!domainCode) {
    // if domain code not found in page config then get it from page domain tag
    domainCode = getDomainFromPageDomainTag()
  }

  const [selectedDomain, setSelectedDomain] = useState<string>('')

  const [isCloseClicked, setIsCloseClicked] = useState(false)

  const { isPageSettingModalOpen, setIsPageSettingModalOpen } =
    useContext(GlobalContext)

  const [activeCategory, setActiveCategory] = useState<string | number>(
    'General'
  )

  const [allowedLocale, setAllowedLocale] = useState<string[]>(['en-CA'])

  const slugField = sdk.entry.fields['slug']
  // const isPageDynamic = normalizedSlug?.startsWith(DYNAMIC_PAGE_PREFIX)
  const isPageDynamic = allowedLocale?.every((locale) => {
    const slug = slugField.getValue(locale) || ''
    return getNormalizedSlug(slug).startsWith(DYNAMIC_PAGE_PREFIX)
  })
  const dispatch = useAppDispatch()

  const isClosable = useSelector(
    (state: RootState) => state.pageData.isPageSettingsModalClosable
  )

  // func to set if modal is closable or not (curr using for tag validations)
  const handleClose = () => {
    if (isClosable) {
      setIsPageSettingModalOpen(false)
      sdk.entry.save()
    }
    setIsCloseClicked(!isClosable)
  }

  // fetch allowed locale by select domain code
  const fetchData = async (domainCode: string) => {
    const res = await fetchGraphQL(getConfigurationsCollectionQuery()).then(
      (res: any) => res?.data?.configurationsCollection?.items
    )

    const matchedData = res?.find((item: any) => {
      return (
        item?.scope === domainCode?.toUpperCase() && item?.type === 'Locale'
      )
    })?.data?.json?.content?.[0]?.content?.[0]?.value

    if (!matchedData) return

    const data = JSON.parse(matchedData)

    setAllowedLocale(data?.allowedLanguages || ['en-CA'])
  }

  /**
   * Handle group translation click button functionality.
   * Translate the selected locale and all selected categories fields.
   * If selected locale is 'en-CA', skip the translation process.
   * @param {string} locale - The selected locale.
   */
  const handleGroupTranslationClick = async () => {
    setGroupTranslationLoading(true)

    let entries: any = []

    Object.entries(sdk.entry.fields).forEach(([key, value]) => {
      entries = [...entries, { key, value }]
    })

    const translationPromises = allowedLocale.map(async (locale) => {
      if (locale === 'en-CA') return // Skip 'en-CA'
      await HandleGroupTranslate(
        sdk,
        locale,
        entries,
        categoryFieldKeyToTranslate[
          activeCategory as keyof typeof categoryFieldKeyToTranslate
        ] || null // enable this we want to restrict the button translate only given active category's fields.
      )
    })

    try {
      await Promise.all(translationPromises.filter(Boolean))
    } catch (error) {
      console.error('Error handling group translation:', error)
    } finally {
      setGroupTranslationLoading(false)
    }
  }

  const actionHandler = { x: handleGroupTranslationClick }

  useEffect(() => {
    setIsCloseClicked(!isClosable)
  }, [isClosable])

  useEffect(() => {
    selectedDomain && fetchData(selectedDomain)
  }, [selectedDomain])

  useEffect(() => {
    dispatch(fetchPageDataById(entryId))
    dispatch(getAllCategorizedTags())
    dispatch(getContentTypesForEnvironment())
  }, [])

  useEffect(() => {
    domainCode && setSelectedDomain(domainCode)
  }, [domainCode])

  const pageSettingCategories = [
    {
      title: 'General',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'SEO',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'AFS',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'Search',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'Translate Group Settings',
      isEnabled: true,
      type: 'button',
      action: 'x',
      isLoading: groupTranslationLoading,
      loadingText: 'Translating...',
    },
    {
      type: 'divider',
    },
    {
      title: 'Navigation',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'Translations',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'Tags',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'Cache',
      isEnabled: false,
      type: 'category',
    },
    {
      title: 'Crosspost',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'Experimentation',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'Duplicator',
      isEnabled: true,
      type: 'category',
    },
    {
      title: 'Dynamic Page',
      isEnabled: true,
      isDisabled: !isPageDynamic,
      disabledtooltip:
        'Dynamic Page settings are unavailable. Please check the "Is Dynamic Page" checkbox in the General Settings.',
      type: 'category',
    },
    {
      title: 'Migrations',
      isEnabled: false,
      type: 'category',
    },
  ]

  return (
    <EntityProvider sdk={sdk}>
      <Modal
        onClose={() => handleClose()}
        isShown={isPageSettingModalOpen}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        className='pageSettingModal'
      >
        {() => (
          <>
            <Modal.Header title='' onClose={() => handleClose()}>
              <ModalHeader isCloseClicked={isCloseClicked} sdk={sdk} />
            </Modal.Header>

            <Modal.Content className='pageSettingModalContent'>
              <ModalContent
                activeCategory={activeCategory}
                allowedLocale={allowedLocale}
                entryId={entryId}
                pageSettingCategories={pageSettingCategories}
                sdk={sdk}
                selectedDomain={selectedDomain}
                setActiveCategory={setActiveCategory}
                actionHandler={actionHandler}
              />
            </Modal.Content>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default PageSettingsModal

export const getNormalizedSlug = (slug: unknown): string => {
  if (typeof slug !== 'string') return ''
  return slug.startsWith('/') ? slug.slice(1) : slug
}
