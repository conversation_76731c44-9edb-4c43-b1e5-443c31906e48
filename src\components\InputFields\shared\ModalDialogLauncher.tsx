import * as React from 'react'
import ReactDOM from 'react-dom'

import { OpenCustomWidgetOptions } from '@contentful/app-sdk'
import { Modal, ModalHeader } from '@contentful/f36-components'
import isNumber from 'lodash/isNumber'

/**
 * Open a modal dialog with a custom component.
 *
 * @param componentRenderer - A function that takes an object with two properties: `onClose` and `isShown`.
 *   When the modal is closed, `onClose` is called with any arguments passed to it.
 *   When the modal is shown/hidden, `isShown` is set to `true`/`false`.
 *   The function should return a React component to render in the modal.
 * @returns {Promise} - A promise that resolves when the modal is closed,
 *   with the arguments passed to `onClose`.
 */
export function open(
  componentRenderer: (params: { onClose: Function; isShown: boolean }) => any
) {
  let rootDom: any = null

  /**
   * @returns {HTMLElement} The root element that modals are rendered into,
   *   creating it if it doesn't exist.
   */
  const getRoot = () => {
    if (rootDom === null) {
      rootDom = document.createElement('div')
      rootDom.setAttribute('id', 'field-editor-modal-root')
      document.body.appendChild(rootDom)
    }
    return rootDom
  }

  return new Promise((resolve) => {
    let currentConfig = { onClose, isShown: true }

    function render({
      onClose,
      isShown,
    }: {
      onClose: Function
      isShown: boolean
    }) {
      ReactDOM.render(componentRenderer({ onClose, isShown }), getRoot())
    }

    function onClose(...args: any[]) {
      currentConfig = {
        ...currentConfig,
        isShown: false,
      }
      render(currentConfig)
      // eslint-disable-next-line -- TODO: describe this disable  @typescript-eslint/ban-ts-comment
      // @ts-ignore
      resolve(...args)
      getRoot().remove()
    }

    render(currentConfig)
  })
}

/**
 * Opens a dialog with the given options and component.
 * @param {OpenCustomWidgetOptions} options Options to pass to the Modal component.
 * @param {React.SFC<{ onClose: (result: T) => void }>} Component The component to render
 * inside the modal.
 * @returns {Promise<T>} Resolves with the result given to the onClose callback.
 */
export function openDialog<T>(
  options: OpenCustomWidgetOptions,
  Component: React.SFC<{ onClose: (result: T) => void }>
) {
  const key = Date.now()
  const size = isNumber(options.width) ? `${options.width}px` : options.width
  return open(({ isShown, onClose }) => {
    const onCloseHandler = () => onClose()
    return (
      <Modal
        key={key}
        shouldCloseOnOverlayClick={options.shouldCloseOnOverlayClick || false}
        shouldCloseOnEscapePress={options.shouldCloseOnEscapePress || false}
        allowHeightOverflow={options.allowHeightOverflow || false}
        position={options.position || 'center'}
        isShown={isShown}
        onClose={onCloseHandler}
        size={size || '700px'}
      >
        {() => (
          <>
            {options.title && (
              <ModalHeader
                testId='dialog-title'
                title={options.title}
                onClose={onCloseHandler}
              />
            )}
            <div style={{ minHeight: options.minHeight || 'auto' }}>
              <Component onClose={onClose as any} />
            </div>
          </>
        )}
      </Modal>
    )
  })
}

export default {
  openDialog,
}
