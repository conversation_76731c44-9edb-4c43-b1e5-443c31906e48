import {
  FormControl,
  Tabs,
  Textarea,
  Tooltip,
} from '@contentful/f36-components'
import React from 'react'
import Info from '../../../../assets/icons/Info'
import { ComponentProps } from '../../interface'

function RobotsTxt(props: ComponentProps) {
  return (
    <Tabs.Panel id='robots' className='tabPanelRoot'>
      <FormControl
        id='original-domain'
        style={{
          padding: '1rem',
          paddingBottom: '0rem',
          paddingLeft: '0rem',
        }}
      >
        <div className='formLabelWithIcon'>
          <FormControl.Label>Robots Txt</FormControl.Label>
          <Tooltip
            placement='top'
            id='tooltip-1'
            content='Add scripts to the footer of the website.'
          >
            <Info />
          </Tooltip>
        </div>
        <Textarea
          placeholder='Start typing...'
          value={props.value}
          onChange={(e) => props.handleChange('robots', e.target.value)}
        />
      </FormControl>
    </Tabs.Panel>
  )
}

export default RobotsTxt
