import { FieldAPI } from '@contentful/app-sdk';
import isNumber from 'lodash/isNumber';

type NumberOfLinksValidation =
  | { type: 'min-max'; min: number; max: number }
  | { type: 'min'; min: number; max: undefined }
  | { type: 'max'; max: number; min: undefined }

export type ReferenceValidations = {
  contentTypes?: string[]
  mimetypeGroups?: string[]
  numberOfLinks?: NumberOfLinksValidation
}

  /**
   * Transforms field validations into a {@link ReferenceValidations} object.
   *
   * - `contentTypes` is extracted from `linkContentType` validation
   * - `mimetypeGroups` is extracted from `linkMimetypeGroup` validation
   * - `numberOfLinks` is extracted from `size` validation and is an object that indicates
   *   the type of validation and the min and max values for that type.
   *   The type of validation can be either `min-max`, `min`, or `max` and the min and max
   *   values are numbers.
   *   - `min-max` means that the linked entities must have a number of links between min and max
   *   - `min` means that the linked entities must have at least min links
   *   - `max` means that the linked entities must have at most max links
   *
   * @param field - field API object
   * @returns an object with the extracted validations
   */
export function fromFieldValidations(field: FieldAPI): ReferenceValidations {
  // eslint-disable-next-line -- TODO: describe this disable  @typescript-eslint/no-explicit-any
  const validations: Record<string, any>[] = [
    ...field.validations,
    ...(field.items?.validations ?? []),
  ]
  const linkContentTypeValidations = validations.find(
    (v) => 'linkContentType' in v
  )
  const linkMimetypeGroupValidations = validations.find(
    (v) => 'linkMimetypeGroup' in v
  )
  const sizeValidations = validations.find((v) => 'size' in v)
  const size = (sizeValidations && sizeValidations.size) || {}
  const min = size.min
  const max = size.max

  let numberOfLinks: NumberOfLinksValidation | undefined = undefined

  if (isNumber(min) && isNumber(max)) {
    numberOfLinks = {
      type: 'min-max',
      min,
      max,
    }
  } else if (isNumber(min)) {
    numberOfLinks = {
      type: 'min',
      min,
      max: undefined,
    }
  } else if (isNumber(max)) {
    numberOfLinks = {
      type: 'max',
      max,
      min: undefined,
    }
  }

  const result: ReferenceValidations = {
    contentTypes: linkContentTypeValidations?.linkContentType ?? undefined,
    mimetypeGroups:
      linkMimetypeGroupValidations?.linkMimetypeGroup ?? undefined,
    numberOfLinks,
    // todo: there are multiple BE problems that need to be solved first, for now we don't want to apply size constraints
    // linkedFileSize: findValidation(field, 'assetFileSize', {}),
    // linkedImageDimensions: findValidation(field, 'assetImageDimensions', {})
  }

  return result
}
