{"cardV3": [{"templateId": "componentCardAuthor", "templateName": "Card / Author", "templateFields": ["altusHeadshot", "altusFirstName", "altusLastName", "altusBio", "altus<PERSON><PERSON><PERSON>", "useExistingData"]}, {"templateId": "componentCardEvent", "templateName": "Card / Event", "templateFields": ["altusEventName", "altusEventDescription", "altusEventDuration"]}], "componentHero": [{"templateId": "componentCutoutHero", "templateName": "Hero / Cutout", "templateFields": ["altusHeading", "altusExcerpt", "altusCutoutImage"]}, {"templateId": "componentSingleColumnHero", "templateName": "Hero / Single Column", "templateFields": ["altusHeading", "altusExcerpt", "altusCTABtn"]}]}