import * as React from 'react'

import { FieldConnector } from '@contentful/field-editor-shared'
import deepEqual from 'deep-equal'

import { EntityProvider } from '../common/EntityStore'
import { ReferenceEditorProps } from '../common/ReferenceEditor'
import { CombinedLinkEntityActions } from '../components/LinkActions/LinkEntityActions'
import { ResourceLink } from '../types'
import { EntryRoute } from './Cards/ContentfulEntryCard'
import { ResourceCard } from './Cards/ResourceCard'
import { useResourceLinkActions } from './useResourceLinkActions'

/**
 * SingleResourceReferenceEditor is a React component that is used to edit a single reference field,
 * restricted to reference resources.
 *
 * The component will render a single reference input field if the value is empty or null.
 * Otherwise, it will render a card for the referenced entity.
 *
 * The component will also render a custom card for the referenced entity,
 * if the renderCustomCard prop is provided.
 *
 * The component will also render custom actions for the referenced entity,
 * if the renderCustomActions prop is provided.
 *
 * @param props - The props for the SingleResourceReferenceEditor component.
 * @returns - A React component that renders the reference input field or the custom card
 * for the referenced entity.
 */
export function SingleResourceReferenceEditor(
  props: ReferenceEditorProps & {
    getEntryRouteHref: (entryRoute: EntryRoute) => string
    apiUrl: string
  }
) {
  const { dialogs, field } = props.sdk
  const linkActionsProps = useResourceLinkActions({
    dialogs,
    field,
    apiUrl: props.apiUrl,
  })

  return (
    <EntityProvider sdk={props.sdk}>
      <FieldConnector<ResourceLink>
        throttle={0}
        field={props.sdk.field}
        isInitiallyDisabled={props.isInitiallyDisabled}
        isEqualValues={deepEqual}
      >
        {({ value, disabled }) => {
          return value ? (
            <ResourceCard
              onRemove={() => props.sdk.field.removeValue()}
              resourceLink={value}
              isDisabled={disabled}
              getEntryRouteHref={props.getEntryRouteHref}
            />
          ) : (
            <CombinedLinkEntityActions
              {...linkActionsProps}
              renderCustomActions={props.renderCustomActions}
            />
          )
        }}
      </FieldConnector>
    </EntityProvider>
  )
}
