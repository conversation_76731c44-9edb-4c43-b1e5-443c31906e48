import { useEffect, useMemo, useState } from 'react'

import { EditorAppSDK } from '@contentful/app-sdk'
import { ContentEntityType, ContentType } from '../types'
import { ReferenceValidations } from '../utils/fromFieldValidations'
import { ReferenceEditorProps } from './ReferenceEditor'
import { useAccessApi } from './useAccessApi'

type ContentTypePermissionsProps = {
  sdk: EditorAppSDK
  entityType: ContentEntityType
  parameters: ReferenceEditorProps['parameters']
  allContentTypes: ContentType[]
  validations: ReferenceValidations
}

type ContentTypePermissions = {
  creatableContentTypes: ContentType[]
  readableContentTypes: ContentType[]
  availableContentTypes: ContentType[]
}

async function filter<T, S extends T>(
  arr: T[],
  predicate: (value: T) => Promise<boolean>
) {
  // intentionally parallel as we assume it's cached in the implementation of the access api
  const fail = Symbol()
  const results = await Promise.all(
    arr.map(async (item) => ((await predicate(item)) ? item : fail))
  )

  return results.filter((x) => x !== fail) as S[]
}

  /**
   * Determines the content types that can be created, read and are available
   * in the current environment.
   *
   * @param props - The properties for determining the available content types.
   * @param props.sdk - The EditorAppSDK instance for interacting with Contentful.
   * @param props.entityType - The type of the entity, either 'Entry' or 'Asset'.
   * @param props.parameters - The parameters for the current reference field.
   * @param props.allContentTypes - All the content types available in the space.
   * @param props.validations - The field validations for the current reference field.
   *
   * @returns An object with the following properties:
   * - `creatableContentTypes`: An array of content types that can be created.
   * - `readableContentTypes`: An array of content types that can be read.
   * - `availableContentTypes`: An array of content types that are available in the space.
   */
export function useContentTypePermissions(
  props: ContentTypePermissionsProps
): ContentTypePermissions {
  const availableContentTypes = useMemo(() => {
    if (props.entityType === 'Asset') {
      return []
    }

    if (props.validations.contentTypes) {
      return props.allContentTypes.filter((ct) =>
        props.validations.contentTypes?.includes(ct.sys.id)
      )
    }

    return props.allContentTypes
  }, [props.allContentTypes, props.validations.contentTypes, props.entityType])
  const [creatableContentTypes, setCreatableContentTypes] = useState(
    availableContentTypes
  )
  const [readableContentTypes, setReadableContentTypes] = useState(
    availableContentTypes
  )
  const { canPerformActionOnEntryOfType } = useAccessApi(props.sdk.access)

  useEffect(() => {
    function getContentTypes(action: 'create' | 'read') {
      return filter(availableContentTypes, (ct) =>
        canPerformActionOnEntryOfType(action, ct.sys.id)
      )
    }

    async function checkContentTypeAccess() {
      const creatable = await getContentTypes('create')
      const readable = await getContentTypes('read')
      setCreatableContentTypes(creatable)
      setReadableContentTypes(readable)
    }

    void checkContentTypeAccess()
    // eslint-disable-next-line react-hooks/exhaustive-deps -- TODO: Evaluate the dependencies
  }, [availableContentTypes])

  return {
    creatableContentTypes,
    readableContentTypes,
    availableContentTypes,
  }
}
