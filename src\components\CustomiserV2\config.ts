export const customiserConfig = [

  {
    category: 'Color',
    styles  : [
      { label: 'Primary 1', class: 'cp1' },
      { label: 'Primary 2', class: 'cp2' },
      { label: 'Primary 3', class: 'cp3' },
      { label: 'Primary 4', class: 'cp4' },
      { label: 'Primary 5', class: 'cp5' },
      { label: 'Secondary 1', class: 'cs1' },
      { label: 'Secondary 2', class: 'cs2' },
      { label: 'Secondary 3', class: 'cs3' },
      { label: 'Neutral 1', class: 'cn1' },
      { label: 'Neutral 2', class: 'cn2' },
      { label: 'Neutral 3', class: 'cn3' },
      { label: 'Neutral 4', class: 'cn4' },
      { label: 'Neutral 5', class: 'cn5' },
      { label: 'Neutral 6', class: 'cn6' },
      { label: 'Neutral 7', class: 'cn7' },
      { label: 'Neutral 8', class: 'cn8' },
    ],
  },
  {
    category: 'Highlight',
    styles  : [
      { label: 'Highlight none', class: 'bnone' },
      { label: 'Transparent', class: 'bgTransparent' },
      { label: 'Primary 1', class: 'bp1' },
      { label: 'Primary 2', class: 'bp2' },
      { label: 'Secondary 1', class: 'bs1' },
      { label: 'Secondary 2', class: 'bs2' },
      { label: 'Secondary 3', class: 'bs3' },
      { label: 'Neutral 1', class: 'bn1' },
      { label: 'Neutral 2', class: 'bn2' },
      { label: 'Neutral 3', class: 'bn3' },
      { label: 'Neutral 4', class: 'bn4' },
      { label: 'Neutral 5', class: 'bn5' },
      { label: 'Neutral 6', class: 'bn6' },
    ],
  },
  {
    category: 'Family',
    styles  : [
      { FName: 'Cambon Bold', class: 'fSerif', label: 'Serif', type: 'serif' },
      { FName: 'Aeonik Bold', class: 'fSansBld', label: 'Sans serif bold', type: 'sans-serif'},
      { FName: 'Aeonik Regular', class: 'fSansReg', label: 'Sans serif regular', type: 'sans-serif'},
      { FName: 'Aeonik Medium',class: 'fSansMed', label: 'Sans serif medium', type: 'sans-serif' },
      { FName: 'Sans serif', class: 'fSans', label: 'Sans serif', type: 'sans-serif' },
    ],
  },
  {
    category: 'Size',
    styles  : [
      { label: 'Font minimum', class: 'fs1', FName: '12px'  },
      { label: 'Font small', class: 'fs2', FName: '14px' },
      { label: 'Font medium', class: 'fs3', FName: '16px' },
      { label: 'Font large', class: 'fs4', FName: '18px' },
      { label: 'Font extra large', class: 'fs5', FName: '22px' },
      { label: 'Font maximum', class: 'fs6', FName: '24px'  },
    ],
  },
  // {
  //   category: 'Background Color',
  //   styles: [
  //     { label: 'Background none', class: 'bgNone' },
  //     { label: 'Transparent', class: 'bgTransparent' },
  //     { label: 'Primary 1', class: 'bp1' },
  //     { label: 'Primary 2', class: 'bp2' },
  //     { label: 'Secondary 1', class: 'bs1' },
  //     { label: 'Secondary 2', class: 'bs2' },
  //     { label: 'Secondary 3', class: 'bs3' },
  //     { label: 'Secondary 4', class: 'bs4' },
  //     { label: 'Secondary 5', class: 'bs5' },
  //     { label: 'Neutral 1', class: 'bn1' },
  //     { label: 'Neutral 2', class: 'bn2' },
  //     { label: 'Neutral 3', class: 'bn3' },
  //     { label: 'Neutral 4', class: 'bn4' },
  //     { label: 'Neutral 5', class: 'bn5' },
  //     { label: 'Neutral 6', class: 'bn6' },
  //     { label: 'Neutral 7', class: 'bn7' },
  //   ],
  // },
  // {
  //     category: 'Gradients',
  //     styles: [
  //         { label: 'Gradient s1 s3 diagonal', class: 'bs1s3d' },
  //         { label: 'Gradient s1 s3 linear', class: 'bs1s3h' },
  //         { label: 'Gradient o1 s2 diagonal', class: 'bgo1n1d' },
  //         { label: 'Text gradient s1 s3 diagonal', class: 'cs1s3d' },
  //         { label: 'Text gradient s1 s3 linear', class: 'cs1s3h' },
  //     ],
  // },
  // {
  //   category: 'Borders',
  //   styles: [
  //     { label: 'Border', class: 'bdr solid' },
  //     { label: 'Border thick', class: 'bdr thick' },
  //     { label: 'Border dashed', class: 'bdr dashed' },
  //     { label: 'Border dashed', class: 'bdr transparent' },
  //     { label: 'Border translucent', class: 'bdr translucent' },
  //   ],
  // },
  // {
  //   category: 'Rounded',
  //   styles: [
  //     { label: 'Rounded min', class: 'rounded min' },
  //     { label: 'Rounded small', class: 'rounded s' },
  //     { label: 'Rounded medium', class: 'rounded m' },
  //     { label: 'Rounded large', class: 'rounded l' },
  //     { label: 'Rounded extra large', class: 'rounded xl' },
  //     { label: 'Rounded max', class: 'rounded max' },
  //   ],
  // },
  {
    category: 'Heading type',
    styles  : [
      { label: 'Heading 6', class: 'h6', FName: 'h6', size:"fs4" },
      { label: 'Heading 5', class: 'h5', FName: 'h5', size:"fs5" },
      { label: 'Heading 4', class: 'h4', FName: 'h4', size:"fs6" },
      { label: 'Heading 3', class: 'h3', FName: 'h3', size:"32px" },
      { label: 'Heading 2', class: 'h2', FName: 'h2', size:"42px" },
      { label: 'Heading 1', class: 'h1', FName: 'h1', size:"64px" },
      { label: 'Subheading', class: 'subheading', FName: 'Subheading', size:"fs4" },
    ],
  },
  // {
  //   category: 'Shadow',
  //   styles: [
  //     { label: 'Shadow 1', class: 'shadow shadow1' },
  //     { label: 'Shadow 2', class: 'shadow shadow2' },
  //     { label: 'Shadow 3', class: 'shadow shadow3' },
  //   ],
  // },
  // {
  //   category: 'Others',
  //   styles: [
  //     { label: 'Visibility hidden', class: 'hidden' },
  //     { label: 'Display none', class: 'dNone' },
  //   ],
  // },
]