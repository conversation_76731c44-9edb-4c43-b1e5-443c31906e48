import {
  createContentEntry,
  getEntryDataById,
  updateEntryData,
} from '../../../../globals/utils'

export const UpdateCurrentConfiguration = async (
  contentId: string,
  data: any,
  doPublish?: boolean
) => {
  if (contentId) {
    const response = await getEntryDataById(contentId).then((res: any) => {
      return res.fields
    })

    let updatedPayload = {
      ...response,
      data: {
        'en-CA': {
          content: [
            {
              content: [
                {
                  data: {},
                  marks: [],
                  nodeType: 'text',
                  value: JSON.stringify(data),
                },
              ],
              data: {},
              nodeType: 'paragraph',
            },
          ],
          data: {},
          nodeType: 'document',
        },
      },
    }

    const res = await updateEntryData(contentId, updatedPayload, doPublish)
    return res
  }
}

export const CreateNewConfiguration = async (
  data: any,
  type: string,
  internalName: string,
  doPublish?: boolean
) => {
  const fields = {
    internalName: {
      'en-CA': internalName,
    },
    type: {
      'en-CA': type,
    },
    data: {
      'en-CA': {
        content: [
          {
            content: [
              {
                data: {},
                marks: [],
                nodeType: 'text',
                value: JSON.stringify(data),
              },
            ],
            data: {},
            nodeType: 'paragraph',
          },
        ],
        data: {},
        nodeType: 'document',
      },
    },
  }

  const res: any = await createContentEntry(
    'configurations',
    fields,
    null,
    doPublish
  )
  const newContentId = res?.sys?.id || ''

  return newContentId
}
