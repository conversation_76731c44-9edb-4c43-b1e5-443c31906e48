import { EditorAppSDK } from '@contentful/app-sdk'
import {
  AssetCard,
  AssetStatus,
  Menu,
  MenuItem,
} from '@contentful/f36-components'
import { Asset } from 'contentful-management'
import React, { useEffect, useState } from 'react'
import { getAssetInfo } from '../../utils'

interface AssetInfoI {
  sdk: EditorAppSDK
  asset: Asset | undefined
  onFileUpdate: (asset: Asset | undefined) => void
  currentLocale: string
}

interface cardInfoI {
  status: AssetStatus
  title: string
  contentType: any
}

const CustomAsset = (props: AssetInfoI) => {
  const { asset, currentLocale, sdk, onFileUpdate } = props

  const [AssetInfo, setAssetInfo] = useState<cardInfoI>()

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchAsset = async () => {
      try {
        const assetData = await getAssetInfo(asset, currentLocale)
        setAssetInfo(assetData)
      } catch (error) {
        console.error('Error fetching asset:', error)
      }
    }
    fetchAsset()
  }, [])

  const handleMenuClick = async (status: string) => {
    setIsLoading(true)
    if (status === 'edit' && asset) {
      try {
        const result = await sdk.navigator.openAsset(asset.sys.id, {
          slideIn: { waitForClose: true },
        })

        if (result) {
          const updatedEntry = result.entity
          const assetInfo = await getAssetInfo(updatedEntry, currentLocale)
          setAssetInfo(assetInfo)
          onFileUpdate(updatedEntry as Asset)
        }
        setIsLoading(false)
      } catch (error) {
        setAssetInfo((pre) => {
          return {
            ...pre,
            status: 'deleted',
          }
        })
        onFileUpdate(undefined)
        console.error('Error opening slide-in panel:', error)
      }
    } else if (status === 'delete') {
      setAssetInfo((pre) => {
        return {
          ...pre,
          status: 'deleted',
        }
      })
      onFileUpdate(undefined)
    }
  }
  return (
    <Menu>
      {' '}
      <AssetCard
        status={AssetInfo?.status}
        title={AssetInfo?.title}
        type={AssetInfo?.contentType}
        size='small'
        actions={[
          <MenuItem key='edit' onClick={() => handleMenuClick('edit')}>
            Edit
          </MenuItem>,
          <MenuItem key='remove' onClick={() => handleMenuClick('delete')}>
            Remove
          </MenuItem>,
        ]}
        onClick={() => handleMenuClick('edit')}
        isLoading={isLoading}
      />
    </Menu>
  )
}
export default CustomAsset
