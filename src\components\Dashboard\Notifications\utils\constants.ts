export const NotificationType = [
  {
    title: 'Only Browser Notification',
    value: 'browser',
  },
  {
    title: 'Only Page Notification',
    value: 'page',
  },
  {
    title: 'Page & Browser Notification',
    value: 'both',
  },
]

export const TypeOfNotifications = [
  {
    title: 'Real Time Notification',
    value: 3,
  },
  {
    title: 'Sticky Notification',
    value: 1,
  },
]

export const NotificationDuration = [
  {
    title: '5 Seconds',
    value: '5',
  },
  {
    title: '10 Seconds',
    value: '10',
  },
  {
    title: '15 Seconds',
    value: '15',
  },
  {
    title: '30 Seconds',
    value: '30',
  },
  {
    title: 'Persistent',
    value: '0',
  },
]

export const StickyNotificationTemplates = [
  {
    title: 'Small',
    value: 'Small',
  },
  {
    title: 'Medium',
    value: 'Medium',
  },
  {
    title: 'Carousel',
    value: 'Carousel',
  },
]

export const BgColors = [
  {
    title: 'Primary',
    value: 'Primary',
  },
  {
    title: 'Secondary',
    value: 'Secondary',
  },
]

export const CTATargets = [
  {
    title: 'Open in Same Window',
    value: '_self',
  },
  {
    title: 'Open in New Window',
    value: '_blank',
  },
]

export const PROD_URLS = {
  agl: 'https://www.altusgroup.com/',
  ver: 'https://www.verifino.com/',
  fia: 'https://financeactive.com/',
  reo: 'https://www.reonomy.com/',
  o11: 'https://www.one11advisors.com/',
  for: 'https://www.forbury.com/',
  adx: 'https://ag.studio/',
}

export const MSA_URLS = {
  agl: 'https://msa-agl-v3w-git-main-altus.vercel.app/',
  ver: 'https://msa-ver-v3w-git-main-altus.vercel.app/',
  fia: 'https://msa-fia-v3w-git-main-altus.vercel.app/',
  reo: 'https://msa-reo-v3w-git-main-altus.vercel.app/',
  o11: 'https://msa-o11-v3w-git-main-altus.vercel.app/',
  for: 'https://msa-for-v3w-git-main-altus.vercel.app/',
  adx: 'https://msa-adx-v3w-git-main-altus.vercel.app/',
}

export const AllowedNotificationType = [
  {
    id: 0,
    title: 'General',
    value: 'general',
  },
  {
    id: 1,
    title: 'Insights',
    value: 'insights',
  },
  {
    id: 2,
    title: 'Press releases',
    value: 'press-releases',
  },
  {
    id: 3,
    title: 'Webinars',
    value: 'webinars',
  },
  {
    id: 4,
    title: 'Events',
    value: 'events',
  },
  {
    id: 5,
    title: 'Research',
    value: 'research',
  },
  {
    id: 6,
    title: 'Training & Education',
    value: 'training-education',
  },
]