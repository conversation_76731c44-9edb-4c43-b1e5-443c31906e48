import { ConnectedRichTextEditor } from '@contentful/field-editor-rich-text'
import { FieldAppSDK } from '@contentful/app-sdk'
import React, { useState } from 'react'


interface AppRichTextEditorI {
  sdk: FieldAppSDK
  fieldId: string,
  setValues: (newValue: any) => {},
  values: any
}
const AppRichTextEditor = (props: AppRichTextEditorI) => {

  const { sdk, fieldId, setValues, values } = props
  const [editorsValue, setEditorsValue] = useState('')
  return (
    <ConnectedRichTextEditor {...props}
      value={values['useExistingData'] === true ? values[fieldId] : editorsValue}
      onChange={
        (doc) => {
          setEditorsValue(doc)
          setValues(prev => {
            const newValue = doc

            const updatedVal = { ...prev, [fieldId]: newValue }
            sdk.field.setValue(updatedVal)
            return updatedVal
          })

        }
      } />
  )
}

export default AppRichTextEditor