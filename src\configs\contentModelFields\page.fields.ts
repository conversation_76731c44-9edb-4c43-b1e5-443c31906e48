// pageCommonFields are fields that are common across all page templates.
const pageCommonFields = [
  // 'slug',
  // 'title',
  // 'pageThumbnail',
  // 'shortTitle',
  // 'seoTitle',
  // 'seoDescription',
  // 'seoKeywords',
  // 'isHeaderNavigationHidden',
  // 'isFooterNavigationHidden',
  // 'internalName',
  // 'template',
  '',
]

export const pageFields = {
  Generic: [
    ...pageCommonFields,
    'content',
    // 'noIndex',
    // 'noFollow',
    // 'hideFromAlgolia',
    // 'isTranslucent',
    // 'canonicalUrl',
    // 'publishDate',
  ],
  'Insight Article': [
    ...pageCommonFields,
    'hero',
    // 'document',
    'menuList',
    'introSection',
    'content',
    'primaryCta',
    'endContent',
    'dts',
    // 'aboutUs',
    // 'publishDate',
    // 'authorsHeading',
    // 'authors',
    // 'noIndex',
    // 'noFollow',
    // 'hideFromAlgolia',
    // 'isTranslucent',
    // 'canonicalUrl',
  ],
  'Press Release': [
    ...pageCommonFields,
    'hero',
    'document',
    'introSection',
    'content',
    // 'endContent',
    'aboutUs',
    'dts',
    // 'publishDate',
    // 'authorsHeading',
    // 'authors',
    // 'noIndex',
    // 'noFollow',
    // 'hideFromAlgolia',
    // 'isTranslucent',
    // 'canonicalUrl',
  ],
  Home: [
    ...pageCommonFields,
    'content',
    // 'noIndex',
    // 'noFollow',
    // 'hideFromAlgolia',
    // 'publishDate',
    // 'isTranslucent',
    // 'canonicalUrl',
  ],
  Object: [...pageCommonFields],
}
