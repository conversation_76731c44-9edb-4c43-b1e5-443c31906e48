import { useCurrentEditor } from '@tiptap/react'
import _ from 'lodash'
import React, { useEffect, useRef, useState } from 'react'
import ReactDOMServer from 'react-dom/server'
import { Tooltip as ReactTooltip } from 'react-tooltip'
import ColorsInputV2 from '../ColorsInputV2'
import GenericIcon from '../Icons/SysIcon'
import TheAnimatedNode from '../TheAnimatedNode'
import { getSelectedText } from '../Tiptap/utils'
import WarningPopup from './@core/WarningPopup'
import { customiserConfig } from './config'
import {
  findCodeNameByHex,
  KeyStringMapper,
  reorderSummaryObject,
} from './helper'
import styles from './index.module.scss'

interface CustomiserV2Props {
  isButtonVisible?: boolean
  selectedText?: string
  handleClose: () => void
  fieldId: string
  isCustomiserOpen: boolean
}

function CustomiserV2(props: CustomiserV2Props) {
  const { editor } = useCurrentEditor()
  const customiserRef = useRef(null)

  const [activeTab, setActiveTab] = useState(0)

  const [selectedValueToCopy, setSelectedValueToCopy] = useState('')

  const [showWarningModal, setShowWarningModal] = useState(false)

  const [selectedStyles, setSelectedStyles] = useState<{
    [key: string]: string
  }>({})

  // on modal close, reset state
  const handleModalClose = () => {
    setActiveTab(0)
    setSelectedStyles({})
    setShowWarningModal(false)
    props.handleClose()
  }

  const handleClickOutside = (event) => {
    if (
      customiserRef.current &&
      !customiserRef.current?.contains(event.target)
    ) {
      props.handleClose()
    }
  }

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // func to get active category
  const getActiveCategory = () => customiserConfig[activeTab].category

  // func to check if styles are selected
  const checkIsStylesSelected = () => Object.keys(selectedStyles).length > 0

  // func to handle copy to clipboard and set selected value
  function copyToClipboard(value: string | undefined) {
    if (!value) {
      return
    }
    navigator.clipboard
      .writeText(value)
      .then(() => {
        setSelectedValueToCopy(value)
      })
      .catch((err) => {})
  }

  // func to get selected style category from category list
  const getSelectedStyleCategoryFromCategoryList = () => {
    const x = customiserConfig[activeTab].styles
    const cat = customiserConfig[activeTab].category

    let filteredStyleObj: any =
      x.find((style) => style.class === selectedStyles[cat]) || {}

    if (cat === 'Heading type') {
      const order = ['class', 'label', 'size']
      filteredStyleObj = _.pick(filteredStyleObj, order)
    }

    return (
      <div className={styles.customiserFooterInner}>
        {Object.keys(filteredStyleObj || {}).map((key) => {
          if (key === 'label' || (cat === 'Heading type' && key === 'FName'))
            return null

          const value = filteredStyleObj[key as keyof typeof filteredStyleObj]

          return (
            <>
              <ReactTooltip
                id='copy-tooltip'
                style={{
                  backgroundColor: '#000',
                  zIndex: '3',
                }}
              />
              <p
                data-tooltip-id={'copy-tooltip'}
                data-tooltip-content={`Copy ${KeyStringMapper(key, cat)}`}
                className={`${styles.tags} ${
                  selectedValueToCopy === value ? styles.active : ''
                }`}
                onClick={() => copyToClipboard(value)}
              >
                {value}
              </p>
            </>
          )
        })}
        {getActiveCategory() === 'Heading type' && (
          <div className={styles.headingTextRoot}>
            <GenericIcon icon={'AiOutlineInfoCircle'} />
            <p className={styles.headingText}>
              Heading type overrides Family and Size.
            </p>
          </div>
        )}
        {(getActiveCategory() === 'Family' ||
          getActiveCategory() === 'Size') && (
          <div className={styles.headingTextRoot}>
            <GenericIcon icon={'AiOutlineInfoCircle'} />
            <p className={styles.headingText}>
              {getActiveCategory()} overrides Heading type.
            </p>
          </div>
        )}
      </div>
    )
  }

  // func to handle style change and update local state
  const handleStyleChange = (category: string, item: any) => {
    let styles = { ...selectedStyles }

    styles = {
      ...styles,
      [category]: item.class || item.hex,
    }

    if (category === 'Heading type') {
      // Heading type overrides Family and Size so delete them on heading type select
      delete styles.Family
      delete styles.Size
    }

    if (category === 'Family' || category === 'Size')
      // Family and Size overrides Heading type so delete it on Family or Size select
      delete styles?.['Heading type']

    setSelectedStyles(styles)
  }

  //  func to handle remove style
  const handleRemoveStyle = (category: string) => {
    setSelectedStyles((prev) => {
      const updatedSelectedStyles = { ...prev }
      delete updatedSelectedStyles[category]
      return updatedSelectedStyles
    })
  }

  // func to apply selected styles
  const handleApply = () => {
    const styleClasses = Object.values(selectedStyles).join(' ')

    if (selectedStyles?.['Highlight'])
      editor?.commands.setHighlight({ color: selectedStyles?.['Highlight'] })
    else editor?.commands.unsetHighlight()

    if (selectedStyles?.['Color'])
      editor?.commands.setColor(selectedStyles?.['Color'])
    else editor?.commands.setColor('black')

    if (Object.keys(selectedStyles).length !== 0)
      editor?.chain().focus().setAltusText({ class: styleClasses }).run()
    else editor?.chain().focus().unsetAltusText().run()

    if (getActiveCategory() === 'Bg Color') {
      if (styleClasses.length > 0)
        editor?.chain().focus().setAltusDiv({ class: styleClasses }).run()
      else editor?.chain().focus().unsetAltusDiv().run()
    }

    setShowWarningModal(false)

    handleModalClose()
  }

  // func to handle revert selected style and copy select value state
  const handleRevert = () => {
    setSelectedStyles({})
    setSelectedValueToCopy('')
  }

  const TheButton = props.isButtonVisible ? <button>Customiser</button> : null

  const Tabs = customiserConfig.map((tab, index) => {
    return (
      <div
        onClick={() => setActiveTab(index)}
        className={`${styles.tabs} ${
          activeTab === index ? styles.active : styles.tabs
        }`}
      >
        <p>{tab.category}</p>
      </div>
    )
  })

  // Summary of selected styles by category
  const TheSummary = checkIsStylesSelected() ? (
    <div>
      <p className='fs3 fSansBld'>Summary</p>
      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
        <tbody>
          {Object.keys(reorderSummaryObject(selectedStyles)).map((key) => {
            let value = selectedStyles[key]
            if (key === 'Color' || key === 'Highlight')
              value = findCodeNameByHex(value) || ''

            if (!value) return null

            return (
              <tr key={String(key)}>
                {' '}
                {/* Explicitly cast key to string */}
                <td
                  style={{
                    padding: '4px 0',
                    width: '100px',
                    verticalAlign: 'top',
                  }}
                >
                  {key}
                </td>
                <td
                  style={{
                    padding: '4px 0',
                    verticalAlign: 'top',
                    textAlign: 'left',
                    fontWeight: 'bold',
                  }}
                >
                  {typeof value === 'object' ? JSON.stringify(value) : value}
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  ) : (
    <div>
      <p>The summary is only available when you have made changes.</p>
    </div>
  )

  const TheSummaryHtml = ReactDOMServer.renderToStaticMarkup(TheSummary)

  // Text Previews by category and based on previously selected styles
  const TheTextPreviews = customiserConfig[activeTab].styles.map((style) => {
    const category = customiserConfig[activeTab].category

    const isStyleSelected = selectedStyles[category] === style.class

    let styleTag = ''

    if (category === 'Size') {
      styleTag = selectedStyles['Family']
    }

    if (category === 'Family') {
      styleTag = selectedStyles['Size']
    }

    return (
      <>
        <div className={`${styles.textPreview} ${style.class} ${styleTag}`}>
          <span
            className={`${styles.text}  ${
              isStyleSelected ? styles.active : null
            }`}
            onClick={() => {
              isStyleSelected
                ? handleRemoveStyle(category)
                : handleStyleChange(category, style)
            }}
          >
            <span
              className={styles.textEllipsisText}
              style={{
                color: selectedStyles['Color'],
                backgroundColor: selectedStyles['Highlight'],
              }}
            >
              {getSelectedText(editor) ?? 'Please Select Text'}
            </span>

            <span className={`${styles.tag}`}>{style?.label}</span>
          </span>
        </div>
      </>
    )
  })

  const TheMain = ['Highlight', 'Color'].includes(
    customiserConfig[activeTab].category
  ) ? (
    <ColorsInputV2
      onChange={(color) => {
        handleStyleChange(customiserConfig[activeTab].category, color)
      }}
      onClose={() => {}}
      pickerType='Standalone'
      disabled={{
        Gradients: true,
      }}
      activeColor={
        selectedStyles[customiserConfig[activeTab].category]
          ? selectedStyles[customiserConfig[activeTab].category]
          : null
      }
      isHeaderHidden={true}
      isApplyButtonHidden={true}
      onCopyValue={(x) => setSelectedValueToCopy(x)}
      copiedSpanValue={selectedValueToCopy}
    />
  ) : (
    <div className={styles.customiserFooter}>
      {TheTextPreviews} {getSelectedStyleCategoryFromCategoryList()}
    </div>
  )

  // fetch the previously applied styles to selected text.
  useEffect(() => {
    const textAttr = editor?.getAttributes('altusText')?.class?.split(' ') || []
    const textStyleAttr = editor?.getAttributes('textStyle')

    const textHighlightAttr = editor?.getAttributes('highlight')

    let obj = textAttr.reduce((acc, element) => {
      const matchedStyle = customiserConfig.find((styleElement) =>
        styleElement.styles.some((style) => style.class === element)
      )

      if (matchedStyle) {
        acc[matchedStyle.category] = element
      }

      return acc
    }, {})

    if (textStyleAttr?.color && textStyleAttr.color !== 'black') {
      obj = {
        ...obj,
        Color: textStyleAttr.color,
      }
    }

    if (textHighlightAttr?.color) {
      obj = {
        ...obj,
        Highlight: textHighlightAttr.color,
      }
    }

    setSelectedStyles(obj || {})

    return () => {
      // handleModalClose()
    }
  }, [props.isCustomiserOpen])
  // Function to determine the animation direction
  const getTabTransition = (prevTab: number, currentTab: number) => {
    if (prevTab < currentTab) {
      // Forward transition (0 -> 1)
      return { initial: { x: 200 }, animate: { x: 0 }, exit: { x: -200 } }
    } else {
      // Backward transition (1 -> 0)
      return { initial: { x: -200 }, animate: { x: 0 }, exit: { x: 200 } }
    }
  }

  const [prevTab, setPrevTab] = React.useState(activeTab)

  useEffect(() => {
    setPrevTab(activeTab)
  }, [activeTab])

  const { initial, animate, exit } = getTabTransition(prevTab, activeTab)
  const variant = {
    closed: { scale: 0.8, display: 'none' },
    open: { scale: 1, display: 'block' },
  }
  return (
    <>
      {TheButton}
      <TheAnimatedNode
        as='div'
        animatedProps={{
          initial: { display: 'none', scale: 0.8 }, // Initial state (closed)
          animate: props?.isCustomiserOpen ? 'open' : 'closed',
          variants: variant,
          transition: { duration: 0.5, ease: [0.68, -0.55, 0.27, 1.55] },
        }}
        htmlAttr={{ className: styles.customiserRoot, ref: customiserRef }}
      >
        <WarningPopup
          showWarningModal={showWarningModal}
          onConfirm={handleApply}
          onReject={handleModalClose}
          onClose={() => setShowWarningModal(false)}
          data={selectedStyles}
          selectedText={getSelectedText(editor) || ''}
        />

        <div className={styles.header}>
          <p className={styles.textEllipsis}>
            Styling <span>{getSelectedText(editor)}</span>
          </p>
          <div
            onClick={() =>
              checkIsStylesSelected()
                ? setShowWarningModal(true)
                : handleModalClose()
            }
          >
            <GenericIcon
              icon={'Close'}
              htmlAttr={{ className: styles.closeIcon }}
              size={'md'}
            />
          </div>
        </div>
        <div className={styles.tabsRoot}>{Tabs}</div>

        <TheAnimatedNode
          key={activeTab}
          as='div'
          animatedProps={{
            initial: initial,
            animate: animate,
            exit: exit,
            transition: { type: 'spring', stiffness: 300, damping: 30 },
          }}
          htmlAttr={{ className: styles.mainRoot }}
        >
          {TheMain}
        </TheAnimatedNode>
        <div className={styles.footer}>
          <p>Content / {customiserConfig[activeTab].category}</p>

          <div className={styles.footerInner}>
            <ReactTooltip
              id='summary-tooltip'
              style={{
                backgroundColor: '#000',
                zIndex: '6',
              }}
              className={'shadow3'}
              place='top'
            />
            <div
              data-tooltip-id='summary-tooltip'
              data-tooltip-html={TheSummaryHtml}
              data-tooltip-place='top'
            >
              <GenericIcon
                icon={'AiOutlineInfoCircle'}
                size={'md'}
                iconColour={'cn1'}
                htmlAttr={{
                  className: !checkIsStylesSelected()
                    ? styles.disabledState
                    : '',
                }}
              />
            </div>

            <ReactTooltip
              id='revert-tooltip'
              style={{
                backgroundColor: '#000',
                zIndex: '6',
              }}
              place='top'
            />
            <div
              data-tooltip-id='revert-tooltip'
              data-tooltip-content={
                !checkIsStylesSelected()
                  ? 'You have not made any changes'
                  : 'Revert current changes'
              }
              onClick={handleRevert}
            >
              <GenericIcon
                icon={'Reset'}
                iconColour={'cn1'}
                size={'md'}
                htmlAttr={{
                  className: !checkIsStylesSelected()
                    ? styles.disabledState
                    : '',
                }}
              />
            </div>

            <ReactTooltip
              id='apply-tooltip'
              style={{
                backgroundColor: '#000',
                zIndex: '6',
              }}
            />
            <div
              onClick={handleApply}
              data-tooltip-id='apply-tooltip'
              data-tooltip-content='Apply current changes'
            >
              <GenericIcon
                icon={'BsCheckLg'}
                bgColour={'ba3'}
                iconColour={'cn8'}
                size={'md'}
              />
            </div>
          </div>
        </div>
      </TheAnimatedNode>
    </>
  )
}

export default CustomiserV2
