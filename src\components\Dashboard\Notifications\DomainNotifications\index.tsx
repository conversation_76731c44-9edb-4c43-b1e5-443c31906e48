import { HomeAppSDK } from '@contentful/app-sdk'
import { Box } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { generateRandomId } from '../../../../globals/firebase/utils'
import { getDomainDataByDomainName } from '../../../../globals/utils'
import {
  fetchNotificationHistory,
  setIsAddNewNotification,
} from '../../../../redux/slices/dashboard/notificationsSlice'
import { useAppDispatch, useAppSelector } from '../../../../redux/store'
import ModalConfirm from '../../../ConfirmModal'
import PushNotificationSettings from '../components/PushNotificationSettings'
import '../style.scss'
import {
  CreateRealTimeNotification,
  createSingleAsset,
  CreateStickyNotification,
  entryPageSelector,
  MultiEntrySelector,
  nonHttpsHandler,
  SaveNotificationAsHistory,
  selectSingleAsset,
  SingleEntrySelector,
} from '../utils'
import LoadingState from '../utils/LoadingState'
import { DomainNotiState, RealTimeNotificationPayload, UpdateNotiStateFunction } from '../utils/notifications.interface'
import MSANotificationFooter from './components/MSANotificationFooter'
import MSANotificationHeader from './components/MSANotificationHeader'
import MSANotificationStep1 from './components/MSANotificationStep1'
import MSANotificationStep2 from './components/MSANotificationStep2'
import MSANotificationStep3 from './components/MSANotificationStep3'
import DomainNotificationHistoryTable from './DomainNotificationsHistoryTable'

function DomainNotifications({ domain, sdk }: { domain: string; sdk: HomeAppSDK }) {

  // Initial state
  const initialState: DomainNotiState = {
    status: '',
    activeStep: 0,
    isLoading: false,
    internalName: '',
    externalLink: '',
    showConfirmBox: false,
    titleSource: 'seoTitle',
    descriptionSource: 'seoDescription',
    stickyNotificationTemplate: '',
    typeOfNotification: '3',
    title: '',
    url: '',
    description: '',
    thumbnail: {
      url: '',
      status: '',
    },
    badge: {
      url: '',
      status: '',
    },
    selectedPage: null,
    specificPages: [] as any,
    carouselData: [] as any,
    formFloatingPage: null,
    categoriesPages: [] as any,
    isGloballyEnabled: false,
    selectedCTA: null,
    isEnabled: true,
    isClosable: false,
    bgColor: 'Primary',
    notificationType: 'both',
    dataFetching: false,
    notificationDuration: '30',
    ctaText: 'Read more',
    ctaTarget: '_self',
    notificationCategory: ['general'],
    isSystemNotification: false,
    attentionRequired: false,
    groupingCategory: '',
    renotify: false,
    isSystemNotificationDismissible: false,
  }

  const [state, setState] = useState(initialState)

  const [saveDisable, setSaveDisable] = useState(false)

  const dispatch = useAppDispatch()

  const { id, dataSource, isAddNewNotification } = useAppSelector(state => state.notificationData)

  useEffect(() => {
    dispatch(fetchNotificationHistory('MSA'))
  }, [])

  /**
   * Updates the `state` by merging the `updates` object with the current `state`.
   * This is a controlled way of updating the state, as it ensures that the state
   * is updated in a predictable and controlled manner.
   * @param updates - The updates to be applied to the state. This should be a
   * partial of the `initialState` object.
   */
  const updateState: UpdateNotiStateFunction<DomainNotiState> = (updates: Partial<typeof initialState>) => {
    setState((prevState) => ({
      ...prevState,
      ...updates,
    }))
  }

  const clearAllStates = () => setState(initialState)

  const handleClose = () => updateState({ showConfirmBox: false })

  /**
   * This function is called when the user confirms the creation of a real-time notification.
   * It closes the confirmation modal and then calls the `HandleRealtimeNotification` function,
   * which handles the actual creation of the real-time notification.
   */
  const handleRealTimeConfirm = async () => {
    handleClose()
    await HandleRealtimeNotification()
  }

/**
 * This function is called when the user confirms the creation of a sticky notification.
 * It first closes the confirmation modal and then calls the `HandleStickyNotification`
 * function, which handles the actual creation of the sticky notification.
 */

  const handleStickyConfirm = async () => {
    handleClose()
    await HandleStickyNotification()
  }


  /**
   * Handles file input changes for the thumbnail and badge assets.
   * This function is called whenever the user selects a new file for either
   * the thumbnail or badge asset. It updates the state with the new asset
   * by calling the `updateState` function.
   * @param event - The event object containing the newly selected file.
   * @param type - The type of asset being updated, either 'thumbnail' or 'badge'.
   */
  const handleFileChange = async (event: any, type: 'thumbnail' | 'badge') => {
    updateState({ dataFetching: true })
    const res = await createSingleAsset(event, sdk, type)
    updateState(res)
  }

  /**
   * Handles asset selection for the thumbnail and badge assets.
   * This function is called whenever the user clicks on the 'Select Asset' button
   * for either the thumbnail or badge asset. It updates the state with the new asset
   * by calling the `updateState` function.
   * @param type - The type of asset being updated, either 'thumbnail' or 'badge'.
   */
  const selectThumbnail = async (type: 'thumbnail' | 'badge') => {
    updateState({ dataFetching: true })
    const res = await selectSingleAsset(sdk, type)
    updateState(res)
  }


  /**
   * Handles page selection for sticky notifications.
   * This function is called whenever the user clicks on the 'Select Page' button
   * for sticky notifications. It updates the state with the new page
   * by calling the `updateState` function.
   */
  const pageSelector = async () => {
    updateState({ dataFetching: true })
    const res = await entryPageSelector(sdk)
    updateState(res)
  }


  /**
   * Removes a page from the categories pages array.
   * @param {string} _ - Unused parameter, passed by the UI component.
   * @param {string} id - The ID of the page to be removed.
   */
  const categoriesPageRemove = (_: string, id: string) => {
    updateState({
      categoriesPages: state?.categoriesPages?.filter(
        (page: any) => page?.sys?.id !== id,
      ),
    })
  }


/**
 * Removes a page from the specific pages array.
 * @param {string} _ - Unused parameter, passed by the UI component.
 * @param {string} id - The ID of the page to be removed.
 */

  const specificPageRemove = (_: string, id: string) => {
    updateState({
      specificPages: state?.specificPages?.filter(
        (page: any) => page?.sys?.id !== id,
      ),
    })
  }

  /**
   * Removes a page from the carousel data array.
   * @param {string} _ - Unused parameter, passed by the UI component.
   * @param {string} id - The ID of the page to be removed.
   */
  const carouselRemove = (_: string, id: string) => {
    updateState({
      carouselData: state?.carouselData?.filter(
        (page: any) => page?.sys?.id !== id,
      ),
    })
  }


  /**
   * Selects multiple pages from the Contentful UI for the specific pages array.
   * This function is called whenever the user clicks on the 'Select Pages' button
   * for specific pages. It opens the Contentful UI's multi-entry selector and allows
   * the user to select multiple pages. The selected pages are then added to the
   * specific pages array in the state.
   */
  const specificPageSelector = async () => {
    updateState({ dataFetching: true })

    const contentTypes = ['page']

    const selected: any = await MultiEntrySelector(contentTypes, sdk)

    if (!selected || selected?.length === 0) {
      updateState({
        dataFetching: false,
      })
      return
    }

    let newSelected = [...state.specificPages, ...selected]

    updateState({
      specificPages: newSelected,
      dataFetching: false,
    })
  }

/**
 * Selects multiple pages from the Contentful UI for the categories pages array.
 * This function is called whenever the user clicks on the 'Select Pages' button
 * for categories. It opens the Contentful UI's multi-entry selector and allows
 * the user to select multiple pages. The selected pages are then added to the
 * categories pages array in the state.
 */

  const categoriesPageSelector = async () => {
    updateState({ dataFetching: true })

    const contentTypes = ['page']

    const selected: any = await MultiEntrySelector(contentTypes, sdk)

    if (!selected || selected?.length === 0) {
      updateState({
        dataFetching: false,
      })
      return
    }

    let newSelected = [...state?.categoriesPages, ...selected]

    updateState({
      categoriesPages: newSelected,
      dataFetching: false,
    })
  }

  /**
   * Selects multiple carousel entries from the Contentful UI for the carousel data array.
   * This function is called whenever the user clicks on the 'Select Carousel' button.
   * It opens the Contentful UI's multi-entry selector and allows the user to select
   * multiple carousel entries. The selected carousel entries are then added to the
   * carousel data array in the state.
   */
  const carouselSelector = async () => {
    updateState({ dataFetching: true })

    const contentTypes = ['carouselComponent']

    const selected = await MultiEntrySelector(contentTypes, sdk)

    if (!selected || selected?.length === 0) {
      updateState({
        dataFetching: false,
      })
      return
    }

    let newSelected = [...state?.carouselData, ...selected]

    const internalName = newSelected?.[0]?.fields?.internalName?.['en-CA']

    updateState({
      internalName,
      carouselData: newSelected,
      dataFetching: false,
    })
  }

  /**
   * Selects a single linkComponent entry from the Contentful UI for the CTA.
   * This function is called whenever the user clicks on the 'Select CTA' button.
   * It opens the Contentful UI's single-entry selector and allows the user to select
   * a single linkComponent entry. The selected linkComponent entry is then updated
   * in the state.
   */
  const CTASelector = async () => {
    updateState({ dataFetching: true })

    const contentTypes = ['linkComponent']
    const selected = await SingleEntrySelector(contentTypes, sdk)

    if (!selected) {
      updateState({ dataFetching: false })
      return
    }

    const internalName = selected?.fields?.internalName?.['en-CA']

    updateState({
      internalName,
      selectedCTA: selected,
      dataFetching: false,
    })
  }

  /**
   * A function that checks if the first step of the MSANotification wizard
   * should be disabled based on the state.
   *
   * The first step should be disabled if:
   *  - The type of notification is not selected
   *  - The type of notification is 'Sticky' and the template is not selected
   * @returns {boolean} True if the first step should be disabled, false otherwise
   */
  const checkFirstStepDisable = () => {
    const { typeOfNotification, stickyNotificationTemplate } = state
    if (!typeOfNotification) return false
    return !(typeOfNotification === '1' && !stickyNotificationTemplate)
  }

  /**
   * A function that checks if the second step of the MSANotification wizard
   * should be disabled based on the state.
   *
   * The second step should be disabled if:
   *  - The type of notification is not selected
   *  - The type of notification is 'Sticky' and the template is not selected
   *  - The type of notification is 'Realtime' and the required fields are not filled
   *  - The type of notification is 'Sticky' and the template is 'Carousel' and the
   *    required fields are not filled
   *  - The type of notification is 'Sticky' and the template is not 'Carousel' and the
   *    required fields are not filled
   * @returns {boolean} True if the second step should be disabled, false otherwise
   */
  const checkSecondStepDisable = () => {
    const {
      title,
      description,
      internalName,
      typeOfNotification,
      selectedCTA,
      selectedPage,
      url,
      thumbnail,
      externalLink,
      stickyNotificationTemplate,
      carouselData,
    } = state

    if (!typeOfNotification) return false
    else if (typeOfNotification === '1' && !stickyNotificationTemplate) {
      return false
    }

    if (typeOfNotification === '3') {
      if (
        title &&
        description &&
        thumbnail.url &&
        (url || externalLink || selectedPage || selectedCTA)
      )
        return true
    }

    if (typeOfNotification === '1') {
      if (stickyNotificationTemplate === 'Carousel') {
        if (internalName && title && description && carouselData) return true
      } else {
        if (internalName && title && description && selectedCTA) return true
      }
    }

    return false
  }

  /**
   * A function that creates a real time notification based on the current state of the MSA notification wizard.
   * It checks if the required fields are filled and if the domain is valid before creating the notification.
   * If the notification creation is successful, it saves the notification as history and fetches the latest history.
   * After the process is complete, it clears all states and sets the add new notification flag to false.
   * @returns {Promise<void>} A promise that resolves when the process is complete.
   */
  const HandleRealtimeNotification = async () => {
    updateState({ isLoading: true })

    const {
      title,
      selectedPage,
      description,
      thumbnail,
      url,
      externalLink,
      notificationType,
      notificationDuration,
      ctaText,
      ctaTarget,
      notificationCategory,
      isSystemNotification,
      attentionRequired,
      badge,
      groupingCategory,
      renotify,
      isSystemNotificationDismissible,
    } = state

    let payload: RealTimeNotificationPayload = {
      title: title || selectedPage?.fields?.seoTitle?.['en-CA'],
      body: description || selectedPage?.fields?.seoDescription?.['en-CA'],
      icon: thumbnail?.url,
      url: url || externalLink || selectedPage?.fields?.slug?.['en-CA'],
      id: generateRandomId(),
      domain: getDomainDataByDomainName(domain)?.domainKey || '',
      type: notificationType,
      duration: notificationDuration,
      timeStamp: new Date(),
      ctaText,
      ctaTarget,
      category: JSON.stringify(notificationCategory),
      isSystemNotification: isSystemNotification.toString(),
      attentionRequired: attentionRequired.toString(),
      badge: badge?.url,
      tag: groupingCategory,
      renotify: renotify.toString(),
      isDismissible: isSystemNotificationDismissible.toString(),
    }

    // handler func to check if url is https or http or empty string and add https://<domain.com>/ if empty
    payload.url = nonHttpsHandler({ url: payload.url, domain })

    // create realtime notification
    const res = await CreateRealTimeNotification({ domain, state })
    updateState({ isLoading: false, status: res })
    setSaveDisable(true)
    try {
      if (res === 'success') {
        // save notification as history once real time notification has been created
        await SaveNotificationAsHistory({ data: payload, dataSource, id: id || '', domain, type: 'realTime' })
        dispatch(fetchNotificationHistory('MSA'))
      }
    } catch (e) {
      console.log('error', e)
    } finally {
      clearAllStates()
      setSaveDisable(false)
      dispatch(setIsAddNewNotification(false))
    }
  }

  /**
   * Handler function to create a sticky notification. It first creates the
   * notification, then saves it as history and finally fetches the notification
   * history and sets the isAddNewNotification state to false.
   */
  const HandleStickyNotification = async () => {
    updateState({ isLoading: true })
    const res = await CreateStickyNotification({ state, domain })

    const {
      internalName,
      title,
      description,
      bgColor,
      stickyNotificationTemplate,
      isGloballyEnabled,
      isClosable,
      isEnabled,
    } = state

    const data = {
      internalName,
      title,
      description,
      bgColor,
      stickyNotificationTemplate,
      isGloballyEnabled,
      isClosable,
      isEnabled,
      id: res.id,
    }

    setSaveDisable(true)
    // save notification as history once sticky notification has been created
    await SaveNotificationAsHistory({ data, dataSource, id: id || '', domain, type: 'sticky' })
    dispatch(fetchNotificationHistory('MSA'))
    dispatch(setIsAddNewNotification(false))
    updateState({ status: res.status, isLoading: false })
    clearAllStates()
    setSaveDisable(false)
  }

/**
 * Updates the type of notification in the state based on the provided type.
 * 
 * @param {('realTime' | 'sticky')} type - The type of notification to set.
 *   - 'realTime': Sets the typeOfNotification to '3'.
 *   - 'sticky': Sets the typeOfNotification to '1'.
 */

  const updateTypeOfNotification = (type: 'realTime' | 'sticky') => {
    updateState({
      typeOfNotification: type === 'realTime' ? '3' : '1',
    })
  }

  const innerComp = {
    header: <MSANotificationHeader activeStep={state.activeStep} />,
    // 0: <MSANotificationStep0 state={state} updateState={updateState} />,
    0: (
      <MSANotificationStep1
        pageSelector={pageSelector}
        CTASelector={CTASelector}
        handleFileChange={handleFileChange}
        selectSingleAsset={selectThumbnail}
        state={state}
        updateState={updateState}
        carouselRemove={carouselRemove}
        carouselSelector={carouselSelector}
      />
    ),
    1: <PushNotificationSettings state={state} updateState={updateState} />,
    2: (
      <MSANotificationStep2
        specificPageRemove={specificPageRemove}
        specificPageSelector={specificPageSelector}
        categoriesPageRemove={categoriesPageRemove}
        categoriesPageSelector={categoriesPageSelector}
        state={state}
        updateState={updateState}
      />
    ),
    3: <MSANotificationStep3 state={state} />,
    footer: (
      <MSANotificationFooter
        checkFirstStepDisable={checkFirstStepDisable}
        checkSecondStepDisable={checkSecondStepDisable}
        state={state}
        updateState={updateState}
        saveDisable={saveDisable}
        clearAllStates={clearAllStates}
      />
    ),
  }

  return (
    <Box className="flex w-full flex-col justify-between items-center h-full px-4">

      {isAddNewNotification && <>
        <Box
          className={`w-full pb-5 ${state.activeStep === 0 ? 'h-[70%]' : 'h-auto'} ${
            state.dataFetching && 'opacity-50'
          }`}
        >
          {innerComp['header']}
          {innerComp[state.activeStep as keyof typeof innerComp]}
        </Box>
        {state.dataFetching && <LoadingState />}
        {innerComp['footer']}

        <ModalConfirm
          children={
            <p className="text-base">
              Once you create a notification, it can't be undone.
            </p>
          }
          handleClose={handleClose}
          onConfirm={
            state.typeOfNotification === '1'
              ? handleStickyConfirm
              : handleRealTimeConfirm
          }
          open={state.showConfirmBox}
        />
      </>}

      {!isAddNewNotification &&
        <DomainNotificationHistoryTable domain={domain} updateTypeOfNotification={updateTypeOfNotification} />}

    </Box>
  )
}

export default DomainNotifications
