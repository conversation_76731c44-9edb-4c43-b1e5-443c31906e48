import { Box, Pagination, Table, TextLink } from '@contentful/f36-components'
import React, { useState } from 'react'
import { ENVIRONMENT, SPACE_ID } from '../../../../../constant/variables'

interface Notification {
  internalName:
    | string
    | number
    | boolean
    | React.ReactElement<any, string | React.JSXElementConstructor<any>>
    | Iterable<React.ReactNode>
    | React.ReactPortal
    | null
    | undefined
  sys: { id: string }
}

interface NotificationTableProps {
  notifications: Notification[]
}

function NotificationTable({ notifications }: NotificationTableProps) {
  const [currentPage, setCurrentPage] = useState(0)
  const itemsPerPage = 6

  const getEditUrl = (id: string) => {
    const space = SPACE_ID
    const environment = ENVIRONMENT
    return `https://app.contentful.com/spaces/${space}/environments/${environment}/entries/${id}`
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const startIndex = currentPage * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentItems = notifications?.slice(startIndex, endIndex)

  return (
    <>
      <Table>
        <Table.Head>
          <Table.Row>
            <Table.Cell>Index</Table.Cell>
            <Table.Cell>Internal Name</Table.Cell>
            <Table.Cell>Action</Table.Cell>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {currentItems.map((notification, index) => (
            <Table.Row key={notification.sys.id}>
              <Table.Cell>{startIndex + index + 1}</Table.Cell>
              <Table.Cell>{notification.internalName}</Table.Cell>
              <Table.Cell>
                <TextLink
                  href={getEditUrl(notification.sys.id)}
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  Edit
                </TextLink>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
      <Box
        style={{
          height: '10px',
        }}
      />
      <Pagination
        activePage={currentPage}
        totalItems={notifications.length}
        itemsPerPage={itemsPerPage}
        onPageChange={handlePageChange}
      />
    </>
  )
}

export default NotificationTable
