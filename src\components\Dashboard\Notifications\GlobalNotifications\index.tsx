import { HomeAppSDK } from '@contentful/app-sdk'
import { Box } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import ModalConfirm from '../../../ConfirmModal'
import { domainsConfig } from '../../../Crosspost/utils'
import { GlobalNotiState, RealTimeNotificationPayload, UpdateNotiStateFunction } from '../utils/notifications.interface'
import {
  CreateRealTimeNotification,
  createSingleAsset,
  entryPageSelector,
  SaveNotificationAsHistory,
  selectSingleAsset,
} from '../utils'
import LoadingState from '../utils/LoadingState'
import GlobalNotificationFooter from './components/GlobalNotificationFooter'
import GlobalNotificationHeader from './components/GlobalNotificationHeader'
import GlobalNotificationStep1 from './components/GlobalNotificationStep1'
import GlobalNotificationStep2 from './components/GlobalNotificationStep2'
import PushNotificationSettings from '../components/PushNotificationSettings'
import {
  fetchNotificationHistory,
  setIsAddNewNotification,
} from '../../../../redux/slices/dashboard/notificationsSlice'
import { useAppDispatch, useAppSelector } from '../../../../redux/store'
import { generateRandomId } from '../../../../globals/firebase/utils'
import GlobalNotificationHistoryTable from './GlobalNotificationHistoryTable'

function GlobalNotifications({ sdk }: { sdk: HomeAppSDK }) {

  const { id, dataSource, isAddNewNotification } = useAppSelector(state => state.notificationData)

  const dispatch = useAppDispatch()

  const initialState: GlobalNotiState = {
    status: '',
    showConfirmBox: false,
    titleSource: 'seoTitle',
    descriptionSource: 'seoDescription',
    activeStep: 0,
    extension: null,
    isLoading: false,
    externalLink: '',
    title: '',
    url: '',
    description: '',
    selectedCTA: null,
    thumbnail: {
      url: '',
      status: '',
    },
    badge: {
      url: '',
      status: '',
    },
    selectedPage: null,
    typeOfNotification: '2',
    notificationType: 'both',
    dataFetching: false,
    notificationDuration: '30',
    ctaText: 'Read more',
    ctaTarget: '_self',
    notificationCategory: ['general'],
    isSystemNotification:false,
    attentionRequired:false,
    groupingCategory:"",
    renotify:false,
    isSystemNotificationDismissible:false
  }

  useEffect(() => {
    dispatch(fetchNotificationHistory('MSA'))
  }, [])


  const [state, setState] = useState<GlobalNotiState>(initialState)

  const [saveDisable, setSaveDisable] = useState(false)

  // updateState function
  const updateState: UpdateNotiStateFunction<GlobalNotiState> = (updates) => {
    setState((prevState) => ({
      ...prevState,
      ...updates,
    }))
  }

  const clearAllStates = () => updateState(initialState)

  // Handle file upload
  const handleFileChange = async (event: any, type: 'thumbnail' | 'badge') => {
    updateState({ dataFetching: true })
    const res = await createSingleAsset(event, sdk, type)
    updateState(res)
  }

  // Handle asset selection by passing field name
  const selectThumbnail = async (type: 'thumbnail' | 'badge') => {
    updateState({ dataFetching: true })
    const res = await selectSingleAsset(sdk, type)
    updateState(res)
  }

  // Handle page selection
  const pageSelector = async () => {
    updateState({ dataFetching: true })
    const res = await entryPageSelector(sdk)
    updateState(res)
  }

  const handleClose = () => updateState({ showConfirmBox: false })

  const handleOnConfirm = async () => {
    handleClose()
    await HandleRealtimeNotification()
  }

  // Check if next button is disabled
  const checkStepDisable = () => {
    const {
      title,
      description,
      url,
      thumbnail,
      externalLink,
      selectedPage,
      selectedCTA,
    } = state

    return !!(title &&
      description &&
      thumbnail.url &&
      (url || externalLink || selectedPage || selectedCTA))
  }

  // Handler func to create real time notification for all domains
  const HandleRealtimeNotification = async () => {
    updateState({ isLoading: true })

    const {
      title,
      selectedPage,
      description,
      thumbnail,
      url,
      externalLink,
      notificationType,
      notificationDuration,
      ctaText,
      ctaTarget,
      notificationCategory,
      isSystemNotification,
      attentionRequired,
      badge,
      groupingCategory,
      renotify,
      isSystemNotificationDismissible
    } = state

    const payload: RealTimeNotificationPayload = {
      title: title || selectedPage?.fields?.seoTitle?.['en-CA'],
      body: description || selectedPage?.fields?.seoDescription?.['en-CA'],
      icon: thumbnail?.url,
      url: url || externalLink || selectedPage?.fields?.slug?.['en-CA'],
      id: generateRandomId(),
      domain: '',
      type: notificationType,
      duration: notificationDuration,
      timeStamp: new Date(),
      ctaText,
      ctaTarget,
      category: JSON.stringify(notificationCategory),
      isSystemNotification:isSystemNotification.toString(),
      attentionRequired:attentionRequired.toString(),
      badge: badge?.url,
      tag:groupingCategory,
      renotify: renotify.toString(),
      isDismissible: isSystemNotificationDismissible.toString()
    }

    // remove one11 as there is no deployment for it yet
    const domainsArray = domainsConfig
      .filter((d) => d.domainKey !== 'domainOne11Com')
      .map((d) => d.key)

    // create notification promise for all domains
    const promises = domainsArray.map(async (domain) => {
      let data: any = { ...state }
      return await CreateRealTimeNotification({ state: data, domain })
    })

    const results = await Promise.all(promises)
    const allSuccessful = results.every((res) => res === 'success')
    updateState({ status: allSuccessful ? 'success' : 'error', isLoading: false })

    setSaveDisable(true)

    try {
      if (allSuccessful) {
        // once notification has been created, save it as history
        await SaveNotificationAsHistory({ data: payload, dataSource, id: id || '', domain: 'global', type: 'realTime' })
        dispatch(fetchNotificationHistory('MSA'))
      }
    } catch (e) {
      console.log('error', e)
    } finally {
      // setTimeout(() => {
      // }, 3000)
        clearAllStates()
        setSaveDisable(false)
        dispatch(setIsAddNewNotification(false))
    }
  }

  const innerComp = {
    header: <GlobalNotificationHeader state={state} />,
    0: (
      <GlobalNotificationStep1
        state={state}
        updateState={updateState}
        handleFileChange={handleFileChange}
        pageSelector={pageSelector}
        selectSingleAsset={selectThumbnail}
      />
    ),
    1: <PushNotificationSettings state={state} updateState={updateState} />,
    2: <GlobalNotificationStep2 state={state} />,
    footer: (
      <GlobalNotificationFooter
        state={state}
        updateState={updateState}
        checkStepDisable={checkStepDisable}
        saveDisable={saveDisable}
        clearAllStates={clearAllStates}
      />
    ),
  }

  return (
    <Box className="flex w-full flex-col justify-between items-center h-full px-4 relative">

      {isAddNewNotification && <>
        <Box
          className={`w-full pb-5 ${state.activeStep === 0 ? 'h-[70%]' : 'h-auto'} ${
            state.dataFetching && 'opacity-50'
          }`}
        >
          {innerComp['header']}

          {innerComp?.[state.activeStep as keyof typeof innerComp]}
        </Box>

        {innerComp['footer']}

        {state.dataFetching && <LoadingState />}

        <ModalConfirm
          children={
            <p className="text-base">
              Once you create a notification, it can't be undone.
            </p>
          }
          handleClose={handleClose}
          onConfirm={handleOnConfirm}
          open={state.showConfirmBox}
        />
      </>}

      {!isAddNewNotification && <GlobalNotificationHistoryTable />}

    </Box>
  )
}

export default GlobalNotifications
