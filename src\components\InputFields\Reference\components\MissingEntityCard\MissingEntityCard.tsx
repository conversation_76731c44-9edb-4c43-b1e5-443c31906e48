import * as React from 'react'

import {
  Card,
  Flex,
  IconButton,
  SectionHeading,
} from '@contentful/f36-components'
import { CloseIcon } from '@contentful/f36-icons'

import { ContentEntityType } from '../../types'
import * as styles from './styles'

/**
 * A card that is rendered when an entity is missing or inaccessible.
 *
 * @param entityType - The type of the entity that is missing. Can be either 'entry' or 'asset'.
 * @param asSquare - If true, the card will be square.
 * @param isSelected - If true, the card will have selected styles.
 * @param isDisabled - If true, the card will be disabled and the remove button will not be rendered.
 * @param onRemove - The function to be called when the user clicks the remove button.
 */
export function MissingEntityCard(props: {
  entityType: ContentEntityType
  asSquare?: boolean
  isSelected?: boolean
  isDisabled: boolean
  onRemove?: Function
}) {
  return (
    <Card
      className={styles.card}
      testId='cf-ui-missing-entry-card'
      isSelected={props.isSelected}
    >
      <Flex alignItems='center' justifyContent='space-between'>
        <div className={props.asSquare ? styles.squareCard : ''}>
          <SectionHeading marginBottom='none'>
            {props.entityType} is missing or inaccessible
          </SectionHeading>
        </div>
        {!props.isDisabled && props.onRemove && (
          <IconButton
            variant='transparent'
            icon={<CloseIcon variant='muted' />}
            aria-label='Delete'
            onClick={() => {
              props.onRemove && props.onRemove()
            }}
          />
        )}
      </Flex>
    </Card>
  )
}
