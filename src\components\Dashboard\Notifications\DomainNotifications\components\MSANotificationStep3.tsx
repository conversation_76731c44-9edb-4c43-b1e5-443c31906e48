import { Box } from '@contentful/f36-components'
import React from 'react'
import { DomainNotiState } from '../../utils/notifications.interface'
import SummaryItem from '../../components/summary/SummaryItem'
import { AllowedNotificationType, CTATargets, NotificationDuration, NotificationType } from '../../utils/constants'

function MSANotificationStep3({ state }: { state: DomainNotiState }) {
  const {
    url,
    title,
    bgColor,
    thumbnail,
    isEnabled,
    isClosable,
    selectedCTA,
    description,
    externalLink,
    selectedPage,
    carouselData,
    specificPages,
    categoriesPages,
    isGloballyEnabled,
    typeOfNotification,
    stickyNotificationTemplate,
    notificationDuration, notificationType,
    internalName,
    ctaTarget,
    ctaText,
    notificationCategory,
    isSystemNotification,
    badge,
    attentionRequired,
    renotify,
    groupingCategory,
    isSystemNotificationDismissible
  } = state

  const getNotificationCategoryNames = () => {
    return notificationCategory.map(cat => AllowedNotificationType.find(item => item.value === cat)?.title || '').join(', ')
  }

  return (
    <Box className="summaryRoot formRoot w-full">
      <h6 className="mt-0 mb-2">Summary</h6>
      <p className="summaryTitle">
        <b>
          {typeOfNotification === '1' ? 'Page sticky ' : 'Real time '}
          Notification{' '}
        </b>{' '}
        will be created with the following details.
        <br /> If everything looks good, click Apply to send the notification.
        You can always go back to update / amend.
      </p>
      <Box className="h-4" />
      <hr className="endHorizontalLine w-full" />
      <div className="flex flex-col gap-5 pt-4">

        <SummaryItem title={'Title'} value={title} type={'String'} />
        <SummaryItem title={'Description'} value={description} type={'String'} />
        <SummaryItem title={'URL'} value={url || externalLink || selectedPage?.fields?.slug?.['en-CA']}
                     type={'String'} />
        {typeOfNotification === '3' && (
          <>
            <SummaryItem title={'Page Resource'} type={'Links'} links={selectedPage ? [selectedPage] : []} />
            <SummaryItem title={'CTA Resource'} type={'Links'} links={selectedCTA ? [selectedCTA] : []} />
            <SummaryItem
              title={'Notification Type'}
              value={NotificationType.find(item => item.value === notificationType)?.title}
              type={'String'} />
            <SummaryItem title={'Notification Category'} value={getNotificationCategoryNames()} type={'String'} />
            <SummaryItem title={'CTA Text'} value={ctaText} type={'String'} />
            <SummaryItem
              title={'CTA Target'}
              value={CTATargets.find(item => item.value === ctaTarget)?.title}
              type={'String'} />
            <SummaryItem
              title={'Duration'}
              type={'String'}
              value={NotificationDuration.find(item => item.value === notificationDuration)?.title} />
            {/*<SummaryItem*/}
            {/*  title={'Is System Notification'}*/}
            {/*  value={isSystemNotification}*/}
            {/*  type={'Boolean'} isSmall={true} />*/}
            {/*{isSystemNotification && (*/}
            {/*  <SummaryItem*/}
            {/*    title={'Is System Notification Dismissible'}*/}
            {/*    value={isSystemNotificationDismissible}*/}
            {/*    type={'Boolean'} isSmall={true} />*/}
            {/*)}*/}
            <SummaryItem
              title={'Is Attention Required'}
              value={attentionRequired}
              type={'Boolean'} isSmall={true} />
            <SummaryItem
              title={'Renotify'}
              value={renotify}
              type={'Boolean'} isSmall={true} />
            <SummaryItem
              title={'Grouping Category'}
              value={AllowedNotificationType.find(item => item.value === groupingCategory)?.title}
              type={'String'} />
            <SummaryItem title={'Thumbnail'} type={'Asset'} thumbnail={thumbnail} />
            <SummaryItem title={'Badge'} type={'Asset'} thumbnail={badge} />
          </>
        )}
        {typeOfNotification === '1' && (
          <>
            <SummaryItem title={'Internal Name'} type={'String'} value={internalName} />
            <SummaryItem title={'Is Global'} type={'Boolean'} value={isGloballyEnabled} />
            <SummaryItem title={'Is Enabled'} type={'Boolean'} value={isEnabled} />
            <SummaryItem title={'Is Closable'} type={'Boolean'} value={isClosable} />
            <SummaryItem title={'Background Color'} type={'String'} value={bgColor} />
            <SummaryItem title={'Specific Pages'} type={'Links'} links={specificPages} />
            <SummaryItem title={'Categories Pages'} type={'Links'} links={categoriesPages} />
          </>
        )}
        {typeOfNotification === '1' && stickyNotificationTemplate === 'Carousel' && (
          <SummaryItem title={'Carousels'} type={'Links'} links={carouselData} />
        )}
      </div>
    </Box>
  )
}

export default MSANotificationStep3
