import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { Asset } from 'contentful-management'
import {
  getAssetData,
  getFieldsData
} from '../../../components/DataVisualization/utils'

const initialState = {
  tableAsset: undefined as Asset | undefined,
  tableData: {} as any,
  tableId: '',
}

export const fetchTableData = createAsyncThunk(
  'dataTable/fetchTableData',
  async (params: { entryId: string; currentLocale: string }) => {
    const { entryId, currentLocale } = params
    const entryData = await getFieldsData(entryId)
  
    if (!entryData) {
      throw new Error('Entry data not found')
    }
    const tableId = entryId
    const value = entryData.data?.[currentLocale]?.content?.[0]?.content?.[0]?.value
    const tableData = value ? JSON.parse(value) : undefined
    const sysId = entryData?.source?.[currentLocale]?.sys?.id
    const tableAsset =entryData?.source && (await getAssetData(currentLocale, entryId, sysId))
  
    return {
        tableId,
      tableData,
      tableAsset,
    }
  }
)

const tableSlice = createSlice({
  name: 'dataTable',
  initialState,
  reducers: {
    setTableId: (state, { payload }: { payload: string }) => {
      state.tableId = payload
    },
    setTableAsset: (state, { payload }: { payload: Asset | undefined }) => {
      state.tableAsset = payload
    },
    setTableData: (state, { payload }: any) => {
      state.tableData = payload
    },
  },
  extraReducers: (builder) => {
    builder.addCase(
      fetchTableData.fulfilled,
      (state, { payload }: any) => {
        state.tableId = payload.tableId
        state.tableAsset = payload.tableAsset
        state.tableData = payload.tableData
      }
    )
  },
})

export default tableSlice.reducer

export const {
  setTableData,
  setTableAsset,
  setTableId,
} = tableSlice.actions
