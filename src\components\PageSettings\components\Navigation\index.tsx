import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Checkbox, Tooltip } from '@contentful/f36-components'
import React, { useContext, useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import { GlobalContext } from '../../../../contexts/globalContext'

interface Props {
  domain: string
  sdk: EditorAppSDK
  locale: string[]
}

function Navigation(props: Props) {
  const { sdk } = props

  const sdkFields = sdk.entry.fields

  const { currentLocale } = useContext(GlobalContext)

  const [values, setValues] = useState<any>({})

  /**
   * Handle checkbox change event.
   * @param {string} fieldName - name of the field
   * @param {any} value - value of the field
   * @description
   * Sets the value of the field in the sdk and updates the state.
   * If the user manually updates the nav settings for the first time,
   * mark it as updated.
   */
  const handleCheckBox = (fieldName: string, value: any) => {
    sdkFields[fieldName].getForLocale(currentLocale).setValue(value)

    setValues((prev: any) => {
      return {
        ...prev,
        [fieldName]: value,
      }
    })

    // if user manually updates the nav settings for the first time, mark it as updated
    const pageConf = sdk.entry.fields['configurations'].getValue() || {}

    sdk.entry.fields['configurations'].setValue({
      ...pageConf,
      isDefaultNavUpdated: true,
    })

    sdk.entry.save()
  }
  useEffect(() => {
    if (sdk) {
      // get all the field values and set them in the local state on load

      const fieldNames = [
        'isHeaderNavigationHidden',
        'isTranslucent',
        'isLightBgImage',
        'isFooterNavigationHidden',
        'isNavLightMode',
      ]

      const updateValues = () => {
        const updatedValues = fieldNames.reduce((acc, fieldName) => {
          const value = sdk.entry.fields[fieldName]
            .getForLocale('en-CA')
            .getValue()
          return { ...acc, [fieldName]: value }
        }, {})

        setValues(updatedValues)
      }

      // Initial fetch of data
      updateValues()

      // Set up onValueChanged for each field
      const unsubscribeFunctions = fieldNames.map((fieldName) =>
        sdk.entry.fields[fieldName].getForLocale('en-CA').onValueChanged(() => {
          updateValues()
        })
      )

      // Cleanup all subscriptions on unmount
      return () => {
        unsubscribeFunctions.forEach((unsubscribe) => unsubscribe())
      }
    }
  }, [sdk, setValues])

  return (
    <Box className='w-full h-auto overflow-y-auto flex gap-10 p-3'>
      <Box className='w-full h-auto overflow-y-auto flex flex-col gap-10 p-3'>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isHeaderNavigationHidden']}
            onChange={(e) =>
              handleCheckBox(
                'isHeaderNavigationHidden',
                !values?.['isHeaderNavigationHidden']
              )
            }
            className='p-0'
          >
            Hide Header Navigation
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to hide the header navigation on this page.'
          >
            <Info />
          </Tooltip>
        </Box>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isTranslucent']}
            onChange={(e) =>
              handleCheckBox('isTranslucent', !values?.['isTranslucent'])
            }
            className='p-0'
          >
            Is Navigation Translucent?
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to set the navigation as translucent on this page.'
          >
            <Info />
          </Tooltip>
        </Box>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isLightBgImage']}
            onChange={(e) =>
              handleCheckBox('isLightBgImage', !values?.['isLightBgImage'])
            }
            className='p-0'
          >
            Is light background image?
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to display the CTA button as blue, otherwise, it will be yellow.'
          >
            <Info />
          </Tooltip>
        </Box>
      </Box>
      <Box className='w-full h-auto overflow-y-auto flex flex-col gap-10 p-3'>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isFooterNavigationHidden']}
            onChange={(e) =>
              handleCheckBox(
                'isFooterNavigationHidden',
                !values?.['isFooterNavigationHidden']
              )
            }
            className='p-0'
          >
            Hide Footer Navigation
          </Checkbox>
          <Tooltip
            placement='right'
            content='Enable to hide the footer on this page.'
          >
            <Info />
          </Tooltip>
        </Box>
        <Box className='flex items-center justify-start w-full gap-1.5'>
          <Checkbox
            isChecked={values?.['isNavLightMode']}
            onChange={(e) =>
              handleCheckBox('isNavLightMode', !values?.['isNavLightMode'])
            }
            className='p-0'
          >
            Is Navigation Light Mode?
          </Checkbox>
          <Tooltip
            placement='right'
            content={
              'Enable to display the logo and text in black, otherwise, they will be white.'
            }
          >
            <Info />
          </Tooltip>
        </Box>
      </Box>
    </Box>
  )
}

export default Navigation
