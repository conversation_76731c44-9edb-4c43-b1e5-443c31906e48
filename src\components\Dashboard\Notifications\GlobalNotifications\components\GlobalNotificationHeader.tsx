import { Box } from '@contentful/f36-components'
import React from 'react'
import ProgressBar from '../../../../ProgressBar'
import { GlobalNotiState } from '../../utils/notifications.interface'

function GlobalNotificationHeader({ state }: { state: GlobalNotiState }) {
  return (
    <>
      <Box className="h-1" />
      <ProgressBar completedSteps={state.activeStep} totalSteps={3} />
      <Box className="h-5" />
      <Box className="">
        <h2 className="modalTitle">
          {
            activeStepData?.[state.activeStep as keyof typeof activeStepData]
              .title
          }
        </h2>
        <Box className="py-2">
          <p className="modalSubTitle">
            <span className="subtitleHeading">Description: </span>

            <span>
                {
                  activeStepData?.[
                    state.activeStep as keyof typeof activeStepData
                    ]?.desc
                }
              </span>
          </p>
        </Box>
        <hr className="endHorizontalLine w-full" />
      </Box>
    </>
  )
}

export default GlobalNotificationHeader

const activeStepData = {
  0: {
    title: 'Add Source & Details',
    desc: 'Select the Notification Source and add related details',
  },
  1: {
    title: 'Add Notification Settings',
    desc: 'Provide notification settings.',
  },
  2: {
    title: 'Real time Notification will be created with the following details.',
    desc: 'If everything looks good, click Apply to send the notification. You can always go back to update / amend.',
  },
}
