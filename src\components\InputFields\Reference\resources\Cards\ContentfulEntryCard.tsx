import * as React from 'react'

import { Entry } from '@contentful/field-editor-shared'

import { WrappedEntryCard } from '../../entries'
import { RenderDragFn, ResourceInfo } from '../../types'

export type CardActionsHandlers = {
  onRemove?: VoidFunction
  onMoveTop?: VoidFunction
  onMoveBottom?: VoidFunction
}

export type EntryRoute = {
  spaceId: string
  environmentId: string
  entryId: string
}

type ContentfulEntryCardProps = {
  info: ResourceInfo<Entry>
  isDisabled: boolean
  renderDragHandle?: RenderDragFn
  getEntryRouteHref: (entryRoute: EntryRoute) => string
} & CardActionsHandlers

// assets are not shown for small cards (which we hardcode currently)
const resolveAsset = () => Promise.resolve()
// we don't want to show scheduled actions for resources
const resolveScheduledActions = () => Promise.resolve([])

/**
 * A component that renders a Contentful entry card with various actions and handlers.
 *
 * @param {Object} props The component props.
 * @param {ResourceInfo<Entry>} props.info The resource information containing the entry to be rendered.
 * @param {boolean} props.isDisabled Whether the card should be disabled.
 * @param {RenderDragFn} [props.renderDragHandle] The function to render the drag handle.
 * @param {VoidFunction} [props.onRemove] The function to call when the "Remove" button is clicked.
 * @param {VoidFunction} [props.onMoveTop] The function to call when the entry should be moved to the top.
 * @param {VoidFunction} [props.onMoveBottom] The function to call when the entry should be moved to the bottom.
 * @param {function} props.getEntryRouteHref Function to get the URL for the entry route, given an entry route object.
 *
 * @returns {React.ReactElement} The rendered entry card component.
 */

export function ContentfulEntryCard({
  info,
  isDisabled,
  renderDragHandle,
  onRemove,
  onMoveTop,
  onMoveBottom,
  getEntryRouteHref,
}: ContentfulEntryCardProps) {
  const resourceSys = info.resource.sys
  const spaceId = resourceSys.space.sys.id
  const environmentId = resourceSys.environment.sys.id
  const entryId = resourceSys.id

  const resourceHref = getEntryRouteHref({
    spaceId,
    environmentId,
    entryId,
  })

  // TODO: move this into `sdk.navigator.openEntry()`, note that it's signature only include the entry id (not a space or environment)
  const openEntryDetail = () => {
    window.open(resourceHref, '_blank', 'noopener,noreferrer')
  }

  return (
    <WrappedEntryCard
      entry={info.resource}
      isDisabled={isDisabled}
      hasCardEditActions={false}
      contentType={info.contentType}
      // we use the default locale from the space the entry belongs to
      // as we assume this gives a more consistent behaviour.
      // locales will inevitably differ from space to space, so it's likely
      // that the current locale does not exist in the "remote" space
      localeCode={info.defaultLocaleCode}
      defaultLocaleCode={info.defaultLocaleCode}
      size='small'
      getAsset={resolveAsset}
      getEntityScheduledActions={resolveScheduledActions}
      spaceName={info.space.name}
      renderDragHandle={renderDragHandle}
      isClickable={true}
      onEdit={openEntryDetail}
      hasCardRemoveActions={Boolean(onRemove)}
      onRemove={onRemove}
      onMoveBottom={onMoveBottom}
      onMoveTop={onMoveTop}
      entryUrl={resourceHref}
    />
  )
}
