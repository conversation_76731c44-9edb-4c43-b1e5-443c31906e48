export const navigationHeaderFields = {
  Sticky: ['navigationMenuItems', 'ctaButton', 'buttonGroup', 'isLightMode', 'htmlAttr'],
  'Non-Sticky': ['navigationMenuItems', 'ctaButton', 'buttonGroup', 'isLightMode', 'htmlAttr'],
  Dropdown: ['navigationMenuItems', 'ctaButton', 'buttonGroup', 'isLightMode', 'htmlAttr'],
  PrimaryHeader: [
    'navigationMenuItems',
    'ctaButton',
    'lightLogo',
    'darkLogo',
    'isLightMode',
    'isSearchHidden',
    'isLoginHidden',
    'isLanguageHidden',
  ],
  'Product Header': [
    'navigationMenuItems',
    'isLightMode',
    'lightLogo',
    'darkLogo',
    'buttonGroup',
  ],
}
