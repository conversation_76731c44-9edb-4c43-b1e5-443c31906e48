import { EditorAppSDK } from '@contentful/app-sdk'
import { useSDK } from '@contentful/react-apps-toolkit'
import { EditorProvider } from '@tiptap/react'
import React, { useEffect, useState } from 'react'
import { EntityProvider } from '../InputFields/Reference'
import CustomBubbleMenu from './CustomBubbleMenu'
import { extensions } from './Extensions'
import AdvanceStylingModal from './Extensions/Modals/AdvanceStylingModal'
import { HyperlinkModal } from './Extensions/Modals/HyperlinkModal'
import { MenuBar } from './MenuBar'
import './styles.scss'
import { TableMenus } from './TableMenus'
import TipTapChangeDetector from './TipTapChangeDetector'

/**
 * The Tiptap component renders a rich text editor for the given fieldId and locale.
 *
 * The component is responsible for rendering the editor, handling the context menu,
 * and storing the value of the editor in the Contentful entry.
 *
 * @param {Object} props - The properties object containing the fieldId and locale.
 * @param {string} props.fieldId - The fieldId of the Contentful entry.
 * @param {string} props.locale - The locale of the Contentful entry.
 */
export default function Tiptap(props: { fieldId: string; locale: string }) {
  const sdk = useSDK<EditorAppSDK>()

  const [showContextMenu, setShowContextMenu] = useState(false)
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 })
  const currentLocale = props.locale

  const [openHyperLinkModal, setOpenHyperLinkModal] = useState(false)

  const [openStylingModal, setOpenStylingModal] = useState(false)
  const [isDarkMode, setIsDarkMode] = useState(false)

  useEffect(() => {
    const config = sdk.entry.fields?.['configurations']?.getValue()
    if (config?.isDarkModeSwitchOn) {
      setIsDarkMode(true)
    }
  }, [])

  /**
   * Handles the context menu event.
   * @param {React.MouseEvent<HTMLDivElement>} e - The context menu event.
   */
  function handleContextMenu(e: React.MouseEvent<HTMLDivElement>) {
    e.preventDefault()
    const x = e.pageX
    const y = e.pageY
    setContextMenuPosition({ x, y })
    setShowContextMenu(true)
  }

  return (
    <EntityProvider sdk={sdk}>
      <div
        onContextMenu={handleContextMenu}
        className={`tiptapRoot ${isDarkMode ? 'darkTheme' : ''}`}
      >
        <EditorProvider
          slotBefore={
            <>
              <MenuBar
                isDarkMode={isDarkMode}
                setIsDarkMode={setIsDarkMode}
                handleHyperLinkModal={setOpenHyperLinkModal}
                handleStylingModal={setOpenStylingModal}
              />
            </>
          }
          onUpdate={({ editor }) => {
            console.log('editor.getJSON(): ', currentLocale, editor.getJSON())
            sdk.entry.fields[props.fieldId]
              .getForLocale(currentLocale)
              .setValue(editor.getJSON())
          }}
          extensions={extensions}
          content={sdk.entry.fields[props.fieldId]
            .getForLocale(currentLocale)
            .getValue()}
        >
          <TipTapChangeDetector
            sdk={sdk}
            fieldId={props.fieldId}
            currentLocale={currentLocale}
          />
          <CustomBubbleMenu fieldId={props.fieldId} />
          <HyperlinkModal
            sdk={sdk}
            fieldId={props.fieldId}
            onClose={(value) => setOpenHyperLinkModal(false)}
            readonly={false}
            locale={currentLocale}
            isOpen={openHyperLinkModal}
          />
          <AdvanceStylingModal
            isOpen={openStylingModal}
            handleClose={setOpenStylingModal}
          />

          <TableMenus
            contextMenuPosition={contextMenuPosition}
            setShowContextMenu={setShowContextMenu}
            showContextMenu={showContextMenu}
          />
        </EditorProvider>
      </div>
    </EntityProvider>
  )
}
