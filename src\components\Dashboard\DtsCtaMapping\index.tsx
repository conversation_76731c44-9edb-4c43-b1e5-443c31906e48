import { HomeAppSDK } from '@contentful/app-sdk'
import { Box } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { ENV_VARIABLES } from '../../../constant/variables'
import {
  CreateConfigurationEntry,
  UpdateConfigurationEntry,
} from '../../../globals/config-helpers'
import { getConfigurationByScopeAndType } from '../../../globals/utils'
import TemplateCtaMapper from './components/TemplateCtaMapper'
import './index.scss'

function DtsCtaMapping({ sdk }: { sdk: HomeAppSDK }) {
  const [DtsTemplates, setDtsTemplates] = useState<any>([])

  const [contentId, setContentId] = useState<string>('')

  const envId = ENV_VARIABLES.contentfulEnvironment

  const spaceId = ENV_VARIABLES.contentfulSpaceID

  const [dtsCtaByCatByDomain, setDtsCtaByCatByDomain] = useState<any>({})

  // const { dtsTemplate } = useSelector(
  //   (state: RootState) => state.dashboardGlobal
  // )

  const [activeCategory, setActiveCategory] = useState<string>('')

  /**
   * Updates the dtsCtaByCatByDomain state and saves the DTS CTA mapping configuration
   * @param {string} activeCat - The active category selected
   * @param {{ [key: string]: string }} data - The DTS CTA mapping data
   */
  const handleDtsCtaMapping = async (
    activeCat: string,
    data: { [key: string]: string }
  ) => {
    let oldData = { ...dtsCtaByCatByDomain }

    oldData = { ...oldData, [activeCat]: data }

    setDtsCtaByCatByDomain({ ...dtsCtaByCatByDomain, [activeCat]: data })
    await handleSaveDtsCtaMapping(oldData)
  }

/**
 * Saves or updates the DTS CTA mapping configuration.
 * If a configuration exists, it updates the existing configuration entry.
 * Otherwise, it creates a new configuration entry with the provided data.
 * 
 * @param {any} dtsCtaData - The DTS CTA mapping data to be saved or updated
 */

  const handleSaveDtsCtaMapping = async (dtsCtaData: any) => {
    if (contentId) {
      // if entry exists, then update the data else create a new crosspost configuration

      await UpdateConfigurationEntry({
        contentId,
        data: dtsCtaData,
        envId,
        spaceId,
      })
    } else {
      const res = await CreateConfigurationEntry({
        data: dtsCtaData,
        type: 'DTS CTA Mapping',
        scope: 'MSA',
        internalName: `Crosspost DTS CTA Mapping - MSA`,
        envId,
        spaceId,
      })

      setContentId(res)
    }
  }

  /**
   * Fetches the DTS CTA mapping configuration from the Contentful API.
   * If the configuration exists, it sets the contentId state to the configuration's id,
   * and sets the dtsCtaByCatByDomain state to the configuration's data.
   */
  const fetchDtsConfig = async () => {
    const res: any = await getConfigurationByScopeAndType(
      'DTS CTA Mapping',
      'MSA'
    )

    if (res) {
      setContentId(res?.id)
      setDtsCtaByCatByDomain(res?.data)
    }
  }

  // useEffect(() => {
  //   if (dtsTemplate.length > 0) {
  //     setDtsTemplates(dtsTemplate)
  //   }
  // }, [dtsTemplate])

  useEffect(() => {
    fetchDtsConfig()
  }, [])

  return (
    <Box className='flex flex-col items-start w-full justify-start gap-5 h-full'>
      <div className={'dtsCtaMappingRoot'}>
        <div className={'dtsCtaMappingInnerRoot'}>
          <aside className='dtsCtaSidebar'>
            <div className={'dtsCtaListRoot'}>
              <div className={'dtsCtaCategoryRoot'}>
                <ul>
                  {DtsTemplates.map((item: string) => {
                    return (
                      <li
                        className={`${
                          activeCategory === item ? 'active' : ''
                        } pointer dtsCtaCatList dtsCtaCategoryListItem pl-4 py-4`}
                        onClick={() => setActiveCategory(item)}
                        key={item}
                      >
                        {splitByCapital(item)}
                      </li>
                    )
                  })}
                </ul>
              </div>
            </div>
          </aside>

          <main className={'dtsCtaMainRoot'}>
            {activeCategory && (
              <TemplateCtaMapper
                sdk={sdk}
                onChange={(data) => handleDtsCtaMapping(activeCategory, data)}
                ctaByDomainData={dtsCtaByCatByDomain?.[activeCategory] || {}}
              />
            )}
          </main>
        </div>
      </div>
    </Box>
  )
}

export default DtsCtaMapping

function splitByCapital(item: string) {
  return item.replace(/([a-z])([A-Z])/g, '$1 $2')
}
