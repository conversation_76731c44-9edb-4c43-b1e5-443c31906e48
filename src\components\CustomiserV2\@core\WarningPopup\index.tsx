import React from 'react'
import ReactDOMServer from 'react-dom/server'
import { Tooltip as ReactTooltip } from 'react-tooltip'
import GenericIcon from '../../../Icons/SysIcon'
import TheAnimatedNode from '../../../TheAnimatedNode'
import { findCodeNameByHex, reorderSummaryObject } from '../../helper'
import styles from './index.module.scss'

interface Props {
  onConfirm: () => void
  onClose: () => void
  onReject: () => void
  showWarningModal: boolean
  data: {
    [key: string]: string
  }
  selectedText: string
}

const WarningPopup = ({
  onClose,
  onConfirm,
  onReject,
  showWarningModal,
  data,
  selectedText,
}: Props) => {
  const opacity = {
    closed: { opacity: 0 , display: 'none'},
    open: { opacity: 1, display: 'flex' },
  }
  const scale = {
    closed: { opacity: 0,scale: 0.8, display: 'none' },
    open: {  opacity: 1,scale: 1, display: 'flex' },
  }

  const getLineClampClass = () => {
    switch (data['Heading type']) {
      case 'h1':
        return 'line-clamp-2 leading-tight'
      case 'h2':
        return 'line-clamp-3 leading-snug'
      case 'h3':
        return 'line-clamp-4 leading-relaxed'
      case 'h4':
        return 'line-clamp-5 leading-loose'
      case 'h5':
        return 'line-clamp-6 leading-loose'
      case 'h6':
        return 'line-clamp-[7] leading-loose'
      case 'subheading':
        return 'line-clamp-[9] leading-relaxed'
    }
  }

  const TheSummary = (
    <div style={{ textAlign: 'left' }}>
      <p className='fs3 fSansBld'>Summary</p>
      <table style={{ width: '100%', borderCollapse: 'collapse' }}>
        <tbody>
          {Object.keys(reorderSummaryObject(data)).map((key) => {
            let value = data[key]
            if (key === 'Color' || key === 'Highlight')
              value = findCodeNameByHex(value) || ''

            if (!value) return null

            return (
              <tr key={String(key)}>
                <td
                  style={{
                    padding: '4px 0',
                    width: '100px',
                    verticalAlign: 'top',
                  }}
                >
                  {key}
                </td>
                <td
                  style={{
                    padding: '4px 0',
                    verticalAlign: 'top',
                    textAlign: 'left',
                    fontWeight: 'bold',
                  }}
                >
                  {typeof value === 'object' ? JSON.stringify(value) : value}
                </td>
              </tr>
            )
          })}
        </tbody>
      </table>
    </div>
  )

  const TheSummaryHtml = ReactDOMServer.renderToStaticMarkup(TheSummary)

  return (
    <TheAnimatedNode
      as='div'
      htmlAttr={{ className: styles.overlayContainer }}
      animatedProps={{
        initial: { opacity: 0, display: 'none' }, // Initial state (closed)
        animate: showWarningModal ? 'open' : 'closed',
        variants: opacity,
        transition: { duration: 0.5, ease: [0.68, -0.55, 0.27, 1.55] },
      }}
    >
      <TheAnimatedNode
      as='div'
      htmlAttr={{ className: styles.modalContent }}
      animatedProps={{
        initial: { scale: 1, display: 'none' }, // Initial state (closed)
        animate: showWarningModal ? 'open' : 'closed',
        variants: scale,
        transition: { duration: 0.75, ease: [0.68, -0.55, 0.27, 1.55] },
      }}
    >
        <div className={styles.modalHeader}>
          <p className={'fSansBld'}>You haven't applied these styles</p>
          <GenericIcon
            icon={'Close'}
            htmlAttr={{ className: styles.modalClose, onClick: onClose }}
            iconColour={'cn1'}
            size={'md'}
          />
        </div>

        <p
          className={`overflow-hidden ${data?.Family || ''} ${
            data?.Size || ''
          } ${data?.['Heading type'] || ''} ${getLineClampClass()}`}
          style={{
            textAlign: 'left',
            color: data?.Color || 'black',
            backgroundColor: data?.Highlight || 'white',
            padding: '10px',
          }}
        >
          {selectedText}
        </p>

        <div className={styles.modalFooter}>
          <p className={'fSansBld'}>
            Do you want to apply
            <ReactTooltip
              id='summary2-tooltip'
              style={{
                backgroundColor: '#000',
                zIndex: '6',
              }}
            />
            <span
              style={{ textDecoration: 'underline', cursor: 'pointer' }}
              data-tooltip-id='summary2-tooltip'
              data-tooltip-html={TheSummaryHtml}
            >
              &nbsp;your changes?
            </span>
          </p>
          <div className={styles.modalBtns}>
            <button className={styles.discardBtn} onClick={onReject}>
              No
            </button>
            <button className={styles.applyBtn} onClick={onConfirm}>
              Yes
            </button>
          </div>
        </div>
      </TheAnimatedNode>
    </TheAnimatedNode>
  )
}

export default WarningPopup