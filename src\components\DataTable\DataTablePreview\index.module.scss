.tableRow {
    background-color: #fff;
}

.oddRow {
    background-color: #fff;
}

.evenRow {
    background-color: #f2f2f2;
}

.xlRow {
    height: 64px;

    td {
        padding: 12px 16px !important;
        /* Adjust padding to fit 64px row */
    }
}

.lgRow {
    height: 48px;

    td {
        padding: 8px 16px !important;
        /* Adjust padding to fit 48px row */
    }
}

.mdRow {
    height: 40px;

    td {
        padding: 6px 16px !important;
        /* Adjust padding to fit 40px row */
    }
}

.smRow {
    height: 32px;

    td {
        padding: 3px 16px !important;
        /* Adjust padding to fit 32px row */
    }
}

.xsRow {
    height: 24px;

    td {
        padding: 0px 16px !important;
        height: inherit;
        /* Adjust padding to fit 24px row */
    }
}

.selectedRow {}


.TableToolBar {
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 30px;
    cursor: default;

    .searchConatiner {
        position: relative;
        display: flex;
        justify-content: end;
        width: 60%;
        cursor: default;
        align-items: center;

        .searchInput {
            background: '#fff';
            border: 1px solid '#e5e5e5';
            border-radius: 4px;
            width: 0;
            overflow: hidden;
            transition: width 0.3s ease-in-out;
            opacity: 0;
            height: 40px;
        }

        .searchInput.expanded {
            //  padding: 0 30px;
            width: 90%;
            opacity: 1;
        }

        .searchIcon {
            opacity: 1;
            margin-right: -10px;
            transition: opacity 0.3s ease-in-out;
            height: inherit;
        }

        .searchIcon.expanded {
            width: 0;
            opacity: 0;
            padding: 0;
        }
    }
}

.table {
    margin-top: 15px;
    .pagination {
        background-color: '#f9f9f9';
        padding: 10px 0;
        margin: 0 !important;
    }

    .expandedIcon {
        height: inherit;
    }

    .expContent {
        margin-left: 56px;
    }

    .expContentWSel {
        margin-left: 104px;
    }

    tr {
        cursor: default;

        th {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: border-color 0.3s ease-in-out, background-color 0.3s ease-in-out;

            &::before {
                display: none;
            }
        }

        td {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

        }
    }
}

.customPagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: '#f9f9f9';
    color: '#333';
    padding: 16px 16px;
    cursor: default;

    .paginationCol {
        display: flex;
        align-items: center;
        gap: 50px;

        .pageSizeSelector select {
            margin-left: 8px;
            padding: 4px;
            border: none;
            background-color: transparent;
            cursor: pointer;
        }

        .pageOfTotal,
        .pageSizeSelector {}

        .pageNav {
            button {
                background: transparent;
                border: none;
                cursor: pointer;
                padding: 4px;
            }

            .pageOfTotal, .rightChevron {
                margin-left: 30px;
            }
        }

        .pageNav button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }
}