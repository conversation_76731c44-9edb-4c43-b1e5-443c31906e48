import { Radio } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { GrCheckmark } from 'react-icons/gr'
import Info from '../../../../../../assets/icons/Info'
import { cleanSlug } from '../../../../../../globals/experimentation-util'
import {
  unpublishEntry,
  updateEntryData,
} from '../../../../../../globals/utils'
import {
  Button,
  Modal,
  notification,
  Table,
  Tooltip,
} from '../../../../../atoms'
import { EntryLink, publishingContentflags } from '../../experiment-const'
import { ExperimentationCRUD } from '../../experimentation-helper'

const CompleteExperiment = ({
  data,
  experimentData,
  ActiveDomain,
  isLoading,
  setIsLoading,
  SaveConfigurationandFetchData,
}: {
  data: ExperimentRootData
  experimentData: ExperimentationData
  ActiveDomain?: string
  isLoading?: boolean
  setIsLoading: Function
  SaveConfigurationandFetchData: Function
}) => {
  const [openDoneExperiment, setExperimentDone] = useState<boolean>(false)
  // const [needToDelete, setNeedtoDelete] = useState(false)
  const [pageToPromote, setPageToPromote] = useState('')
  useEffect(() => {
    if (experimentData?.experimentationPages && !isLoading) {
      const defaultSelected = experimentData.experimentationPages?.[0]?.id
      setPageToPromote(defaultSelected)
    }
  }, [experimentData, openDoneExperiment, isLoading])
  const handelExperimentDone = async () => {
    if (experimentData && pageToPromote) {
      try {
        setIsLoading(true)
        // Here you can perform any actions needed with the selected page
        const results = ExperimentationCRUD.getOne(
          data,
          experimentData?.experimentationId
        )
        if (results?.status) {
          // const res = ExperimentationCRUD.delete(
          //   data,
          //   experimentData?.experimentationId
          // )
          const res = ExperimentationCRUD?.edit(
            data,
            experimentData?.experimentationId,
            {
              isDeleted: true,
              isShiped: true,
              shipedPageId: pageToPromote,
              isPublished: false,
            }
          )
          const pagesToDelete = experimentData?.experimentationPages?.filter(
            (el) => el?.id !== pageToPromote
          )
          // console.log(res, deletData)
          const updatePagesPromises =
            pagesToDelete?.map((pagetoupdate) =>
              updateEntryData(
                pagetoupdate?.id,
                {
                  isExperimentation: {
                    'en-CA': false,
                  },
                  experimentationId: {
                    'en-CA': '',
                  },
                  slug: {
                    'en-CA': cleanSlug(pagetoupdate?.slug),
                    'fr-CA': cleanSlug(pagetoupdate?.slug),
                  },
                },
                publishingContentflags?.completeExp
              )
            ) ?? []
          await Promise.all(updatePagesPromises)
          await updateEntryData(
            pageToPromote,
            {
              noFollow: { 'en-CA': false },
              noIndex: { 'en-CA': false },
              hideFromAlgolia: { 'en-CA': false },
              experimentationId: {
                'en-CA': '',
              },
              isExperimentation: {
                'en-CA': false,
              },
              slug: {
                'en-CA': experimentData?.masterPage,
                'fr-CA': experimentData?.masterPage,
              },
            },
            true
          )
          // const archievOrDeletePagesPromises =
          //   pagesToDelete?.map((pagetoupdate) =>
          //     needToDelete
          //       ? deleteEntry(pagetoupdate?.id)
          //       : archiveEntry(pagetoupdate?.id)
          //   ) ?? []
          const archievOrDeletePagesPromises =
            pagesToDelete?.map((pagetoupdate) =>
              unpublishEntry(pagetoupdate?.id)
            ) ?? []
          await Promise.all(archievOrDeletePagesPromises)
          await SaveConfigurationandFetchData(res?.data)

          notification.success({
            message: (
              <>
                <p
                  className={'fSansBld'}
                  style={{ lineHeight: 1, marginBottom: '15px' }}
                >
                  {`${experimentData?.experimentTitle} experiment is now completed.`}
                </p>
                <p
                  className={'fSansLight'}
                  style={{ lineHeight: 1, marginBottom: '15px' }}
                >
                  page will be avilable in sitemap and algolia as well indexable
                  by google.
                </p>
              </>
            ),
            description: 'Do not forget to push your changes to live.',
          })
          handleClose()
          setIsLoading(false)
        }
      } catch (error) {
        setIsLoading(false)
        notification.error({
          message: (
            <p
              className={'fSansBld'}
              style={{ lineHeight: 1, marginBottom: '15px' }}
            >
              Something went wrong
            </p>
          ),
          description: `${JSON.stringify(error)}`,
        })
      }
    } else {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Please select page to complete experiment
          </p>
        ),
      })
    }
  }
  const handleClose = () => {
    // setDeleteData(null)
    // setExperimentData(null)
    setExperimentDone(false)
    setPageToPromote('')
    // setNeedtoDelete(false)
  }

  const handleRadioChange = (id: string) => {
    setPageToPromote(id) // Set the selected page as the promoted one
  }
  return (
    <>
      {' '}
      <Modal
        open={openDoneExperiment}
        onCancel={handleClose}
        onClose={handleClose}
        destroyOnClose
        closable={!isLoading}
        maskClosable={!isLoading}
        cancelButtonProps={{
          disabled: isLoading,
        }}
        okButtonProps={{
          disabled: isLoading,
          loading: isLoading,
          className: 'bg-neutral-950 text-white',
        }}
        onOk={handelExperimentDone}
        // onOk={()=>{
        //   // console.log({
        //   //   experimentData,
        //   //   unpublishPage,
        //   //   sele : experimentData?.experimentationPages?.filter((page) =>
        //   //     unpublishPage?.some((pageId) => pageId === page?.id) // Only include pages that are in the unpublishPage array
        //   //   )
        //   // })
        // }}
        okText='Confirm'
        centered
        title={
          <div className='pr-5'>
            <h3 className='text-xl font-semibold flex items-center gap-4 justify-between'>
              Complete experiment "{experimentData?.experimentTitle}"
            </h3>
            <p className='text-gray-500 mt-2'>
              {experimentData?.experimentDescription}
            </p>
            <div className='text-sm text-gray-400 mt-4'>
              Routing URL:{' '}
              <a
                target='_blank'
                rel='noopener noreferrer'
                href={`${ActiveDomain}${experimentData?.masterPage}`}
                className='font-medium text-gray-700'
              >
                {experimentData?.masterPage}{' '}
              </a>
            </div>
          </div>
        }
        className='w-full '
      >
        <Table
          dataSource={experimentData?.experimentationPages}
          pagination={false}
          scroll={{ x: 'auto' }}
          locale={{ emptyText: 'No data For Page To View' }}
          columns={[
            {
              title: 'Title',
              dataIndex: 'internalName',
              key: 'internalName',
              width: 150,
              render: (link: string, row) =>
                row?.id ? (
                  <a
                    href={`${EntryLink}/${row?.id}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-blue-400 flex justify-between items-center'
                  >
                    {`${row?.internalName ?? 'N/A'}`}
                  </a>
                ) : (
                  'N/A'
                ),
            },
            // {
            //   title: 'Internal URL',
            //   dataIndex: 'link',
            //   key: 'link',
            //   width: 200,
            //   render: (_, row) => {
            //     const url = `${ActiveDomain}${row?.slug}`
            //     return (
            //       <a
            //         href={url}
            //         target='_blank'
            //         rel='noopener noreferrer'
            //         className='hover:text-blue-400 flex justify-between items-center'
            //       >
            //         {row?.slug}
            //       </a>
            //     )
            //   },
            // },
            {
              title: 'External URL',
              dataIndex: 'slug',
              key: 'slug',
              width: 200,
              render: (_, __, index) => {
                const variant = (experimentData?.historyCount ?? 0) + index
                const url = `${ActiveDomain}${experimentData?.masterPage}?variant=${variant}`

                return (
                  <a
                    href={url}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-blue-400 flex justify-between items-center'
                  >
                    {`${experimentData?.masterPage}?variant=${variant}`}
                  </a>
                )
              },
            },
            {
              title: (
                <Tooltip
                  title={
                    'Select the variant that will be promoted to the routing URL. The selected variant will also be published.'
                  }
                >
                  <p className='flex gap-3 justify-center items-center'>
                    Promote
                    <Info />
                  </p>
                </Tooltip>
              ),
              dataIndex: 'id',
              key: 'id',

              render: (id: string) => (
                <Radio
                  isChecked={pageToPromote === id}
                  onChange={(e) => handleRadioChange(id)}
                />
              ),
              width: 100,
            },
          ]}
        />
      </Modal>
      <Tooltip
        title={
          experimentData?.isDeleted
            ? experimentData?.shipedPageId
              ? 'This experiment is already completed.'
              : 'This experiment was deleted.'
            : 'Complete Experiment'
        }
      >
        <Button
          onClick={() => {
            setExperimentDone(true)
          }}
          disabled={experimentData?.isDeleted}
          type='primary'
          className={'text-neutral-950 bg-transparent border-none'}
        >
          <GrCheckmark size={20} />
          {/* Delete Document */}
        </Button>
      </Tooltip>
      {/* </ModalConfirm> */}
      {/* <CustomButton
        variant='secondary'
        tooltipText={
          experimentData?.isDeleted
            ? experimentData?.shipedPageId
              ? 'This experiment is already completed.'
              : 'This experiment was deleted.'
            : 'Complete Experiment'
        }
        size='small'
        tooltipPlacement='top'
        startIcon={<Done />}
        isDisabled={experimentData?.isDeleted}
        onClick={() => {
          setExperimentDone(true)
          // const dataofExp = ExperimentationCRUD.getOne(
          //   data,
          //   experimentData?.experimentationId
          // )
          // if (dataofExp?.status) {
          //   setExperimentData(dataofExp?.data)
          // }
        }}
      /> */}
    </>
  )
}

export default CompleteExperiment
