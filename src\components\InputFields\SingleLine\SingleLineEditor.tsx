import * as React from 'react'

import { TextInput } from '@contentful/f36-components'
import {
  Char<PERSON>ounter,
  CharValidation,
  ConstraintsUtils,
  FieldAPI,
  FieldConnector,
  LocalesAPI,
} from '@contentful/field-editor-shared'

import * as styles from './styles'

export interface SingleLineEditorProps {
  /**
   * is the field disabled initially
   */
  isInitiallyDisabled: boolean

  /**
   * is the field manually disabled
   */
  isDisabled?: boolean

  /**
   * whether char validation should be shown or not
   */
  withCharValidation: boolean

  /**
   * whether char count should be shown or not
   */
  charCounter?: boolean

  /**
   * sdk.field
   */
  field: FieldAPI

  /**
   * sdk.locales
   */
  locales: LocalesAPI

  /**
   * onValueChange callback
   */

  onValueChange?: (value: string, fieldId: string, locale: string) => void

  /**
   * onFocus function
   */

  onFocus?: (fieldId: string, locale: string) => void
}

/**
 * Checks if the given field type is one of the supported types.
 *
 * @param val - the field type to check
 * @returns whether the given field type is supported
 */
function isSupportedFieldTypes(val: string): val is 'Symbol' | 'Text' {
  return val === 'Symbol' || val === 'Text'
}

/**
 * SingleLineEditor is a React component that renders a single line input
 * editor. It is used by the `Symbol` and `Text` field types.
 *
 * @example
 * import { SingleLineEditor } from '@contentful/field-editor-single-line'
 *
 * <SingleLineEditor
 *   field={field}
 *   locales={locales}
 *   withCharValidation={true}
 *   charCounter={true}
 *   isInitiallyDisabled={false}
 *   isDisabled={false}
 *   onValueChange={(value, fieldId, locale) => {
 *     // handle value change
 *   }}
 *   onFocus={(fieldId, locale) => {
 *     // handle focus event
 *   }}
 * />
 *
 * @param {SingleLineEditorProps} props - component props
 * @returns {ReactElement} - a single line input editor
 */
export function SingleLineEditor(props: SingleLineEditorProps) {
  const { field, locales, onValueChange, onFocus } = props

  if (!isSupportedFieldTypes(field.type)) {
    throw new Error(
      `"${field.type}" field type is not supported by SingleLineEditor`
    )
  }

  const constraints = ConstraintsUtils.fromFieldValidations(
    field.validations,
    field.type
  )
  const checkConstraint = ConstraintsUtils.makeChecker(constraints)
  const direction = locales.direction[field.locale] || 'ltr'

  const handleOnFocus = () => {
    onFocus && onFocus(field.id, field.locale)
  }

  return (
    <FieldConnector<string>
      field={field}
      isInitiallyDisabled={props.isInitiallyDisabled}
      isDisabled={props.isDisabled}
    >
      {({ value, errors, disabled, setValue }) => {
        return (
          <div data-test-id='single-line-editor'>
            <TextInput
              className={direction === 'rtl' ? styles.rightToLeft : ''}
              isRequired={field.required}
              isInvalid={errors.length > 0}
              isDisabled={disabled}
              value={value || ''}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                onValueChange &&
                  onValueChange(e.target.value, field.id, field.locale)
                setValue(e.target.value)
              }}
              onFocus={handleOnFocus}
            />
            {props.withCharValidation && (
              <div className={styles.validationRow}>
                <CharCounter
                  value={value || ''}
                  checkConstraint={checkConstraint}
                />
                <CharValidation constraints={constraints} />
              </div>
            )}
            {props.withCharValidation === false && props.charCounter && (
              <div className={styles.validationRow}>
                <CharCounter value={value || ''} checkConstraint={() => true} />
              </div>
            )}
          </div>
        )
      }}
    </FieldConnector>
  )
}

SingleLineEditor.defaultProps = {
  isInitiallyDisabled: true,
  withCharValidation: true,
}
