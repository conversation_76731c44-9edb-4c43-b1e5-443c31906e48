import React, { useState } from 'react'
import { Modal, Button } from 'antd'

type GroupDataViewerProps = {
  data: Record<string, any>
}

const GroupDataViewer = ({ data }: GroupDataViewerProps) => {
  const [visible, setVisible] = useState(false)

  if (process.env.NODE_ENV !== 'development') {
    return null
  }
  return (
    <>
      <Button onClick={() => setVisible(true)}>View Data</Button>
      <Modal
        open={visible}
        onCancel={() => setVisible(false)}
        onOk={() => setVisible(false)}
        title='Group Data'
        okText='Close'
        cancelButtonProps={{ style: { display: 'none' } }}
      >
        <pre
          style={{
            background: '#f5f5f5',
            padding: '16px',
            borderRadius: '4px',
            maxHeight: '400px',
            overflowY: 'auto',
          }}
        >
          {JSON.stringify(data, null, 2)}
        </pre>
      </Modal>
    </>
  )
}

export default GroupDataViewer
