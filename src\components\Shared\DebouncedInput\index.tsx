import { TextInput } from '@contentful/f36-components'
import React, { memo, useEffect, useState } from 'react'

type DebouncedInputProps = {
  onDebouncedChange: (value: string) => void
  delay?: number // Optional: default is 1000ms
  initialValue: string
  isLoading?: boolean
}

const DebouncedInput: React.FC<DebouncedInputProps> = ({
  onDebouncedChange,
  delay = 1000,
  initialValue,
  isLoading,
}) => {
  const [value, setValue] = useState<string>('')
  const [debouncedValue, setDebouncedValue] = useState<string>('')

  const [hasRendered, setHasRendered] = useState(false)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  useEffect(() => {
    if (initialValue !== undefined && initialValue !== null) {
      setValue(initialValue)
      setDebouncedValue(initialValue)
      setHasRendered(true)
    }

    return () => {
      setHasRendered(false)
    }
  }, [initialValue])

  useEffect(() => {
    if (hasRendered && initialValue !== debouncedValue) {
      onDebouncedChange(debouncedValue)
    }
  }, [debouncedValue, onDebouncedChange, hasRendered, initialValue])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value)
  }

  return (
    <TextInput
      type='text'
      value={value}
      onChange={handleChange}
      isDisabled={isLoading}
    />
  )
}

export default memo(DebouncedInput)
