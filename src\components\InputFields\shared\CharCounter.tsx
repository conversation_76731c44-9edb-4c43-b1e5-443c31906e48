import * as React from 'react'

import tokens from '@contentful/f36-tokens'
import { css, cx } from 'emotion'

interface CharCounterProps {
  value?: string
  checkConstraint: (n: number) => boolean
}

const styles = {
  invalid: css({
    color: tokens.red600,
  }),
}

/**
 * CharCounter is a component that displays the number of characters in the
 * input text and adds a red color to it if the text length is invalid.
 *
 * Props:
 *   - `value`: The text to count. If not provided, the component will render
 *     "0 characters".
 *   - `checkConstraint`: A function that takes a number and returns true if the
 *     number of characters is valid, false otherwise.
 */
export function CharCounter(props: CharCounterProps) {
  let count = 0
  if (props.value) {
    count = props.value.length
  }
  const valid = count === 0 || props.checkConstraint(count)
  return (
    <span
      data-status-code={valid ? null : 'invalid-size'}
      className={cx({
        [styles.invalid]: !valid,
      })}
    >
      {count} characters
    </span>
  )
}
