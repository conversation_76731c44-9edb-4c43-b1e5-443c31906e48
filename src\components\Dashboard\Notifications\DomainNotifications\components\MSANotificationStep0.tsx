import { Box, Select } from '@contentful/f36-components'
import React from 'react'
import FormControlComp from '../../../../Shared/FormControlComp'
import {
  StickyNotificationTemplates,
  TypeOfNotifications,
} from '../../utils/constants'
import { DomainNotiState, UpdateNotiStateFunction } from "../../utils/notifications.interface"

/**
 * This component renders the 0th step of the Multi-Step Notification (MSN) creation form.
 * It displays a dropdown for selecting the type of notification (Sticky or Realtime).
 * If the type of notification is Sticky, it also renders a dropdown for selecting the template.
 * The component takes in the current notification state and an update function to update the state.
 * When the user selects a different type of notification or template, the component updates the state accordingly.
 */
function MSANotificationStep0({
  state,
  updateState,
}: {
  state: DomainNotiState
  updateState: UpdateNotiStateFunction<DomainNotiState>
}) {
  const { typeOfNotification, stickyNotificationTemplate } = state

  return (
    <Box className='flex w-full flex-col justify-center items-center h-full gap-5'>
      <FormControlComp
        label='Type of Notification'
        tooltip='Select either Sticky Notification or Realtime Notification'
        isRequired
        className='smallSelect'
      >
        <Select
          value={typeOfNotification}
          onChange={(e: any) => {
            updateState({
              typeOfNotification: e?.target?.value,
              stickyNotificationTemplate: '',
              selectedPage: null,
              selectedCTA: null,
              url: '',
              externalLink: '',
              carouselData: [],
              thumbnail: {
                url: '',
                status: '',
              },
            })
          }}
        >
          <Select.Option value='' isDisabled>
            Select
          </Select.Option>
          {TypeOfNotifications?.map((option) => (
            <Select.Option value={option?.value}>{option?.title}</Select.Option>
          ))}
        </Select>
      </FormControlComp>

      {typeOfNotification === '1' && (
        <FormControlComp
          label='Template'
          tooltip='Select template for Sticky notification'
          isRequired
          className='smallSelect'
        >
          <Select
            value={stickyNotificationTemplate}
            onChange={(e: any) => {
              updateState({
                specificPages: [],
                categoriesPages: [],
                selectedCTA: null,
                selectedPage: null,
                carouselData: [],
                stickyNotificationTemplate: e?.target?.value,
              })
            }}
          >
            <Select.Option value='' isDisabled>
              Select
            </Select.Option>
            {StickyNotificationTemplates?.map((option) => (
              <Select.Option value={option?.value}>
                {option?.title}
              </Select.Option>
            ))}
          </Select>
        </FormControlComp>
      )}
    </Box>
  )
}

export default MSANotificationStep0
