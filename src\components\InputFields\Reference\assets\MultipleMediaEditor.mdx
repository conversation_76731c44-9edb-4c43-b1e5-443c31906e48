import { <PERSON><PERSON>, <PERSON>a, <PERSON><PERSON>, <PERSON>, <PERSON>s } from '@storybook/blocks';
import * as EditorStories from '../../stories/MultipleMediaEditor.stories';

import Readme from '../../README.md?raw';

<Markdown>{Readme}</Markdown>

```jsx
import { MultipleMediaEditor } from '@contentful/field-editor-reference';
```

<Meta of={EditorStories} />

## In Action

<Story of={EditorStories.Default} />

---

<Story of={EditorStories.Link} />

## With custom actions

Note the alternative link actions injected via the `renderCustomActions` prop.

<Story of={EditorStories.CustomActions} />

## With custom card

<Story of={EditorStories.CustomCard} />

## Props

<Controls />
