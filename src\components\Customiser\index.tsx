/**
 * Customiser Component - Legacy Text Styling Interface
 *
 * This is the original customizer component that provides basic text styling capabilities
 * for the Tiptap rich text editor. It offers a simple interface for applying colors,
 * highlights, and predefined style classes to selected text.
 *
 * Key Features:
 * - Basic color selection for text and highlights
 * - Predefined style categories (Highlight, Color, Font Weight, etc.)
 * - Integration with ColorsInputV2 for color picking
 * - Direct application of styles to Tiptap editor
 *
 * Note: This is the legacy version. CustomiserV2 provides enhanced functionality.
 */

import {
  Button,
  ButtonGroup,
  IconButton,
  Radio,
} from '@contentful/f36-components'
import { CheckCircleIcon } from '@contentful/f36-icons'
import { useCurrentEditor } from '@tiptap/react'
import React, { useEffect } from 'react'
import { GlobalContext } from '../../contexts/globalContext'
import Tag from '../Bubbles/Tag'
import ColorsInput from '../ColorsInput'
import { getSelectedText } from '../Tiptap/utils'
import './index.scss'

/**
 * StyleList Configuration
 *
 * Defines all available styling categories and their corresponding CSS classes.
 * This configuration drives the UI options and maps user selections to CSS classes
 * that will be applied to the text in the Tiptap editor.
 *
 * Structure:
 * - category: The style category name (used for UI grouping)
 * - styles: Array of available styles within that category
 *   - label: Human-readable name shown in UI
 *   - class: CSS class name to be applied to the text
 */
const StyleList = [
  {
    category: 'Highlight Color',
    styles: [
      { label: 'Highlight none', class: 'bnone' },
      { label: 'Transparent', class: 'bgTransparent' },
      { label: 'Primary 1', class: 'bp1' },
      { label: 'Primary 2', class: 'bp2' },
      { label: 'Secondary 1', class: 'bs1' },
      { label: 'Secondary 2', class: 'bs2' },
      { label: 'Secondary 3', class: 'bs3' },
      { label: 'Neutral 1', class: 'bn1' },
      { label: 'Neutral 2', class: 'bn2' },
      { label: 'Neutral 3', class: 'bn3' },
      { label: 'Neutral 4', class: 'bn4' },
      { label: 'Neutral 5', class: 'bn5' },
      { label: 'Neutral 6', class: 'bn6' },
    ],
  },
  {
    category: 'Font Color',
    styles: [
      { label: 'Primary 1', class: 'cp1' },
      { label: 'Primary 2', class: 'cp2' },
      { label: 'Primary 3', class: 'cp3' },
      { label: 'Primary 4', class: 'cp4' },
      { label: 'Primary 5', class: 'cp5' },
      { label: 'Secondary 1', class: 'cs1' },
      { label: 'Secondary 2', class: 'cs2' },
      { label: 'Secondary 3', class: 'cs3' },
      { label: 'Neutral 1', class: 'cn1' },
      { label: 'Neutral 2', class: 'cn2' },
      { label: 'Neutral 3', class: 'cn3' },
      { label: 'Neutral 4', class: 'cn4' },
      { label: 'Neutral 5', class: 'cn5' },
      { label: 'Neutral 6', class: 'cn6' },
      { label: 'Neutral 7', class: 'cn7' },
      { label: 'Neutral 8', class: 'cn8' },
    ],
  },
  {
    category: 'Font Sizes',
    styles: [
      { label: 'Font min', class: 'fs2' },
      { label: 'Font small', class: 'fs3' },
      { label: 'Font medium', class: 'fs4' },
      { label: 'Font large', class: 'fs5' },
      { label: 'Font extra large', class: 'fs6' },
      { label: 'Font max', class: 'fs7' },
    ],
  },
  {
    category: 'Font Family',
    styles: [
      { label: 'Serif', class: 'fSerif' },
      { label: 'Sans serif Bold', class: 'fSansBld' },
      { label: 'Sans serif Regular', class: 'fSansReg' },
      { label: 'Sans serif Medium', class: 'fSansMed' },
      { label: 'Sans serif', class: 'fSans' },
    ],
  },
  // {
  //   category: 'Background Color',
  //   styles: [
  //     { label: 'Background none', class: 'bgNone' },
  //     { label: 'Transparent', class: 'bgTransparent' },
  //     { label: 'Primary 1', class: 'bp1' },
  //     { label: 'Primary 2', class: 'bp2' },
  //     { label: 'Secondary 1', class: 'bs1' },
  //     { label: 'Secondary 2', class: 'bs2' },
  //     { label: 'Secondary 3', class: 'bs3' },
  //     { label: 'Secondary 4', class: 'bs4' },
  //     { label: 'Secondary 5', class: 'bs5' },
  //     { label: 'Neutral 1', class: 'bn1' },
  //     { label: 'Neutral 2', class: 'bn2' },
  //     { label: 'Neutral 3', class: 'bn3' },
  //     { label: 'Neutral 4', class: 'bn4' },
  //     { label: 'Neutral 5', class: 'bn5' },
  //     { label: 'Neutral 6', class: 'bn6' },
  //     { label: 'Neutral 7', class: 'bn7' },
  //   ],
  // },
  {
    category: 'Gradients',
    styles: [
      { label: 'Gradient s1 s3 diagonal', class: 'bs1s3d' },
      { label: 'Gradient s1 s3 linear', class: 'bs1s3h' },
      { label: 'Gradient o1 s2 diagonal', class: 'bgo1n1d' },
      { label: 'Text gradient s1 s3 diagonal', class: 'cs1s3d' },
      { label: 'Text gradient s1 s3 linear', class: 'cs1s3h' },
    ],
  },
  // {
  //   category: 'Borders',
  //   styles: [
  //     { label: 'Border', class: 'bdr solid' },
  //     { label: 'Border thick', class: 'bdr thick' },
  //     { label: 'Border dashed', class: 'bdr dashed' },
  //     { label: 'Border dashed', class: 'bdr transparent' },
  //     { label: 'Border translucent', class: 'bdr translucent' },
  //   ],
  // },
  // {
  //   category: 'Rounded',
  //   styles: [
  //     { label: 'Rounded min', class: 'rounded min' },
  //     { label: 'Rounded small', class: 'rounded s' },
  //     { label: 'Rounded medium', class: 'rounded m' },
  //     { label: 'Rounded large', class: 'rounded l' },
  //     { label: 'Rounded extra large', class: 'rounded xl' },
  //     { label: 'Rounded max', class: 'rounded max' },
  //   ],
  // },
  {
    category: 'Heading tags',
    styles: [
      { label: 'Heading 1', class: 'h1' },
      { label: 'Heading 2', class: 'h2' },
      { label: 'Heading 3', class: 'h3' },
      { label: 'Heading 4', class: 'h4' },
      { label: 'Heading 5', class: 'h5' },
      { label: 'Heading 6', class: 'h6' },
      { label: 'Subheading', class: 'subheading' },
    ],
  },
  // {
  //   category: 'Shadow',
  //   styles: [
  //     { label: 'Shadow 1', class: 'shadow shadow1' },
  //     { label: 'Shadow 2', class: 'shadow shadow2' },
  //     { label: 'Shadow 3', class: 'shadow shadow3' },
  //   ],
  // },
  // {
  //   category: 'Others',
  //   styles: [
  //     { label: 'Visibility hidden', class: 'hidden' },
  //     { label: 'Display none', class: 'dNone' },
  //   ],
  // },
]

/**
 * Customiser Component - Main Function
 *
 * Provides a legacy styling interface for the Tiptap editor with basic functionality.
 * Manages style selection, color picking, and application of styles to selected text.
 *
 * State Management:
 * - activeCategory: Currently selected style category (Highlight, Color, etc.)
 * - selectedStyles: Object mapping categories to selected style classes
 * - Global context for modal state and color management
 *
 * @returns JSX.Element - The customiser interface
 */
const Customiser = () => {
  // Track which style category is currently active, defaults to first category
  const [activeCategory, setActiveCategory] = React.useState(
    StyleList[0].category
  )

  // Store selected styles as key-value pairs (category -> CSS class)
  const [selectedStyles, setSelectedStyles] = React.useState<{
    [key: string]: string
  }>({})

  // Get current Tiptap editor instance for applying styles
  const { editor } = useCurrentEditor()

  // Global context for modal state management
  const { isStylingModalOpen, setIsStylingModalOpen } =
    React.useContext(GlobalContext)

  // Global context for color state management
  const { activeColor, setActiveColor } = React.useContext(GlobalContext)

  /**
   * Effect: Initialize Component State from Editor
   *
   * Runs once on component mount to sync the component state with any
   * existing styles in the editor. This ensures the UI reflects the
   * current state of the selected text.
   *
   * Process:
   * 1. Extract CSS classes from altusText attribute
   * 2. Map classes back to style categories
   * 3. Initialize selectedStyles and activeColor states
   */
  useEffect(() => {
    // Get CSS classes applied to selected text, fallback to empty array
    const textAttr = editor?.getAttributes('altusText')?.class?.split(' ') || []

    // Get text style attributes (for font color)
    const textStyleAttr = editor?.getAttributes('textStyle')

    // Get highlight attributes (for background color)
    const textHighlightAttr = editor?.getAttributes('highlight')

    // Map CSS classes back to their style categories
    const obj = textAttr.reduce((acc, element) => {
      const matchedStyle = StyleList.find((styleElement) =>
        styleElement.styles.some((style) => style.class === element)
      )

      if (matchedStyle) {
        console.warn(matchedStyle.category)
        acc[matchedStyle.category] = element
      }

      return acc
    }, {})

    // Initialize component state with found styles
    setSelectedStyles(obj || {}) // Ensure setSelectedStyles receives an object

    // Initialize color state from editor attributes
    setActiveColor({
      highlightColor: textHighlightAttr?.color || '',
      fontColor: textStyleAttr?.color || '',
    })
  }, []) // Run only once on mount

  /**
   * Handle Style Selection Change
   *
   * Updates the selectedStyles state when a user selects a different style
   * within a category. This doesn't immediately apply the style to the editor,
   * but stores the selection for later application.
   *
   * @param category - The style category being changed (e.g., 'Highlight', 'Color')
   * @param item - The selected style item with label and CSS class
   */
  function handleStyleChange(
    category: string,
    item: { label: string; class: string }
  ) {
    setSelectedStyles((prev) => ({
      ...prev,
      [category]: item.class,
    }))
  }

  /**
   * Remove Style by Class Name
   *
   * Removes a specific style from the selectedStyles state by finding
   * and deleting the entry that matches the given className.
   *
   * @param className - The CSS class name to remove from selected styles
   */
  function removeStyle(className: string) {
    setSelectedStyles((prev) => {
      const updatedSelectedStyles = { ...prev }
      // Iterate over the entries and remove the style with the specified className
      for (const [category, selectedStyle] of Object.entries(
        updatedSelectedStyles
      )) {
        if (selectedStyle === className) {
          delete updatedSelectedStyles[category]
          break // Stop after the first match is found
        }
      }
      return updatedSelectedStyles
    })
  }

  function handleApply() {
    const styleClasses = Object.values(selectedStyles).join(' ')

    if (
      activeColor.highlightColor !== null &&
      activeColor.highlightColor !== ''
    ) {
      editor?.commands.setHighlight({ color: activeColor.highlightColor })
    } else {
      editor?.commands.unsetHighlight()
    }

    if (activeColor.fontColor !== 'black') {
      editor?.commands.setColor(activeColor.fontColor)
    } else {
      editor?.commands.unsetColor()
    }

    if (Object.keys(selectedStyles).length !== 0) {
      editor?.chain().focus().setAltusText({ class: styleClasses }).run()
    } else {
      editor?.chain().focus().unsetAltusText().run()
    }

    if (activeCategory === 'Bg Color') {
      if (styleClasses.length > 0) {
        editor?.chain().focus().setAltusDiv({ class: styleClasses }).run()
      } else {
        editor?.chain().focus().unsetAltusDiv().run()
      }
    }

    setIsStylingModalOpen(false)
  }

  function handleColorClick({
    name,
    value,
  }: {
    name: string
    value: string | number
  }) {
    if (name === 'Highlight Color') {
      setActiveColor((prev) => {
        return {
          ...prev,
          highlightColor: value.toString(),
        }
      })
    } else {
      setActiveColor((prev) => {
        return {
          ...prev,
          fontColor: value.toString(),
        }
      })
    }
  }

  function clearAll() {
    setSelectedStyles({})
    setActiveColor({
      highlightColor: null,
      fontColor: 'black',
    })
  }

  const SubCategory = (
    <div className={'subCategoryRoot'}>
      <h6 className={'categoryHeading'}>Sub category</h6>
      <div className={'subCategoryInner'}>
        {StyleList.find((item) => item.category === activeCategory)?.styles.map(
          (item, index) => (
            <Radio
              key={index}
              isChecked={selectedStyles[activeCategory] === item.class}
              onClick={() => handleStyleChange(activeCategory, item)}
            >
              {item.label}
            </Radio>
          )
        )}
      </div>
    </div>
  )

  return (
    <div className={'customiserRoot'}>
      <div className={'customiserInnerRoot'}>
        <aside>
          <div className={'asideTopDiv'}>
            {/*<div className={'searchRoot'}>*/}
            {/*  <TextInput*/}
            {/*    icon={<SearchIcon />}*/}
            {/*    size='small'*/}
            {/*    placeholder='Search here...'*/}
            {/*  />*/}
            {/*</div>*/}

            <div className={'stylingRoot'}>
              {/* <ButtonGroup variant='spaced' spacing='spacingM'>
              <IconButton
                variant='secondary'
                size='small'
                aria-label='Transition'
                icon={<Icon as={PiStarLight} />}
              />
              <IconButton
                variant='secondary'
                size='small'
                aria-label='Animation'
                icon={<Icon as={PiShootingStarDuotone} />}
              />
            </ButtonGroup> */}
              <Button
                size='medium'
                variant='secondary'
                onClick={() => clearAll()}
              >
                Clear All
              </Button>
            </div>
          </div>

          <hr />

          <div className={'listRoot'}>
            <div className={'categoryRoot'}>
              <h6 className={'categoryHeading'}>Category</h6>
              <ul>
                {StyleList.map((item, index) => {
                  return (
                    <li
                      key={index}
                      onClick={() => setActiveCategory(item.category)}
                      className={`${activeCategory === item.category ? 'active' : ''} pointer`}
                    >
                      {item.category}
                    </li>
                  )
                })}
              </ul>
            </div>

            {/* <div className={'resizer'}></div> */}
            <hr />

            {activeCategory === 'Highlight Color' ||
              activeCategory === 'Font Color' ? (
              <ColorsInput
                name={activeCategory}
                onClick={handleColorClick}
                activeColor={
                  activeCategory === 'Font Color'
                    ? activeColor?.fontColor
                    : activeColor?.highlightColor
                }
              />
            ) : (
              SubCategory
            )}

            {/* <div className={'tagsRoot'}>
            {
              classList.map((item, index) => {
                return <Tag key={index} variant={'secondary'} isIcon={true} size={'small'} text={item} onRemove={
                  () => removeStyle(item)
                } />
              })
            }
          </div> */}
            <div className={'tagsRoot'}>
              {Object.entries(selectedStyles).map(
                ([category, selectedStyle]) => (
                  <Tag
                    key={category}
                    variant={'secondary'}
                    isIcon={true}
                    size={'small'}
                    text={selectedStyle}
                    onRemove={() => removeStyle(selectedStyle)}
                  />
                )
              )}
            </div>
          </div>
        </aside>

        <main>
          <div className={'previewRoot'}>
            <span
              className={Object.values(selectedStyles).join(' ')}
              style={{
                color: activeColor.fontColor,
                backgroundColor: activeColor.highlightColor || 'transparent',
              }}
            >
              {getSelectedText(editor) || 'Please select text'}
            </span>
          </div>

          <footer>
            <div className={'footerInner'}>
              <ButtonGroup>
                {/* <IconButton
                onClick={() => setIsStylingModalOpen(false)}
                size={'large'}
                variant='transparent'
                aria-label='cancel'
                icon={<CloseIcon />}
              /> */}
                <IconButton
                  onClick={handleApply}
                  size={'large'}
                  variant='transparent'
                  aria-label='cancel'
                  icon={<CheckCircleIcon />}
                />
              </ButtonGroup>
            </div>
          </footer>
        </main>

        <div></div>
      </div>
    </div>
  )
}

export default Customiser
