import { Datepicker } from '@contentful/f36-components'
import moment from 'moment'
import React, { useState } from 'react'

// Define the payload interface
interface DateRangePayload {
  id: string
  name: string
  additionalDetails?: string
}

// Define the component props
interface DateRangeSelectorProps {
  payload: DateRangePayload
  onDateRangeSelected: (
    startDate: Date | undefined,
    endDate: Date | undefined,
    payload: DateRangePayload
  ) => void
}

const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  payload,
  onDateRangeSelected,
}) => {
  const [startDate, setStartDate] = useState<Date | undefined>(undefined)
  const [endDate, setEndDate] = useState<Date | undefined>(undefined)

  const handleStartDateChange = (date: Date | undefined) => {
    setStartDate(date)
    if (endDate && date && endDate?.isBefore(date)) {
      setEndDate(undefined)
      onDateRangeSelected(date, undefined, payload)
    } else {
      onDateRangeSelected(date, endDate, payload)
    }
  }

  const handleEndDateChange = (date: Date | undefined) => {
    setEndDate(date)
    onDateRangeSelected(startDate, date, payload)
  }
  const startDateObj = startDate?.toObject()
  const selectedStartDate = startDateObj
    ? new Date(startDateObj.years, startDateObj.months, startDateObj.date)
    : undefined
  const endDateObj = endDate?.toObject()
  const selectedEndDate = endDateObj
    ? new Date(endDateObj.years, endDateObj.months, endDateObj.date)
    : undefined
  // console.log(startDate, endDate, selectedEndDate , selectedStartDate)

  return (
    <div className='flex gap-5'>
      <Datepicker
        labelText='Start Date'
        selected={selectedStartDate}
        // value={startDate}
        onSelect={(day) => {
          const momentDay = day ? moment(day) : undefined
          handleStartDateChange(momentDay)
        }}
        fromDate={new Date()}
        // onChange={handleStartDateChange}
        // minDate={today}
        placeholderText='Select a start date'
      />
      <Datepicker
        labelText='End Date'
        selected={selectedEndDate}
        onSelect={(day) => {
          const momentDay = day ? moment(day) : undefined
          handleEndDateChange(momentDay)
        }}
        // onChange={handleEndDateChange}
        // minDate={selectedStartDate }
        // value={endDate}
        fromDate={selectedStartDate}
        placeholderText='Select an end date'
        disabled={!selectedStartDate}
      />
    </div>
  )
}

export default DateRangeSelector
