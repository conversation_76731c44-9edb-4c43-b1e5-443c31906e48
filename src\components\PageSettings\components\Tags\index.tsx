import { EditorAppSDK } from '@contentful/app-sdk'
import {
  Box,
  Button,
  Checkbox,
  Notification,
  Text,
} from '@contentful/f36-components'
import _ from 'lodash'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import {
  setIsPageSettingsModalClosable,
  updateTags,
} from '../../../../redux/slices/pageSettings'
import { RootState, useAppDispatch } from '../../../../redux/store'

interface Props {
  domain: string
  sdk: EditorAppSDK
  locale: string[]
}

function Tags(props: Props) {
  const { sdk } = props

  const template = sdk.entry.fields['template'].getForLocale('en-CA').getValue()

  const dispatch = useAppDispatch()

  // globally hidden tags
  const tagCatToExcludeGlobally = [
    'DTS',
    'Events',
    'Featured',
    'Our experts',
    'Asset Type',
  ]

  // globally hidden tags by category
  const tagIdToExcludeGlobally = ['AFS: Map']

  // required tags for PR (add more if required)
  const ReqPRCats = ['Afs', 'Press release']

  // tags categories to exclude for PR (add more if required)
  const PRCatsExclude = ['Insights']

  // required tags for Insights (add more if required)
  const InsightsCatsExclude = ['Press release']

  const GenericCatsExclude = ['Press release']

  // tags categories to exclude for Insights (add more if required)
  const ReqInsightsCats = [
    'Afs',
    'Insights',
    'Country',
    'Expertise',
    'Region',
    'Solution',
  ]

  // required tags for all (add more if required)
  const ReqGlobalCats = ['Domain', 'Internal', 'Year']

  const [categorizedTags, setCategorizedTags] = useState<any>({})

  const [activeTagCategory, setActiveTagCategory] = useState('')

  const pageTags = sdk.entry.getMetadata()?.tags

  const [countByCategory, setCountByCategory] = useState<any>({})

  const [summaryTags, setSummaryTags] = useState<any>({})

  const [selectedTags, setSelectedTags] = useState<string[]>([])

  const [oldTags, setOldTags] = useState<string[]>([])

  const tags = useSelector((state: RootState) => state.pageData.categorizedTags)

  const tagsUpdateLoading = useSelector(
    (state: RootState) => state.pageData.tagsUpdateLoading
  )

  const nonCategorizedTags = useSelector(
    (state: RootState) => state.pageData.nonCategorizedTags
  )

  // func to handle tag cat select change and update local state of selected tags and update the category count
  const handleTagSelect = (x: string) => {
    let oldTags = [...selectedTags]

    if (selectedTags.includes(x))
      oldTags = oldTags.filter((tag: string) => tag !== x)
    else oldTags = [...oldTags, x]

    setSelectedTags(oldTags)

    setCountByCategory(countCategories(oldTags))
  }

  const sortTagsObj = (x: any[]) => _.sortBy(x, 'name') // sort tags alphabetically

  const isTagsUpdated = () => _.isEqual(oldTags, selectedTags) // check if tags are updated

  // on change of selected tags, check if required tags are added by page template
  const checkRequiredTagCategories = () => {
    let reqCats = [...ReqGlobalCats]

    if (template === 'Press Release') {
      // due to the name difference of same PR tag and category, we need to remove the press release tag and add the press tag
      reqCats = [...reqCats, ...ReqPRCats].filter(
        (cat) => cat !== 'Press release'
      )

      reqCats = [...reqCats, 'Press']
    } else if (template === 'Insight Article') {
      reqCats = [...reqCats, ...ReqInsightsCats]
    } else {
      reqCats = [...reqCats]
    }

    // get the categories from added tags
    const keysFromAddedTags = countByCategory
      ? Object.keys(countByCategory)
      : []

    // get the missing categories
    const missingCats = reqCats.filter(
      (cat) => !keysFromAddedTags.includes(cat)
    )

    // set the closable flag so that without adding the required tags, page settings wizard can not be closed.
    dispatch(setIsPageSettingsModalClosable(missingCats.length === 0))

    return missingCats
  }

  // func to generate summary of selected tags by their categories
  const getSummaryObj = (arr: any[]) => {
    return arr?.reduce((acc, item) => {
      // Extract the category from the name field before the first colon
      const category = item?.name?.split(':')?.[0]?.trim()

      // If the category doesn't exist in the accumulator, create it
      if (!acc[category]) {
        acc[category] = []
      }

      // Push the name into the corresponding category array
      acc[category].push(item.name)

      return acc
    }, {})
  }

  // func to add tags to entry
  const handleTagAddToEntry = async () => {
    await dispatch(
      updateTags({ newTags: selectedTags, entryId: sdk?.entry?.getSys()?.id })
    )
    setOldTags(selectedTags)

    Notification.success('Tags updated successfully')
  }

  // func to count the number of tags in each category
  const countCategories = (data: string[]) => {
    const categoryCount: any = {}
    if (data.length === 0) return

    data.forEach((item) => {
      const category = item.match(/[a-z]+|[A-Z][a-z]*/g)[0]
      const capitalizedCategory =
        category?.charAt(0)?.toUpperCase() + category?.slice(1)

      if (categoryCount[capitalizedCategory]) {
        categoryCount[capitalizedCategory]++
      } else {
        categoryCount[capitalizedCategory] = 1
      }
    })

    return categoryCount
  }

  // func to get tags from entry and update the local state
  const handleTagFromEntry = (tags: any) => {
    const x: any = tags?.map((tag: any) => tag?.sys?.id)
    setCountByCategory(countCategories(x))

    setSelectedTags(x)
    setOldTags(x)
  }

  // func to check if add tag button is disabled
  const checkIsTagAddDisabled = () => {
    const flag =
      tagsUpdateLoading ||
      isTagsUpdated() ||
      checkRequiredTagCategories().length > 0

    return flag
  }

  useEffect(() => {
    // get tags from entry and update the local state
    pageTags && handleTagFromEntry(pageTags)
  }, [pageTags])

  useEffect(() => {
    // on tags update of entry refetch the newly updated tags and store them in local state
    sdk.entry.onMetadataChanged(() => {
      const tags = sdk.entry.getMetadata()?.tags
      handleTagFromEntry(tags)
    })
  }, [sdk])

  useEffect(() => {
    // categorize and sort the tags by key and select the first category as active by page template as default
    if (Object.keys(tags).length > 0) {
      const sortedTags = _.fromPairs(_.sortBy(_.toPairs(tags), 0)) // sort by key
      setCategorizedTags(sortedTags)

      let firstCat = ''

      if (template === 'Press Release' || template === 'Insight Article')
        firstCat = Object.keys(sortedTags)?.[0]
      else firstCat = Object.keys(sortedTags)?.[1]

      firstCat && setActiveTagCategory(firstCat)
    }
  }, [tags])

  useEffect(() => {
    // get summary of selected tags
    const x = nonCategorizedTags?.filter((tag: any) =>
      selectedTags?.includes(tag?.sys?.id)
    )
    setSummaryTags(_.fromPairs(_.sortBy(_.toPairs(getSummaryObj(x)), 0)))
  }, [selectedTags, nonCategorizedTags])

  return (
    <Box className={'flex w-full h-full'}>
      <Box className='bg-[#F7F9FA] w-[350px] h-full'>
        <Box className=''>
          <Box className={'overflow-y-auto'}>
            <Box className='flex flex-col'>
              {Object.keys(categorizedTags).map((item) => {
                if (tagCatToExcludeGlobally.includes(item)) return null

                if (template === 'Generic' && GenericCatsExclude.includes(item))
                  return null

                if (
                  template === 'Press Release' &&
                  PRCatsExclude.includes(item)
                )
                  return null

                if (
                  template === 'Insight Article' &&
                  InsightsCatsExclude.includes(item)
                )
                  return null

                let catCount =
                  countByCategory?.[_.upperFirst(item?.toLowerCase())]

                if (item === 'Press release') {
                  catCount = countByCategory?.['Press']
                }

                return (
                  <Text
                    className={`${
                      activeTagCategory === item ? 'bg-[#cfd9e080]' : ''
                    } cursor-pointer font-medium hover:bg-[#cfd9e080] py-2 pl-2 pr-3 flex items-center justify-between`}
                    onClick={() => setActiveTagCategory(item)}
                    key={item}
                  >
                    <p className='flex items-start gap-1.5'>{item}</p>
                    {catCount && (
                      <p className='bg-[#667082] text-[#FFFFFF] size-5 text-xs flex justify-center items-center rounded-full'>
                        {catCount}
                      </p>
                    )}
                  </Text>
                )
              })}
            </Box>
          </Box>
        </Box>
      </Box>

      <main className={'w-full h-full '}>
        <div
          className={
            'h-full w-full flex justify-start items-start px-8 py-4 flex-col'
          }
        >
          <p className='font-semibold text-base'>
            {TagCatToolTips[activeTagCategory]}
          </p>
          <Box className='flex w-full h-full flex-col gap-3 py-4 overflow-y-auto'>
            {categorizedTags?.[activeTagCategory] &&
              sortTagsObj(categorizedTags?.[activeTagCategory])?.map(
                (item: any) => {
                  if (tagIdToExcludeGlobally.includes(item?.name)) return null
                  return (
                    <Checkbox
                      isChecked={selectedTags.includes(item?.sys?.id)}
                      onChange={() => handleTagSelect(item?.sys?.id)}
                      className='py-2'
                      key={item.name}
                    >
                      {item.name}
                    </Checkbox>
                  )
                }
              )}
          </Box>
        </div>
      </main>
      <Box className='bg-[#F7F9FA] w-[430px] h-full flex flex-col'>
        <Box className='flex-grow overflow-y-auto p-3'>
          <h6 className='mb-0'>Summary</h6>
          <p className='mb-4'>
            These are the selected tags that will be added to this page.
          </p>
          {checkRequiredTagCategories().length > 0 && (
            <Box className='pb-4'>
              {checkRequiredTagCategories().map((tag: string) => {
                console.log('tag: ', tag);
                if (tag === 'Afs') {
                  tag = 'AFS'
                }
                if (tag === 'Press') {
                  tag = 'Press release'
                }

                return (
                  <Box
                    key={tag}
                    className='flex flex-col justify-start items-start'
                  >
                    <h6 className='mb-0'>{tag}</h6>
                    <p>You haven’t added any tags for the {tag}. </p>
                    <p className='text-[#E2222B]'>
                      These tags are required for proper functioning.
                    </p>
                  </Box>
                )
              })}
            </Box>
          )}
          <Box className='flex flex-col items-start justify-start gap-4'>
            {Object.keys(summaryTags).map((item) => {
              if (item === 'Afs') item = 'AFS'
              return (
                <Box
                  key={item}
                  className='flex flex-col justify-start items-start'
                >
                  <h6 className='mb-0'>{item}</h6>
                  {summaryTags?.[item]
                    ?.sort()
                    ?.map((tag: any, index: number) => {
                      return <p key={index}>{tag}</p>
                    })}
                </Box>
              )
            })}
          </Box>
        </Box>
        <Box className='p-3'>
          <Button
            className='w-full'
            variant='primary'
            isLoading={tagsUpdateLoading}
            isDisabled={checkIsTagAddDisabled()}
            onClick={handleTagAddToEntry}
          >
            Add Tags
          </Button>
        </Box>
      </Box>
    </Box>
  )
}

export default Tags

const TagCatToolTips: any = {
  AFS: 'Select the relevant AFS tag to indicate with listing this page will be displayed on.',
  City: 'Select the relevant city tag associated with this page.',
  Country: 'Select the relevant country tag associated with this page.',
  Domain:
    'Select the relevant domain tag for the website that this page will be displayed on.',
  Expertise: 'Select the relevant expertise tags associated with this page.',
  Insights:
    'Select the relevant insight type tag associated with this insight page.',
  Internal: 'Select the relevant internal tag associated with this page.',
  'Press release':
    'Select the relevant press release type tag associated with this press release page.',
  Region:
    'Select the relevant region tag associated with the page intended target audience.',
  Solution:
    'Select the relevant solution tags associated with this content of this page.',
  Year: "Select the year tag to specify the page's publishing year.",
}
