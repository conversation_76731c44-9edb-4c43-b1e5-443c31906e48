import { Form } from 'antd'
import React, { useState } from 'react'
import { FaPlus } from 'react-icons/fa6'
import { GrClose } from 'react-icons/gr'
import { useSelector } from 'react-redux'
import { ENV_VARIABLES } from '../../../../../constant/variables'
import {
  createOrUpdateAsset,
  SaveConfigurationandData,
} from '../../../../../globals/utils'
import {
  addHostedFile,
  isFileIdExists,
  isSourceExists,
} from '../../../../../redux/slices/dashboard/hostedFiles'
import { RootState, useAppDispatch } from '../../../../../redux/store'
import { Button, Dragger, Input, Modal, notification } from '../../../../atoms'

const AddHostedFileModal = ({
  setIsButtonDisabled,
}: {
  setIsButtonDisabled: Function
}) => {
  const state = useSelector((state: RootState) => state.hostedFiles)
  const dispatch = useAppDispatch()
  const [isVisible, setIsVisible] = React.useState<boolean>(false)
  const [form] = Form.useForm()
  const [isUploading, setIsUploading] = useState(false)
  const handleUpload = async (values: any) => {
    if (!state?.id) {
      return
    }
    const { source, file, name, fileId } = values
    const exitstdoc = isSourceExists(state, source)
    if (exitstdoc) {
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            {`Source "${source}" already exists.`}
          </p>
        ),
      })
      return
    }
    if (fileId) {
      const existsFileId = isFileIdExists(state, fileId)
      if (existsFileId) {
        notification.error({
          message: (
            <p
              className={'fSansBld'}
              style={{ lineHeight: 1, marginBottom: '15px' }}
            >
              {`File ID "${fileId}" already exists.`}
            </p>
          ),
        })
        return
      }
    }
    const selectedFile = file?.[0]?.originFileObj
    if (!selectedFile) {
      // message.error('Please upload a file. 111')
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Please upload a file.
          </p>
        ),
      })
      return
    }

    try {
      setIsUploading(true)

      const data = await createOrUpdateAsset(
        selectedFile,
        'en-CA',
        undefined,
        'This file was uploaded via hosted files. DO NOT modify this file. It will break the system.'
      )
      const fileUrl = data?.fields?.file?.['en-CA']?.url?.replace(
        `//${ENV_VARIABLES.assetdomain}/${ENV_VARIABLES.contentfulSpaceID}/${data?.sys?.id}`,
        ''
      )
      const extension = fileUrl
        ? fileUrl?.split('.')?.pop()?.split('?')[0] || ''
        : ''
      const payload = {
        id: `${new Date()?.toISOString()?.replace(/[^a-zA-Z0-9_-]/g, '')}`,
        source: source,
        name,
        assetId: data?.sys?.id,
        destination: fileUrl ? `${fileUrl}` : '',
        createdAt: new Date()?.toISOString(),
        fileId: fileId || '',
        extension,
      }
      await SaveConfigurationandData(
        [payload, ...state.dataSource],
        state?.id,
        true
      )
      await dispatch(addHostedFile(payload))
      // await onAssetUploaded(per, source) // Close modal after successful upload
      setIsVisible(false)
      setIsButtonDisabled(false)
      form.resetFields()
      notification.success({
        message: (
          <>
            <p className={'fSansBld'} style={{ lineHeight: ' 13px' }}>
              The file is Uploaded.
            </p>

            <code
              style={{
                lineHeight: 1,
                marginBottom: '15px',
                display: 'inline-block',
              }}
              className={'fs1 cn2'}
            >
              {payload?.name}
            </code>
          </>
        ),
        description: 'Do not forget to push your changes to live.',
      })
    } catch (error) {
      console.error('Error uploading document:', error)
      notification.error({
        message: (
          <p
            className={'fSansBld'}
            style={{ lineHeight: 1, marginBottom: '15px' }}
          >
            Failed to upload document. Please try again.
          </p>
        ),
      })
    } finally {
      setIsUploading(false)
    }
  }
  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e
    }
    return e?.fileList
  }

  return (
    <>
      <Button
        type='primary'
        onClick={() => {
          setIsVisible(true)
        }}
      >
        <FaPlus /> Host new file
      </Button>
      <Modal
        title='Host new file'
        open={isVisible}
        onCancel={() => {
          setIsVisible(false)
          form.resetFields()
        }}
        centered
        footer={null}
      >
        <Form form={form} layout='vertical' onFinish={handleUpload}>
          <Form.Item
            name='fileId'
            label='File ID'
            className={'inputField'}
            rules={[
              {
                required: false,
                message: 'file ID is optional',
              },
              {
                pattern: /^[a-zA-Z0-9-_.]+$/,
                message:
                  'File ID can only contain letters, numbers, _ , .  and -',
              },
            ]}
          >
            <Input placeholder='File ID' />
          </Form.Item>
          <Form.Item
            name='name'
            label='Internal name'
            className={'inputField'}
            rules={[
              {
                required: true,
                message: 'Internal name is required',
              },
            ]}
          >
            <Input placeholder='Internal Name' />
          </Form.Item>
          <Form.Item
            name='source'
            label='Live URL'
            className={'inputField'}
            rules={[
              {
                required: true,
                message: 'Live URL is required',
              },
              // {
              //   pattern: /^\/(?!$|home$).+/,
              //   message:
              //     "Invalid File URL. It must start with '/' and cannot be '/', 'home', or '/home'.",
              // },
              // {
              //   pattern: /^[^.\s]+$/, // Ensures no file extensions
              //   message: "URL cannot contain a file extension.",
              // },
              {
                validator: (_, value) => {
                  if (!value.startsWith('/')) {
                    return Promise.reject(new Error("URL must start with '/'."))
                  }
                  if (value === '/' || value === '/home') {
                    return Promise.reject(
                      new Error("URL cannot be '/' or '/home'.")
                    )
                  }
                  if (!/^[a-zA-Z0-9/-]+$/.test(value.slice(1))) {
                    return Promise.reject(
                      new Error(
                        "URL can only contain letters, numbers, '-' and '/'"
                      )
                    )
                  }
                  if (/[.\s]/.test(value)) {
                    return Promise.reject(
                      new Error(
                        'URL cannot contain a file extension or spaces.'
                      )
                    )
                  }
                  return Promise.resolve()
                },
              },
            ]}
          >
            <Input placeholder='Enter the URL where the file should be available' />
          </Form.Item>

          {/* <Form.Item
          name="file"
          label="Upload Document"
          valuePropName="fileList"
          getValueFromEvent={(e: any) => (Array.isArray(e) ? e : e && e.fileList)}
        //   rules={[{ required: true, message: 'Please upload a file.' }]}
        >
          <Upload
            beforeUpload={() => false} // Prevent auto-upload
            multiple={false}
            accept=".pdf,.doc,.docx" // Adjust file types as needed
          >
            <Button icon={<UploadOutlined />}>Select File</Button>
          </Upload>
        </Form.Item>
        <Form.Item label="Dragger"> */}
          <Form.Item
            name='file'
            id='file'
            valuePropName='fileList'
            getValueFromEvent={normFile}
            className={'inputField'}
            // noStyle
            required
            rules={[
              {
                required: true,
                message: 'Please attach a file',
              },
            ]}
          >
            <Dragger
              name='files'
              beforeUpload={() => false}
              multiple={false}
              maxCount={1}
              accept='.pdf,.csv,.xlsx,.xls,.rtf'
              itemRender={(originNode, file, fileList, actions) => (
                <div className='justify-between items-center flex mt-2'>
                  <span>{file.name}</span>
                  <GrClose
                    size={'20'}
                    style={{ cursor: 'pointer' }}
                    onClick={() => actions.remove()}
                  />
                </div>
              )}
            >
              <p className='ant-upload-text'>
                Click or drag and drop the file here.
              </p>
              {/* <p className='ant-upload-hint'>Support for a single upload.</p> */}
            </Dragger>
          </Form.Item>
          {/* </Form.Item> */}
          <Form.Item className='flex justify-center inputField'>
            <Button
              type='primary'
              htmlType='submit'
              loading={isUploading}
              // block
              className='bg-neutral-950'
            >
              {isUploading ? 'Uploading...' : 'Upload file'}
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default AddHostedFileModal
