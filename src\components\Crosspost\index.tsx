/* eslint-disable react-hooks/exhaustive-deps */
import { EditorAppSDK } from '@contentful/app-sdk'
import { Box, Button, Modal, Notification } from '@contentful/f36-components'
import { EntityProvider } from '@contentful/field-editor-reference'
import React, { useEffect, useState } from 'react'
import { GlobalContext } from '../../contexts/globalContext'
import {
  CreateConfigurationEntry,
  UpdateConfigurationEntry,
} from '../../globals/config-helpers'
import { getConfigurationsCollectionQuery } from '../../globals/queries'
import { updateCurrentEntry } from '../../globals/utils'
import { fetchCrossPostData } from '../../redux/slices/pageSettings'
import { useAppDispatch } from '../../redux/store'
import NextButton from '../Buttons/NextButton'
import PrevButton from '../Buttons/PrevButton'
import SaveButton from '../Buttons/SaveButton'
import ProgressBar from '../ProgressBar'
import SourceAndSlug from './components/SourceAndSlug'
import SummaryUrl from './components/SummaryUrl'

import { ENV_VARIABLES } from '../../constant/variables'
import {
  checkEachKeyValue,
  fetchGraphQLQuery,
  filterEmptyObjects,
  mergeUniqueTags,
  renameKeys,
} from './helpers'
import './index.scss'
import { Domains, getDomainFullName, getDomainShortName } from './utils'

interface CrossPostingModalProps {
  onClose: (value: unknown) => void
  sdk: EditorAppSDK
  entryId: string
}

interface DomainLocale {
  [key: string]: string[]
}

function CrossPostingModal(props: CrossPostingModalProps) {
  const { sdk, entryId } = props

  const [isDataFetching, setIsDataFetching] = useState(false)

  const dispatch = useAppDispatch()

  const pageTags = sdk.entry.getMetadata()?.tags // page tags for already added tags reference

  const pageConfigurations = sdk.entry.fields['configurations'].getValue()


  const envId = ENV_VARIABLES.contentfulEnvironment

  const spaceId = ENV_VARIABLES.contentfulSpaceID

  const pageTemplate = sdk.entry.fields['template'].getValue()

  const [activeStep, setActiveStep] = useState(0)

  const [isSaving, setIsSaving] = useState(false)

  const [contentId, setContentId] = useState('')

  const [globalJson, setGlobalJson] = useState<any>({})

  const [domainLocale, setDomainLocale] = useState<DomainLocale>({})

  const [isCrosspostedSuccessfully, setIsCrosspostedSuccessfully] =
    useState(false)

  const [checkboxState, setCheckboxState] = useState({
    agl: false,
    fia: false,
    reo: false,
    ver: false,
    o11: false,
  })

  const [originalDomain, setOriginalDomain] = useState<Domains>('' as Domains)

  const [slugAgainstDomains, setSlugAgainstDomains] = useState<any>({})

  const { isCrossPostingModalOpen, setIsCrossPostingModalOpen } =
    React.useContext(GlobalContext)

/**
 * Closes the cross-posting modal and resets relevant states.
 * 
 * This function performs the following actions:
 * 1. Sets the cross-posting modal state to closed.
 * 2. Dispatches an action to fetch cross-post data with the current entry ID, environment ID, and space ID.
 * 3. Resets the state indicating successful cross-posting to false.
 */

  const handleClose = () => {
    setIsCrossPostingModalOpen(false)
    dispatch(fetchCrossPostData({ id: entryId, envId, spaceId }))
    setIsCrosspostedSuccessfully(false)
  }

  /**
   * This function takes a Domains enum as an argument and sets the slugAgainstDomains state with the
   * current entry ID as the key, and the domain name as the sub-key. The value of this key is another
   * object with the slug and primaryCTA as key-value pairs.
   *
   * @param {Domains} originalDomain - The domain for which to set the slug and primaryCTA.
   */
  const setSourceDomainSlugs = (originalDomain: Domains) => {
    const map = domainLocale?.[originalDomain]
      ?.map((item) => {
        return {
          [item.split('-')[0]]: sdk.entry.fields['slug']
            .getForLocale(item)
            .getValue(),
        }
      })
      ?.filter((item) => {
        return item[Object.keys(item)[0]] !== undefined
      })

    if (!map) return

    let reducedMap = map.reduce((acc, item) => {
      return {
        ...acc,
        ...item,
      }
    })

    const primaryCTA = sdk.entry.fields['primaryCta'].getValue()?.sys?.id

    let originalDomainSlug = {
      ...slugAgainstDomains,
      [entryId]: {
        [getDomainFullName(originalDomain)]: {
          slug: reducedMap,
          primaryCTA,
        },
      },
    }

    setSlugAgainstDomains(originalDomainSlug)
  }

  /**
   * Resets the slugAgainstDomains state for the current entryId,
   * sets the new source domain, and updates the checkboxState.
   *
   * @param {Domains} x - The new source domain.
   */
  const resetSlugAgainstDomainsAndSetNewSourceDomain = (x: Domains) => {
    let oldSlugAgainstDomains = { ...slugAgainstDomains }
    oldSlugAgainstDomains[entryId] = {}
    setSlugAgainstDomains(oldSlugAgainstDomains)
    setSourceDomainSlugs(x)
    setOriginalDomain(x)

    let oldCheckboxState = { ...checkboxState }

    Object.keys(oldCheckboxState).forEach((key) => {
      oldCheckboxState[key as keyof typeof checkboxState] = false
    })

    setCheckboxState({ ...oldCheckboxState, [x]: true })
  }

  /**
   * Fetches all the allowed languages for each domain by filtering the configurations collection for Locale content type,
   * and then parsing the JSON data to extract the allowed languages. The result is an object with the domain name as the key,
   * and an array of allowed languages as the value.
   * @param {any} configurationsCollection - The array of configuration objects from the Contentful API.
   * @returns {Promise<any>} - The object with the domain name as the key and an array of allowed languages as the value.
   */
  async function fetchAllMsaLanguageDataFromMsaConfig(
    configurationsCollection: any
  ) {
    // filter all the content type MSA

    const filteredObj = configurationsCollection.filter((item: any) => {
      return item?.type === 'Locale'
    })

    let final: any = []

    for (let i = 0; i < filteredObj.length; i++) {
      const obj = filteredObj[i]
      const domainName = obj.internalName
        .split('-')[1]
        .trim()
        .split(' ')[0]
        .toLowerCase()

      // fetch each id's data

      const x = obj.data.json?.content?.[0]?.content?.[0]?.value

      const parsedData = JSON.parse(x || '{}')

      // get all the allowed languages
      const allowedLanguages = parsedData?.allowedLanguages || ['en-CA']

      final.push({
        [domainName]: allowedLanguages,
      })
    }

    // reduce all the allowed languages against their domain
    final = final.reduce((acc: any, item: any) => {
      return {
        ...acc,
        ...item,
      }
    }, {})

    setDomainLocale(final)

    return final
  }

  /**
   * Fetches the Crosspost configuration from the Contentful API, and then:
   * 1. Fetches all the allowed languages for each domain by filtering the configurations collection for Locale content type,
   *    and then parsing the JSON data to extract the allowed languages. The result is an object with the domain name as the key,
   *    and an array of allowed languages as the value.
   * 2. Parses the JSON data of the Crosspost configuration to extract the current entry's data against the entry id, and sets
   *    the original domain, slug against domains, and crossposted domain state.
   * @returns {Promise<void>}
   */
  async function fetchData() {
    setIsDataFetching(true)

    const configurationsCollection = await fetchGraphQLQuery(
      getConfigurationsCollectionQuery(),
      spaceId,
      envId
    ).then((res: any) => res?.data?.configurationsCollection?.items)

    // fetch all domain lang data from their msa config to show instead of having static from codebase
    await fetchAllMsaLanguageDataFromMsaConfig(configurationsCollection)

    // fetch the crosspost configuration
    const matchedObj = configurationsCollection?.find((item: any) => {
      return item.type === 'Crosspost' && item?.scope === 'MSA'
    })

    if (matchedObj) {
      const matchedData =
        matchedObj?.data?.json?.content?.[0]?.content?.[0]?.value

      setContentId(matchedObj?.sys?.id) // matched configuration content id to update data on save
      if (matchedData) {
        const res = JSON.parse(matchedData)

        setGlobalJson(res)

        const currEntrySlug = res?.[entryId] // get current entry data against entry id

        if (currEntrySlug) {
          setSlugAgainstDomains(res)
          setOriginalDomain(
            getDomainShortName(currEntrySlug?.originalDomain) as Domains
          )

          // set the crossposted domain state
          const newCheckboxState = currEntrySlug?.allowedDomains
            ?.map((key: Domains) => {
              return {
                [key]: true,
              }
            })
            .reduce((a: any, b: any) => ({ ...a, ...b }), {})

          setIsDataFetching(false)
          setCheckboxState(newCheckboxState)
          return
        }
      }
    }

    setIsDataFetching(false)
    // setSourceDomainSlugs(domainLocales)
  }

  /**
   * Handles the checkbox state change for a given domain.
   * Updates the checkbox state by flipping the boolean value of the domain key.
   * @param {Domains} domain - The domain for which the checkbox state needs to be updated.
   */
  const handleCheckBoxStateChange = (domain: Domains) => {
    // update the checkbox state
    setCheckboxState({
      ...checkboxState,
      [domain]: !checkboxState[domain as keyof typeof checkboxState],
    })
  }


  /**
   * Handles the slug change for a given domain and locale.
   * Updates the slugAgainstDomains state by updating the slug for the given domain and locale.
   * @param {string} value - The new slug value.
   * @param {Domains} domain - The domain for which the slug needs to be updated.
   * @param {string} locale - The locale for which the slug needs to be updated.
   */
  const handleSlugChangeByDomainAndLocale = (
    value: string,
    domain: Domains,
    locale: string
  ) => {
    let domainFullName = getDomainFullName(domain)

    let newDomainSlugObj: any = { ...globalJson }

    newDomainSlugObj[entryId] = {
      ...slugAgainstDomains?.[entryId],
      [domainFullName]: {
        ...slugAgainstDomains?.[entryId]?.[domainFullName],
        slug: {
          ...slugAgainstDomains?.[entryId]?.[domainFullName]?.slug,
          [locale]: value,
        },
      },
    }

    setSlugAgainstDomains(newDomainSlugObj)
  }

  /**
   * Handles the primary CTA change for a given domain.
   * Updates the slugAgainstDomains state by updating the primary CTA for the given domain.
   * @param {string} domain - The domain for which the primary CTA needs to be updated.
   * @param {string} id - The ID of the new primary CTA.
   */
  const handlePrimaryCTAChange = (domain: string, id: string) => {
    let newDomainSlugObj: any = { ...globalJson }

    newDomainSlugObj[entryId] = {
      ...slugAgainstDomains?.[entryId],
      [domain]: {
        ...slugAgainstDomains?.[entryId]?.[domain],
        primaryCTA: id,
      },
    }

    setSlugAgainstDomains(newDomainSlugObj)
  }

/**
 * Removes a specific field value for a given domain from the slugAgainstDomains state.
 * Sets the field's value to null within the specified domain for the current entry.
 * 
 * @param {string} domain - The domain from which the field value should be removed.
 * @param {string} field - The field whose value needs to be set to null.
 */

  const handleRemoveByField = (domain: string, field: string) => {
    let newDomainSlugObj: any = { ...globalJson }

    newDomainSlugObj[entryId] = {
      ...slugAgainstDomains?.[entryId],
      [domain]: {
        ...slugAgainstDomains?.[entryId]?.[domain],
        [field]: null,
      },
    }

    setSlugAgainstDomains(newDomainSlugObj)
  }

  /**
   * Handles the save functionality for crossposting.
   * Updates the tags in the page entry and updates the crosspost configuration entry.
   * If the entry doesn't exist, it creates a new one.
   * After saving, it updates the page configuration field and saves the page.
   * Finally, it dispatches the action to fetch the crosspost data again.
   */
  const handleSave = async () => {
    setIsSaving(true)

    let slugsObj = slugAgainstDomains

    // get the selected domain names and convert to array of string
    const allowedDomains = Object.keys(checkboxState).filter((key) => {
      return checkboxState[key as keyof typeof checkboxState]
    })

    // add the original domain name and allowed domains to payload
    slugsObj[entryId] = {
      ...slugsObj[entryId],
      originalDomain: getDomainFullName(originalDomain),
      allowedDomains,
    }

    let urls = { ...globalJson }

    // update the slugs against each domain and clean the empty objects
    urls[entryId] = filterEmptyObjects(slugsObj[entryId])

    // update the entry if exists or create a new one
    let originalDomainsSlugFromObj =
      slugsObj[entryId]?.[slugsObj[entryId]?.originalDomain]?.slug

    originalDomainsSlugFromObj = renameKeys(
      originalDomainsSlugFromObj,
      originalDomain
    )

    // get the tags from the selected domains
    const tags = getSelectedDomainNames().map((key: string) => {
      return {
        sys: {
          type: 'Link',
          linkType: 'Tag',
          id: getDomainFullName(key as Domains),
        },
      }
    })

    // get the tags from the entry
    const otherTagsFromCustom =
      pageTags
        ?.map((pageTag) => {
          return {
            sys: {
              type: 'Link',
              linkType: 'Tag',
              id: pageTag.sys.id,
            },
          }
        })
        .filter((tag: any) => {
          return !tag?.sys?.id?.includes('domain')
        }) || []

    // finally merge the tags
    let newTagsToBeAddedToEntryCustom = mergeUniqueTags(
      otherTagsFromCustom,
      tags
    )

    // update the page entry to add the tags
    await updateCurrentEntry({
      entryId,
      tags: newTagsToBeAddedToEntryCustom,
      slugs: originalDomainsSlugFromObj,
      envId,
      spaceId,
    })

    const primaryCTAByDomains: { [key: string]: string } = {}
    for (const domain in urls[entryId]) {
      if (urls[entryId][domain].primaryCTA) {
        primaryCTAByDomains[domain] = urls[entryId][domain].primaryCTA
      }
    }

    if (contentId) {
      // if entry exists, then update the data else create a new crosspost configuration

      await UpdateConfigurationEntry({ contentId, data: urls, envId, spaceId })
    } else {
      const res = await CreateConfigurationEntry({
        data: urls,
        type: 'Crosspost',
        scope: 'MSA',
        internalName: `Crosspost - MSA`,
        envId,
        spaceId,
      })

      setContentId(res)
    }

    // save the required data to page configuration field, which will be used on frontend
    const newPageConfig = {
      ...pageConfigurations,
      isCrossPosted: true,
      crossPostOriginalDomain: originalDomain,
      crossPostedDomains: allowedDomains,
      primaryCTAByDomains,
    }

    sdk.entry.fields['configurations'].setValue(newPageConfig)

    // save the data and move the next steps as preview links step

    sdk.entry.save()

    setIsSaving(false)

    dispatch(fetchCrossPostData({ id: entryId, envId, spaceId }))

    setIsCrosspostedSuccessfully(true)

    Notification.success(
      'Congratulations! Your page has been successfully crossposted across the universe.'
    )
    setTimeout(() => {
      Notification.info('Wizard will auto close in 2 seconds.')
    }, 2000)

    setTimeout(() => {
      handleClose()
    }, 5000)

    // setActiveStep(2)
  }

  /**
   * Check if any domain is enabled, but the any slug is not added
   * before showing the summary.
   *
   * @returns {boolean} true if any domain is enabled but slug is not added
   */
  const checkBeforeSummary = () => {
    // func to check before summary if any domain is enabled, but the any slug is not added
    const checkedDomains = Object.keys(checkboxState).filter((key) => {
      return checkboxState[key as keyof typeof checkboxState]
    })

    const isAnyCheckBoxTick = checkedDomains.length > 0

    const currentIdSlugs = slugAgainstDomains[entryId]

    const selectedCheckBoxSlugAdded = checkedDomains.map((domain) => {
      return !checkEachKeyValue(
        currentIdSlugs?.[getDomainFullName(domain as Domains)]
      )
    })

    const isAnyCheckBoxSlugAdded = selectedCheckBoxSlugAdded.some(
      (item) => item
    )

    return !isAnyCheckBoxTick || !isAnyCheckBoxSlugAdded
  }

/**
 * Retrieves and returns a sorted array of domain names that have been selected.
 *
 * @returns {string[]} An array of selected domain names, sorted alphabetically.
 */

  const getSelectedDomainNames = () => {
    // func to get the selected domain names and sort them
    return Object.keys(checkboxState)
      .filter((key) => {
        return checkboxState[key as keyof typeof checkboxState]
      })
      .sort()
  }

  /**
   * Generates a summary payload from the current state of the
   * crossposting form.
   *
   * The summary payload contains the original domain name and
   * allowed domains, as well as the slugs against each domain.
   * Empty objects are filtered out of the payload.
   *
   * @returns {object} The summary payload.
   */
  const generateSummaryPayload = () => {
    let slugsObj = slugAgainstDomains

    // get the selected domain names and convert to array of string
    const allowedDomains = Object.keys(checkboxState).filter((key) => {
      return checkboxState[key as keyof typeof checkboxState]
    })

    // add the original domain name and allowed domains to payload
    slugsObj[entryId] = {
      ...slugsObj[entryId],
      originalDomain: getDomainFullName(originalDomain),
      allowedDomains,
    }

    let urls = { ...globalJson }

    // update the slugs against each domain and clean the empty objects
    urls[entryId] = filterEmptyObjects(slugsObj[entryId])

    return urls[entryId]
  }

  // fetch data on mount
  useEffect(() => {
    fetchData()
  }, [])

  return (
    <EntityProvider sdk={sdk}>
      <Modal
        onClose={() => handleClose()}
        isShown={isCrossPostingModalOpen}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        className='crossPostingModal'
      >
        {() => (
          <>
            <Modal.Header title='' onClose={() => handleClose()}>
              <Box>
                <h2 className='m-0 p-0 font-sans font-semibold text-gray-900 text-base leading-6 tracking-normal overflow-hidden text-ellipsis whitespace-nowrap max-w-full'>
                  Multisite Crossposting
                </h2>
                <Box>
                  <p className='flex justify-start items-center gap-1 w-full'>
                    <span className='font-semibold normal-case text-black'>
                      Title:{' '}
                    </span>
                    <span>{sdk.entry.fields['seoTitle'].getValue()}</span>
                  </p>
                </Box>
              </Box>
            </Modal.Header>

            <Box style={{ width: '100%' }}>
              <ProgressBar completedSteps={activeStep} totalSteps={2} />
            </Box>
            <Modal.Content className='flex flex-col items-center justify-between w-full h-full overflow-y-hidden p-0'>
              <Box className='w-full h-[90%] '>
                {activeStep === 0 && (
                  <SourceAndSlug
                    checkboxState={checkboxState}
                    handleCheckBoxStateChange={handleCheckBoxStateChange}
                    isDataFetching={isDataFetching}
                    originalDomain={originalDomain}
                    setOriginalDomain={setOriginalDomain}
                    setSourceDomainSlugs={setSourceDomainSlugs}
                    domainLocale={domainLocale}
                    getSelectedDomainNames={getSelectedDomainNames}
                    slugAgainstDomains={slugAgainstDomains?.[entryId]}
                    handleSlugChangeByDomainAndLocale={
                      handleSlugChangeByDomainAndLocale
                    }
                    resetSlugAgainstDomainsAndSetNewSourceDomain={
                      resetSlugAgainstDomainsAndSetNewSourceDomain
                    }
                    entryId={entryId}
                    handlePrimaryCTAChange={handlePrimaryCTAChange}
                    handleRemoveByField={handleRemoveByField}
                    isInsightsArticle={pageTemplate === 'Insight Article'}
                    sdk={sdk}
                  />
                )}
                {activeStep === 1 && (
                  <SummaryUrl
                    summaryProp={generateSummaryPayload()}
                    isSummaryView={!isCrosspostedSuccessfully}
                  />
                )}
                {/* {activeStep === 2 && (
                  <PreviewUrl
                    checkboxState={checkboxState}
                    slugAgainstDomains={slugAgainstDomains}
                    entryId={entryId}
                    getSelectedDomainNames={getSelectedDomainNames}
                  />
                )} */}
                <Modal.Controls className='px-5 py-3'>
                  <>
                    {isDataFetching && (
                      <Button isLoading size='small'>
                        Loading
                      </Button>
                    )}
                    {activeStep === 0 ? (
                      <NextButton
                        onClick={() => setActiveStep(1)}
                        isDisabled={checkBeforeSummary() || isDataFetching}
                      />
                    ) : activeStep === 1 ? (
                      <>
                        <PrevButton onClick={() => setActiveStep(0)} />
                        <SaveButton onClick={handleSave} isLoading={isSaving} />
                      </>
                    ) : (
                      <>
                        {/* <PrevButton onClick={() => setActiveStep(1)} />
                        <SaveButton onClick={handleClose} helpText='Done' /> */}
                      </>
                    )}
                  </>
                </Modal.Controls>
              </Box>
            </Modal.Content>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default CrossPostingModal
