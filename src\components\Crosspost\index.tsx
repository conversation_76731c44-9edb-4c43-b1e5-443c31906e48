/* eslint-disable react-hooks/exhaustive-deps */
import { EditorAppSDK } from '@contentful/app-sdk'
import {
  Box,
  Button,
  FormControl,
  Modal,
  Select,
  Switch,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import { EntityProvider } from '@contentful/field-editor-reference'
import React, { useEffect, useState } from 'react'
import Info from '../../assets/icons/Info'
import { GlobalContext } from '../../contexts/globalContext'
import { getConfigurationsCollectionQuery } from '../../globals/queries'
import {
  createContentEntry,
  fetchGraphQL,
  getEntryDataById,
  updateCurrentEntry,
  updateEntryData,
} from '../../globals/utils'
import { fetchCrossPostData } from '../../redux/slices/pageSettings'
import { useAppDispatch } from '../../redux/store'
import NextButton from '../Buttons/NextButton'
import PrevButton from '../Buttons/PrevButton'
import SaveButton from '../Buttons/SaveButton'
import ProgressBar from '../ProgressBar'
import FormControlComp from '../Shared/FormControlComp'
import TextDivider from '../Shared/TextDivider'
import './index.scss'
import {
  checkEachKeyValue,
  domains,
  Domains,
  domainsConfig,
  filterEmptyObjects,
  getDomainFullName,
  getDomainPreview,
  getDomainShortName,
  getDomainWebsiteName,
  getLocaleFullName,
  mergeUniqueTags,
  renameKeys,
} from './utils'

interface CrossPostingModalProps {
  onClose: (value: unknown) => void
  sdk: EditorAppSDK
  entryId: string
}

interface DomainLocale {
  [key: string]: string[]
}

function CrossPostingModal(props: CrossPostingModalProps) {
  const { sdk, entryId } = props

  const [isDataFetching, setIsDataFetching] = useState(false)

  const dispatch = useAppDispatch()

  const pageTags = sdk.entry.getMetadata()?.tags // page tags for already added tags reference

  const pageConfigurations = sdk.entry.fields['configurations'].getValue()

  const [activeStep, setActiveStep] = useState(0)

  const [isSaving, setIsSaving] = useState(false)

  const [contentId, setContentId] = useState('')

  const [globalJson, setGlobalJson] = useState<any>({})

  const [domainLocale, setDomainLocale] = useState<DomainLocale>({})

  const [checkboxState, setCheckboxState] = useState({
    agl: false,
    fia: false,
    reo: false,
    ver: false,
    o11: false,
  })

  const [originalDomain, setOriginalDomain] = useState<Domains>('' as Domains)

  const [slugAgainstDomains, setSlugAgainstDomains] = useState<any>({})

  const { isCrossPostingModalOpen, setIsCrossPostingModalOpen } =
    React.useContext(GlobalContext)

  const handleClose = () => setIsCrossPostingModalOpen(false)

  const setSourceDomainSlugs = (
    domainLocales: DomainLocale,
    originalDomain: Domains
  ) => {
    const map = domainLocales?.[originalDomain]
      ?.map((item) => {
        return {
          [item.split('-')[0]]: sdk.entry.fields['slug']
            .getForLocale(item)
            .getValue(),
        }
      })
      ?.filter((item) => {
        return item[Object.keys(item)[0]] !== undefined
      })

    if (!map) return

    let originalDomainSlug = {
      [entryId]: {
        [getDomainFullName(originalDomain)]: map.reduce((acc, item) => {
          return {
            ...acc,
            ...item,
          }
        }),
      },
    }

    setSlugAgainstDomains(originalDomainSlug)
  }

  async function fetchAllMsaLanguageDataFromMsaConfig(
    configurationsCollection: any
  ) {
    // filter all the content type MSA

    const filteredObj = configurationsCollection.filter((item: any) => {
      return item.type === 'MSA'
    })

    let final: any = []

    for (let i = 0; i < filteredObj.length; i++) {
      const obj = filteredObj[i]
      const domainName = obj.internalName
        .split('-')[1]
        .trim()
        .split(' ')[0]
        .toLowerCase()

      // fetch each id's data

      const x = obj.data.json?.content?.[0]?.content?.[0]?.value

      const parsedData = JSON.parse(x || '{}')

      // get all the allowed languages
      const allowedLanguages = parsedData?.allowedLanguages || ['en-CA']

      final.push({
        [domainName]: allowedLanguages,
      })
    }

    // reduce all the allowed languages against their domain
    final = final.reduce((acc: any, item: any) => {
      return {
        ...acc,
        ...item,
      }
    }, {})

    setDomainLocale(final)

    return final
  }

  async function fetchData() {
    setIsDataFetching(true)

    const configurationsCollection = await fetchGraphQL(
      getConfigurationsCollectionQuery()
    ).then((res: any) => res?.data?.configurationsCollection?.items)

    // fetch all domain lang data from their msa config to show instead of having static from codebase
    await fetchAllMsaLanguageDataFromMsaConfig(configurationsCollection)

    // fetch the crosspost configuration
    const matchedObj = configurationsCollection?.find((item: any) => {
      return item.type === 'CrossPost'
    })

    const matchedData =
      matchedObj?.data?.json?.content?.[0]?.content?.[0]?.value

    if (matchedData) {
      setContentId(matchedObj?.sys?.id) // matched configuration content id to update data on save

      const res = JSON.parse(matchedData)

      setGlobalJson(res)

      const currEntrySlug = res?.[entryId] // get current entry data against entry id

      if (currEntrySlug) {
        setSlugAgainstDomains(res)
        setOriginalDomain(
          getDomainShortName(currEntrySlug?.originalDomain) as Domains
        )

        // set the crossposted domain state
        const newCheckboxState = currEntrySlug?.allowedDomains
          ?.map((key: Domains) => {
            return {
              [key]: true,
            }
          })
          .reduce((a: any, b: any) => ({ ...a, ...b }), {})

        setIsDataFetching(false)
        setCheckboxState(newCheckboxState)
        return
      }
    }

    setIsDataFetching(false)
    // setSourceDomainSlugs(domainLocales)
  }

  const handleCheckBoxStateChange = (domain: Domains) => {
    // update the checkbox state
    setCheckboxState({
      ...checkboxState,
      [domain]: !checkboxState[domain],
    })
  }

  // func to handle the slug change against each domain against each locale
  const handleSlugChangeByDomainAndLocale = (
    value: string,
    domain: Domains,
    locale: string
  ) => {
    let domainFullName = getDomainFullName(domain)

    let newDomainSlugObj: any = { ...globalJson }

    newDomainSlugObj[entryId] = {
      ...slugAgainstDomains?.[entryId],
      [domainFullName]: {
        ...slugAgainstDomains?.[entryId]?.[domainFullName],
        [locale]: value,
      },
    }

    setSlugAgainstDomains(newDomainSlugObj)
  }

  // func to save the crossposted data
  const handleSave = async () => {
    setIsSaving(true)

    let slugsObj = slugAgainstDomains

    // get the selected domain names and convert to array of string
    const allowedDomains = Object.keys(checkboxState).filter((key) => {
      return checkboxState[key as Domains]
    })

    // add the original domain name and allowed domains to payload
    slugsObj[entryId] = {
      ...slugsObj[entryId],
      originalDomain: getDomainFullName(originalDomain),
      allowedDomains,
    }

    let urls = { ...globalJson }

    // update the slugs against each domain and clean the empty objects
    urls[entryId] = filterEmptyObjects(slugsObj[entryId])

    // update the entry if exists or create a new one
    let originalDomainsSlugFromObj =
      slugsObj[entryId]?.[slugsObj[entryId]?.originalDomain]

    originalDomainsSlugFromObj = renameKeys(
      originalDomainsSlugFromObj,
      originalDomain
    )

    // get the tags from the selected domains
    const tags = getSelectedDomainNames().map((key: string) => {
      return {
        sys: {
          type: 'Link',
          linkType: 'Tag',
          id: getDomainFullName(key as Domains),
        },
      }
    })

    // get the tags from the entry
    const otherTagsFromCustom =
      pageTags
        ?.map((pageTag) => {
          return {
            sys: {
              type: 'Link',
              linkType: 'Tag',
              id: pageTag.sys.id,
            },
          }
        })
        .filter((tag: any) => {
          return !tag?.sys?.id?.includes('domain')
        }) || []

    // finally merge the tags
    let newTagsToBeAddedToEntryCustom = mergeUniqueTags(
      otherTagsFromCustom,
      tags
    )

    // update the page entry to add the tags
    await updateCurrentEntry(
      entryId,
      newTagsToBeAddedToEntryCustom,
      originalDomainsSlugFromObj
    )

    if (contentId) {
      // if entry exists, then update the data else create a new crosspost configuration
      const response = await getEntryDataById(contentId).then((res: any) => {
        return res.fields
      })

      let updatedPayload = {
        ...response,
        data: {
          'en-CA': {
            content: [
              {
                content: [
                  {
                    data: {},
                    marks: [],
                    nodeType: 'text',
                    value: JSON.stringify(urls),
                  },
                ],
                data: {},
                nodeType: 'paragraph',
              },
            ],
            data: {},
            nodeType: 'document',
          },
        },
      }

      await updateEntryData(contentId, updatedPayload)
    } else {
      const fields = {
        internalName: {
          'en-CA': 'Configuration Page CrossPost',
        },
        type: {
          'en-CA': 'CrossPost',
        },
        data: {
          'en-CA': {
            content: [
              {
                content: [
                  {
                    data: {},
                    marks: [],
                    nodeType: 'text',
                    value: JSON.stringify(urls),
                  },
                ],
                data: {},
                nodeType: 'paragraph',
              },
            ],
            data: {},
            nodeType: 'document',
          },
        },
      }

      const res: any = await createContentEntry('configurations', fields)
      const newContentId = res?.sys?.id || ''
      setContentId(newContentId)
    }

    // save the required data to page configuration field, which will be used on frontend
    const newPageConfig = {
      ...pageConfigurations,
      isCrossPosted: true,
      crossPostOriginalDomain: originalDomain,
      crossPostedDomains: allowedDomains,
    }

    sdk.entry.fields['configurations'].setValue(newPageConfig)

    // save the data and move the next steps as preview links step

    sdk.entry.save()

    setIsSaving(false)

    dispatch(fetchCrossPostData(entryId))

    setActiveStep(2)
  }

  const getDomainDataFromDomainConfig = (domain: string) => {
    // func to get the domain data from domain config
    return [
      domainsConfig.find(
        (item) => item.key === domain || item.domainKey === domain
      ),
    ]
  }

  const checkBeforeSummary = () => {
    // func to check before summary if any domain is enabled, but the any slug is not added
    const checkedDomains = Object.keys(checkboxState).filter((key) => {
      return checkboxState[key as Domains]
    })

    const isAnyCheckBoxTick = checkedDomains.length > 0

    const currentIdSlugs = slugAgainstDomains[entryId]

    const selectedCheckBoxSlugAdded = checkedDomains.map((domain) => {
      return !checkEachKeyValue(
        currentIdSlugs?.[getDomainFullName(domain as Domains)]
      )
    })

    const isAnyCheckBoxSlugAdded = selectedCheckBoxSlugAdded.some(
      (item) => item
    )

    return !isAnyCheckBoxTick || !isAnyCheckBoxSlugAdded
  }

  const getSelectedDomainNames = () => {
    // func to get the selected domain names and sort them
    return Object.keys(checkboxState)
      .filter((key) => {
        return checkboxState[key as Domains]
      })
      .sort()
  }

  // fetch data on mount
  useEffect(() => {
    fetchData()
  }, [])

  return (
    <EntityProvider sdk={sdk}>
      <Modal
        onClose={() => handleClose()}
        isShown={isCrossPostingModalOpen}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        className='crossPostingModal'
      >
        {() => (
          <>
            <Modal.Header title='' onClose={() => handleClose()}>
              <Box>
                <h2 className='m-0 p-0 font-sans font-semibold text-gray-900 text-base leading-6 tracking-normal overflow-hidden text-ellipsis whitespace-nowrap max-w-full'>
                  Multisite Crossposting
                </h2>
                <Box>
                  <p className='flex justify-start items-center gap-1 w-full'>
                    <span className='font-semibold normal-case text-black'>
                      Title:{' '}
                    </span>
                    <span>{sdk.entry.fields['title'].getValue()}</span>
                  </p>
                </Box>
              </Box>
            </Modal.Header>

            <Box style={{ width: '100%' }}>
              <ProgressBar completedSteps={activeStep} totalSteps={3} />
            </Box>
            <Modal.Content className='flex flex-col items-center justify-between w-full h-full overflow-y-hidden p-0'>
              <Box className='w-full h-[90%] '>
                {activeStep === 0 && (
                  <Box className='flex items-start justify-start w-full h-full '>
                    <aside className='bg-[#F7F9FA] w-[350px] h-full'>
                      <h6 className={'pl-4 pt-2 mb-2'}>Websites</h6>
                      <ul>
                        {domainsConfig?.map((item, index) => {
                          return (
                            <li
                              key={index}
                              className={` cursor-pointer flex justify-start items-center p-0 hover:bg-[rgba(207,217,224,0.5)]`}
                            >
                              <Switch
                                isChecked={checkboxState[item.key as Domains]}
                                onChange={() =>
                                  handleCheckBoxStateChange(item.key as Domains)
                                }
                                className='py-2 pl-5'
                                isDisabled={isDataFetching}
                              >
                                {item.label}
                              </Switch>
                            </li>
                          )
                        })}
                      </ul>
                    </aside>

                    <main className='w-full px-5 overflow-y-auto h-full py-5'>
                      <div className='flex w-full flex-col items-start justify-start px-1'>
                        <FormControlComp
                          label='Source Website'
                          tooltip={`Source website specifies where the canonical URL should point to.`}
                        >
                          <Select
                            isDisabled={isDataFetching}
                            value={originalDomain}
                            onChange={(
                              event: React.ChangeEvent<HTMLSelectElement>
                            ) => {
                              const value = event.target.value as Domains
                              setOriginalDomain(value)
                              setSourceDomainSlugs(domainLocale, value)
                              if (
                                !checkboxState[
                                  value as keyof typeof checkboxState
                                ]
                              ) {
                                handleCheckBoxStateChange(value)
                              }
                            }}
                          >
                            <Select.Option value={''} isDisabled>
                              Select source website
                            </Select.Option>
                            {domainsConfig?.map((item) => (
                              <Select.Option key={item.key} value={item.key}>
                                {item.label}
                              </Select.Option>
                            ))}
                          </Select>
                        </FormControlComp>
                        {getSelectedDomainNames().map((selectedDomain) => {
                          return (
                            <>
                              <TextDivider
                                text={getDomainWebsiteName(
                                  getDomainFullName(selectedDomain as Domains)
                                )}
                              />
                              {getDomainDataFromDomainConfig(
                                selectedDomain
                              )?.map((domain: any) => {
                                const localeToShow = domainLocale?.[
                                  domain.key
                                ] || ['en-CA']

                                return (
                                  <React.Fragment key={domain.key}>
                                    <Box className='w-full pt-4 flex justify-start items-start gap-5 flex-col'>
                                      {localeToShow?.map((locale: string) => (
                                        <FormControl
                                          key={`${domain.key}-${locale}`}
                                          className='mb-0 w-full'
                                        >
                                          <div
                                            className='flex justify-start items-start gap-0.5 cursor-pointer mb-2.5 ml-0
'
                                          >
                                            <FormControl.Label className='mb-0'>
                                              <span className='flex gap-1 items-center'>
                                                <p className='text-black font-semibold'>
                                                  {getLocaleFullName(
                                                    locale.split('-')[0]
                                                  )}
                                                </p>
                                                <p className='text-[#667082] text-xs'>
                                                  ({locale.toUpperCase()})
                                                </p>
                                              </span>
                                            </FormControl.Label>
                                            <Tooltip
                                              placement='top'
                                              id='tooltip-2'
                                              content='Do not specify language settings, they will be applied automatically.'
                                            >
                                              <Info />
                                            </Tooltip>
                                          </div>
                                          <TextInput
                                            name={`${domain.key}-${
                                              locale.split('-')[0]
                                            }`}
                                            value={
                                              slugAgainstDomains?.[entryId]?.[
                                                domain.domainKey
                                              ]?.[locale.split('-')[0]]
                                            }
                                            onChange={(event) =>
                                              handleSlugChangeByDomainAndLocale(
                                                event.target.value,
                                                domain.key as Domains,
                                                locale.split('-')[0]
                                              )
                                            }
                                            testId={`${domain.key}-${
                                              locale.split('-')[0]
                                            }`}
                                          />
                                        </FormControl>
                                      ))}
                                    </Box>
                                  </React.Fragment>
                                )
                              })}
                            </>
                          )
                        })}
                      </div>
                    </main>
                  </Box>
                )}

                {activeStep === 1 && (
                  <div className='flex flex-col p-4 h-full'>
                    <h6 className='mt-0 mb-3'>Summary</h6>
                    <p className='text-[14.5px] pb-2.5'>
                      This Page will be crossposted to the undermentioned
                      websites.
                      <br /> If everything looks good, click Apply to save. You
                      can always go back to update / amend.
                    </p>

                    <div className='flex flex-col gap-3.5 pt-2.5'>
                      {domains?.map((domain, index) => {
                        if (
                          !checkboxState[getDomainShortName(domain) as Domains]
                        )
                          return null

                        const localeToShow = domainLocale?.[
                          getDomainShortName(domain)
                        ] || ['en-CA']

                        return (
                          <div className='flex flex-col'>
                            <div
                              className='flex gap-2.5 w-full justify-start items-start'
                              key={index}
                            >
                              <span className='flex justify-between items-center text-black w-[150px] font-medium text-base'>
                                <p>{getDomainWebsiteName(domain)}</p>
                              </span>
                              <div className='flex flex-col gap-3'>
                                {localeToShow?.map(
                                  (locale: string, slugIndex: number) => {
                                    return (
                                      <div
                                        key={slugIndex}
                                        className='flex justify-start items-start'
                                      >
                                        <span className='flex justify-between items-center font-medium pr-1 w-[105px]'>
                                          <p className='font-medium w-[120px] text-xs text-[#667082]'>
                                            {getLocaleFullName(
                                              locale.split('-')[0]
                                            )}{' '}
                                            ({locale})
                                          </p>{' '}
                                          <p> :</p>
                                        </span>
                                        <p className='text-sm text-[#1a1e27]'>
                                          {slugAgainstDomains[entryId]?.[
                                            domain
                                          ]?.[locale.split('-')[0]] || '404'}
                                        </p>
                                      </div>
                                    )
                                  }
                                )}
                              </div>
                            </div>
                            {index !== getSelectedDomainNames().length - 1 && (
                              <div className='h-px bg-[#cfd9e0] mt-3.5' />
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}
                {activeStep === 2 && (
                  <div className='flex flex-col p-[16px] pl-6 h-full w-full'>
                    <h6
                      style={{
                        marginTop: 0,
                        marginBottom: 10,
                      }}
                    >
                      Preview URLs
                    </h6>

                    <p className='text-[14.5px] pb-2.5'>
                      This Page have been successfully crossposted to the
                      undermentioned Preview websites with the following slugs .
                    </p>

                    <div className='flex flex-col gap-3.5 pt-2.5'>
                      {domains?.map((domain, index) => {
                        if (
                          !checkboxState[getDomainShortName(domain) as Domains]
                        )
                          return null
                        return (
                          <div className='flex flex-col' key={index}>
                            <div className='flex gap-2.5 w-full justify-start items-start'>
                              <span className='text-[15px] font-medium w-[110px] flex justify-between items-center text-black'>
                                <p>{getDomainWebsiteName(domain)}</p>
                              </span>
                              <div className='flex flex-col gap-2.5'>
                                {getDomainDataFromDomainConfig(
                                  getDomainShortName(domain)
                                )[0]?.locales?.map((locale, slugIndex) => {
                                  if (
                                    !slugAgainstDomains[entryId]?.[domain]?.[
                                      locale.split('-')[0]
                                    ]
                                  ) {
                                    return null
                                  }

                                  return (
                                    <div
                                      key={slugIndex}
                                      className='flex justify-start items-start'
                                    >
                                      <span className='font-medium w-[130px] flex justify-between items-center pr-1.5'>
                                        <p className='flex justify-start items-start'>
                                          {getLocaleFullName(
                                            locale.split('-')[0]
                                          )}{' '}
                                          ({locale})
                                        </p>{' '}
                                        <p> :</p>
                                      </span>

                                      <a
                                        className='text-[#1a1e27] underline text-sm'
                                        href={getDomainPreview(
                                          getDomainShortName(domain),
                                          slugAgainstDomains[entryId]?.[
                                            domain
                                          ]?.[locale.split('-')[0]],
                                          locale.split('-')[0]
                                        )}
                                        target='_blank'
                                        rel='noreferrer'
                                      >
                                        {slugAgainstDomains[entryId]?.[
                                          domain
                                        ]?.[locale.split('-')[0]] || '404'}
                                      </a>
                                    </div>
                                  )
                                })}
                              </div>
                            </div>
                            {index !== getSelectedDomainNames().length - 1 && (
                              <div className='h-px bg-[#cfd9e0] mt-3.5' />
                            )}
                          </div>
                        )
                      })}
                    </div>
                  </div>
                )}
                <Box style={{ width: '100%' }}>
                  <hr className='w-full mb-3' />
                  <Box
                    style={{
                      paddingRight: '1rem',
                    }}
                    className='w-full flex justify-end items-center gap-5'
                  >
                    {isDataFetching && (
                      <Button isLoading size='small'>
                        Loading
                      </Button>
                    )}
                    {activeStep === 0 ? (
                      <NextButton
                        onClick={() => setActiveStep(1)}
                        isDisabled={checkBeforeSummary() || isDataFetching}
                      />
                    ) : activeStep === 1 ? (
                      <>
                        <PrevButton onClick={() => setActiveStep(0)} />
                        <SaveButton onClick={handleSave} isLoading={isSaving} />
                      </>
                    ) : (
                      <>
                        <PrevButton onClick={() => setActiveStep(1)} />

                        <SaveButton onClick={handleClose} helpText='Done' />
                      </>
                    )}
                  </Box>
                </Box>
              </Box>
            </Modal.Content>
          </>
        )}
      </Modal>
    </EntityProvider>
  )
}

export default CrossPostingModal
