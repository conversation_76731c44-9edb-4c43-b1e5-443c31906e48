import React, { useMemo, useState } from 'react'
import { RiHistoryFill } from 'react-icons/ri'
import { sortValues } from '../../../../../../globals/utils'
import { Button, Modal, Table, Tooltip } from '../../../../../atoms'
import { EntryLink, pageExperimentationStatus } from '../../experiment-const'
import { ExperimentationCRUD } from '../../experimentation-helper'

const ViewHistory = ({
  data,
  masterPage,
  ActiveDomain,
}: {
  data: ExperimentRootData
  masterPage: string
  ActiveDomain: string
}) => {
  const [experimentDataview, setExperimentData] = useState<
    Partial<ExperimentationData>[] | null
  >(null)
  const handleClose = () => {
    setExperimentData(null)
  }
  const dataSource = useMemo(() => {
    if (
      experimentDataview &&
      Array.isArray(experimentDataview) &&
      experimentDataview?.length
    ) {
      return experimentDataview
        ?.map((el) => {
          const { experimentationPages = [], ...expData } = el
          return experimentationPages?.map((elp, index) => {
            // const pageStatusExpId =
            //   elp?.id &&
            //   expData?.shipedPageId &&
            //   elp?.id === expData?.shipedPageId &&
            //   expData?.isShiped
            //     ? '0'
            //     : expData?.isDeleted && expData?.isShiped
            //     ? '1'
            //     : expData?.isDeleted
            //     ? '2'
            //     : '3'
            const pageStatusExpId = expData?.isDeleted ? '2' : '3'
            return {
              ...elp,
              expData,
              pageStatusExpId,
              variantNo: index + (expData?.historyCount ?? 0),
              PageStatusOfExperiment: `${pageExperimentationStatus?.[pageStatusExpId]}`,
            }
          })
        })
        ?.flat(2)
    }
    return []
  }, [experimentDataview])

  const dataofExp = ExperimentationCRUD.getAll(data, {
    masterPage: masterPage?.toLowerCase(),
  })
  return (
    <>
      <Modal
        open={experimentDataview !== null}
        onCancel={handleClose}
        onClose={handleClose}
        footer={null}
        centered
        title={
          <h3 className='text-center flex justify-start gap-5 items-center'>
            Experiment History View
          </h3>
        }
        className='w-full '
      >
        <Table
          dataSource={dataSource}
          pagination={{
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            defaultPageSize: 10,
            showQuickJumper: true,
            showPrevNextJumpers: true,
          }}
          scroll={{ x: 'auto', y: 500 }}
          columns={[
            {
              title: 'Title',
              dataIndex: 'internalName',
              key: 'internalName',
              sorter: (a, b) => sortValues(a, b, 'title'),
              width: 150,
              fixed: 'left',
              render: (link: string, row) =>
                row?.id ? (
                  <a
                    href={`${EntryLink}/${row?.id}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-blue-400 flex justify-between items-center'
                  >
                    {`${row?.internalName ?? 'N/A'}`}
                  </a>
                ) : (
                  'N/A'
                ),
            },
            // {
            //   title: 'Slug',
            //   dataIndex: 'slug',
            //   key: 'slug',
            //   sorter: (a, b) => sortValues(a, b, 'slug'),
            //   width: 150,
            // },
            // {
            //   title: 'Internal Name',
            //   dataIndex: 'internalName',
            //   key: 'internalName',
            //   sorter: (a, b) => sortValues(a, b, 'internalName'),
            //   width: 250,
            // },

            // {
            //   title: 'Contentful Entry',
            //   dataIndex: 'link',
            //   key: 'link',
            //   render: (link) => (
            //     <a
            //       href={link}
            //       target='_blank'
            //       rel='noopener noreferrer'
            //       className='hover:text-blue-400'
            //     >
            //       View Entry
            //     </a>
            //   ),
            //   width: 300,
            // },
            // {
            //   title: 'Master Page',
            //   dataIndex: ['expData', 'masterPage'],
            //   key: 'masterPage',
            //   sorter: (a, b) =>
            //     a.expData.masterPage.localeCompare(b.expData.masterPage),
            //   render: (masterPage) => (
            //     <a
            //       href={masterPage}
            //       target='_blank'
            //       rel='noopener noreferrer'
            //       className='hover:text-blue-400'
            //     >
            //       {masterPage}
            //     </a>
            //   ),
            //   width: 300,
            // },
            // {
            //   title: 'Experiment Title',
            //   dataIndex: ['expData', 'experimentTitle'],
            //   key: 'experimentTitle',
            //   sorter: (a, b) =>
            //     a.expData.experimentTitle.localeCompare(
            //       b.expData.experimentTitle
            //     ),
            //   render: (experimentTitle) => (
            //     <span className='font-medium text-gray-800'>
            //       {experimentTitle || 'N/A'}
            //     </span>
            //   ),
            //   width: 300,
            // },
            // {
            //   title: 'Experiment Description',
            //   dataIndex: ['expData', 'experimentDescription'],
            //   key: 'experimentDescription',
            //   sorter: (a, b) =>
            //     a?.expData?.experimentDescription.localeCompare(
            //       b.expData.experimentDescription
            //     ),
            //   render: (experimentDescription) => (
            //     <span className='text-gray-600'>
            //       {experimentDescription || 'No description available'}
            //     </span>
            //   ),
            //   width: 300,
            // },
            // {
            //   title: 'Internal URL',
            //   dataIndex: 'link',
            //   key: 'link',
            //   render: (link: string, row) => {
            //     const isShippedPage =
            //       row?.expData?.shipedPageId &&
            //       row?.id === row?.expData?.shipedPageId
            //     const slug = row?.slug
            //     const fullUrl = `${ActiveDomain}${slug}`

            //     return isShippedPage ? (
            //       slug
            //     ) : (
            //       <a
            //         href={fullUrl}
            //         target='_blank'
            //         rel='noopener noreferrer'
            //         className='hover:text-blue-400 flex justify-between items-center'
            //       >
            //         {slug}
            //       </a>
            //     )
            //   },
            //   width: 200,
            // },
            {
              title: 'External URL',
              dataIndex: 'variantNo',
              key: 'variantNo',
              width: 200,
              render: (variantNo, row) => {
                const variantQuery = `${masterPage}?variant=${variantNo ?? 0}`
                const isShippedPage =
                  row?.expData?.shipedPageId &&
                  row?.id === row?.expData?.shipedPageId
                const isDeleted = row?.expData?.isDeleted

                if (isShippedPage || isDeleted) {
                  return variantQuery
                }

                return (
                  <a
                    href={`${ActiveDomain}${variantQuery}`}
                    target='_blank'
                    rel='noopener noreferrer'
                    className='hover:text-blue-400 flex justify-between items-center'
                  >
                    {variantQuery}
                  </a>
                )
              },
            },
            {
              title: 'Status',
              dataIndex: 'PageStatusOfExperiment',
              key: 'PageStatusOfExperiment',
              sorter: (a, b) => sortValues(a, b, 'PageStatusOfExperiment'),

              filters: [
                { text: 'Active', value: 'Active' },
                { text: 'Inactive', value: 'Inactive' },
                // { text: 'Winner', value: 'Winner' },
                // { text: 'Loser', value: 'Loser' },
              ],
              onFilter: (value, record) =>
                record?.PageStatusOfExperiment === value,
              width: 60,
            },
            {
              title: 'Start date',
              dataIndex: ['expData', 'date'],
              key: 'dateCreated',
              sorter: (a, b) => sortValues(a, b, ['expData', 'date']),
              // sorter: (a, b) =>
              //   new Date(a.expData.date) - new Date(b.expData.date),
              // sorter :
              render: (date) => (
                <span className='text-sm text-gray-500'>
                  {date ? new Date(date).toLocaleString() : '-'}
                </span>
              ),
              width: 100,
            },
          ]}
        />
        {/* </Box> */}
      </Modal>
      <Tooltip
        title={
          !dataofExp?.status || Boolean(dataofExp?.data?.length <= 1)
            ? 'History not avilable'
            : 'View History'
        }
      >
        <Button
          onClick={() => {
            if (dataofExp?.status) {
              setExperimentData(dataofExp?.data)
            }
          }}
          disabled={!dataofExp?.status || Boolean(dataofExp?.data?.length <= 1)}
          type='primary'
          className={'text-neutral-950 bg-transparent border-none'}
        >
          <RiHistoryFill size={20} />
        </Button>
      </Tooltip>
      {/* <CustomButton
        variant='secondary'
        tooltipText={
          !dataofExp?.status || Boolean(dataofExp?.data?.length <= 1)
            ? 'History not avilable'
            : 'View History'
        }
        size='small'
        tooltipPlacement='top'
        startIcon={<RiHistoryFill size={18} />}
        onClick={() => {
          if (dataofExp?.status) {
            setExperimentData(dataofExp?.data)
          }
        }}
        isDisabled={!dataofExp?.status || Boolean(dataofExp?.data?.length <= 1)}
      /> */}
    </>
  )
}

export default ViewHistory
