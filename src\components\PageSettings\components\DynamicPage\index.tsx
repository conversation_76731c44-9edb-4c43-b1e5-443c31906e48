import { Button, message, Steps } from 'antd'
import React, { useEffect, useState } from 'react'
import { EditorAppSDK } from 'contentful-ui-extensions-sdk'
import StepReorder from './Steps/StepReorder'
import { getEntryDataById } from '../../../../globals/utils'
import { Spin } from '../../../atoms'
import StepParameterNameValue from './Steps/StepParameterNameValue'
import GroupDataViewer from './GroupDataViewer'

const { Step } = Steps

interface Props {
  sdk: EditorAppSDK
  entryId: string
}

const DynamicPage = ({ sdk, entryId }: Props) => {
  const [dynamicPageConfiguration, setDynamicPageConfighuration] = useState<
    Record<string, Record<string, string[]>>
  >({})
  const [currentStep, setCurrentStep] = useState(0)
  const [refresh, setRefresh] = useState(false)
  const [parameterName, setParameterName] = useState('')
  const [parameterData, setParameterData] = useState<
    Record<string, Record<string, string[]>>
  >({})
  const [references, setReferences] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [isActiveDynamicPage, setIsActivedynamicPage] = useState(false)
  // useEffect(() => {
  //   getEntryDataById(entryId).then((entry) => {
  //     const refs = extractReferences(entry?.fields)
  //     setReferences(refs?.map((el) => el?.id) ?? [])
  //   })
  // }, [entryId])
  // useEffect(() => {
  //   const fetchConfigs = async () => {
  //     setLoading(true)

  //     try {
  //       const configurations =
  //         (await sdk.entry.fields['configurations'].getValue()) || {}
  //       const dynamicPage = configurations.dynamicpage || {}
  //       setDynamicPageConfighuration(dynamicPage)
  //     } catch (err) {
  //       message.error('Failed to load configurations')
  //     } finally {
  //       setLoading(false)
  //     }
  //   }

  //   fetchConfigs()
  // }, [])
  useEffect(() => {
    if (!entryId || !sdk) {
      return
    }
    const fetchData = async () => {
      setLoading(true)

      try {
        const [entry, configurations] = await Promise.all([
          getEntryDataById(entryId),
          sdk?.entry?.fields['configurations']?.getValue(),
        ])

        const refs = extractReferences(entry?.fields)
        setReferences(refs?.map((el) => el?.id) ?? [])

        const dynamicPage = configurations?.dynamicpage || {}
        setDynamicPageConfighuration(dynamicPage)
      } catch (err) {
        message.error('Failed to load entry or configurations')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [entryId, sdk, refresh])

  const save = async () => {
    console.log('Parameter Data:', parameterData)
    const configurations =
      (await sdk.entry.fields['configurations'].getValue()) || {}
    // const existingDynamicPage = configurations.dynamicpage || {}
    const mergedDynamicPage = { ...parameterData }
    // const mergedDynamicPage = { ...existingDynamicPage, ...parameterData }

    await sdk.entry.fields['configurations'].setValue({
      ...configurations,
      dynamicpage: mergedDynamicPage,
    })
    message.success('Saved!')
    setRefresh((prev) => !prev)
  }

  const steps = [
    {
      title: 'Parameter Name',
      content: (
        <StepParameterNameValue
          parameterName={parameterName}
          setParameterName={setParameterName}
          setParameterData={setParameterData}
          dynamicPageConfiguration={dynamicPageConfiguration}
          isActiveDynamicPage={isActiveDynamicPage}
          parameterData={parameterData}
          references={references}
        />
      ),
    },
    // {
    //   title: 'Parameter Name',
    //   content: (
    // <StepParameterName
    //   parameterName={parameterName}
    //   setParameterName={setParameterName}
    //   setParameterData={setParameterData}
    //   dynamicPageConfiguration={dynamicPageConfiguration}
    //   isActiveDynamicPage={isActiveDynamicPage}
    // />
    //   ),
    // },
    // {
    //   title: 'Parameter Values',
    //   content: (
    // <StepParameterValues
    //   parameterName={parameterName}
    //   parameterData={parameterData}
    //   setParameterData={setParameterData}
    //   references={references?.map((el) => el?.id)}
    // />
    //   ),
    // },

    {
      title: 'Reorder',
      content: (
        <StepReorder
          parameterName={parameterName}
          parameterData={parameterData}
          setParameterData={setParameterData}
        />
      ),
    },
  ]

  useEffect(() => {
    const val =
      Object.entries(dynamicPageConfiguration).find(
        ([key, value]) =>
          key &&
          typeof key === 'string' &&
          value &&
          Object.keys(value).length > 0
      )?.[0] || ''
    if (!val || val?.trim()?.length < 1) {
      return
    }
    setParameterName(val)

    const selectedParameter = dynamicPageConfiguration?.[val] || {}

    const updatedGroups: Record<string, string[]> = {}

    const currentRefsSet = new Set(references)

    for (const [groupName, ids] of Object.entries(selectedParameter)) {
      const filtered = ids.filter((id) => currentRefsSet.has(id))
      const additions = references.filter((id) => !filtered.includes(id))
      updatedGroups[groupName] = [...filtered, ...additions]
    }
    if (val && Object.keys(updatedGroups)?.length) {
      setParameterData({
        [val]: updatedGroups,
      })

      setCurrentStep((steps?.length ?? 1) - 1)
      setIsActivedynamicPage(true)
    }
  }, [dynamicPageConfiguration, references, refresh, steps?.length])

  if (loading) {
    return (
      <div className='w-full h-full flex justify-center items-center'>
        <Spin spinning size='large' />
      </div>
    )
  }
  return (
    <div style={{ padding: 16 }} className='w-full'>
      {/* <GroupDataViewer data={parameterData} /> */}
      <Steps current={currentStep}>
        {steps.map((s, i) => (
          <Step key={i} title={s?.title} />
        ))}
      </Steps>

      <div style={{ marginTop: 24 }}>{steps?.[currentStep]?.content}</div>

      <div
        style={{ marginTop: 24 }}
        className='flex justify-end items-center gap-5'
      >
        {currentStep > 0 && (
          <Button onClick={() => setCurrentStep(currentStep - 1)}>Back</Button>
        )}
        {currentStep < steps.length - 1 ? (
          <Button
            type='primary'
            onClick={() => setCurrentStep(currentStep + 1)}
          >
            Next
          </Button>
        ) : (
          <Button type='primary' onClick={save}>
            Save
          </Button>
        )}
      </div>
    </div>
  )
}

export default DynamicPage

export const extractReferences = (fields: any): ComponentReference[] => {
  const references: ComponentReference[] = []
  const traverseFields = (obj: any) => {
    if (Array.isArray(obj)) {
      obj.forEach(traverseFields)
    } else if (typeof obj === 'object' && obj !== null) {
      if (obj.sys?.type === 'Link' && obj.sys.linkType === 'Entry') {
        references.push({ id: obj.sys.id })
      } else {
        Object.values(obj).forEach(traverseFields)
      }
    }
  }
  traverseFields(fields)
  return references
}
