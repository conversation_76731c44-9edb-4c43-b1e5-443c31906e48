import { Entry, HomeAppSDK } from '@contentful/app-sdk'
import { Box, Button } from '@contentful/f36-components'
import { LinkIcon } from '@contentful/f36-icons'
import React, { useEffect } from 'react'
import ContentEntryCard from '../../../Crosspost/components/ContentEntryCard'
import { getDomainData } from '../../../Crosspost/helpers'
import { domains } from '../../../Crosspost/utils'

/**
 * A React component that renders a list of domains with the ability to link or unlink
 * Contentful entries of type 'linkComponent' as CTAs for each domain. The component
 * maintains a state of CTAs mapped by domain and updates this state based on user
 * interaction. It also provides buttons for linking existing CTA rows and displays
 * linked entries with an option to remove them.
 *
 * @param {Object} props - The component props.
 * @param {HomeAppSDK} props.sdk - The Contentful SDK instance for handling dialog interactions.
 * @param {Function} props.onChange - Callback function to handle changes in the CTA mapping.
 * @param {Object} props.ctaByDomainData - Initial mapping of CTAs by domain.
 */

function TemplateCtaMapper({
  sdk,
  onChange,
  ctaByDomainData,
}: {
  sdk: HomeAppSDK
  onChange: (x: { [key: string]: string }) => void
  ctaByDomainData: { [key: string]: string }
}) {
  const [ctaByDomain, setCtaByDomain] = React.useState<{
    [key: string]: string
  }>({})

  /**
   * Opens a dialog for selecting a single Contentful entry of type 'linkComponent'
   * and updates the ctaByDomain state with the selected entry's id.
   * @param {string} domain - The domain that the primaryCTA is being selected for
   */
  const DtsCTASelector = async (domain: string) => {
    const contentTypes = ['linkComponent']

    const selected: Entry | null = await sdk.dialogs.selectSingleEntry({
      contentTypes: contentTypes,
    })

    if (!selected) return

    const id = selected.sys.id

    let oldData = { ...ctaByDomain }

    oldData = { ...oldData, [domain]: id }

    setCtaByDomain(oldData)

    onChange(oldData)
  }

  const domainId = (domain: string) => {
    const domainId = getDomainData(domain, 'domainCode')

    if (Array.isArray(domainId)) {
      return domainId[0]
    }

    return domainId
  }

  const handleRemoveByDomain = (domain: string) => {
    let oldData = { ...ctaByDomain }

    oldData = { ...oldData, [domain]: '' }

    onChange(oldData)

    setCtaByDomain(oldData)
  }

  useEffect(() => {
    setCtaByDomain(ctaByDomainData)
  }, [ctaByDomainData])

  return (
    <>
      {domains.map((domain) => {
        return (
          <Box key={domain} className='flex flex-col w-full gap-3'>
            <p className='text-lg font-semibold'>
              {getDomainData(domain, 'domainName')}
            </p>
            {ctaByDomain[domainId(domain) as string] ? (
              <ContentEntryCard
                id={ctaByDomain?.[domainId(domain) as string]}
                onRemoveEntry={() =>
                  handleRemoveByDomain(domainId(domain) as string)
                }
              />
            ) : (
              <Button
                onClick={() =>
                  DtsCTASelector((domainId(domain) as string) || '')
                }
                variant='secondary'
                startIcon={<LinkIcon />}
                size='small'
              >
                Link existing CTA Row
              </Button>
            )}
          </Box>
        )
      })}
    </>
  )
}

export default TemplateCtaMapper
