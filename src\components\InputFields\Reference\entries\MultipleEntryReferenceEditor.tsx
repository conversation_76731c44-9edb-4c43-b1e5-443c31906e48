import * as React from 'react'

import { MultipleReferenceEditor } from '../common/MultipleReferenceEditor'
import { ReferenceEditorProps } from '../common/ReferenceEditor'
import { SortableLinkList } from '../common/SortableLinkList'
import { ReferenceValue } from '../types'
import { FetchingWrappedEntryCard } from './WrappedEntryCard/FetchingWrappedEntryCard'

/**
 * Component for editing multiple entry references.
 *
 * This component utilizes the `MultipleReferenceEditor` to manage a list of entry references.
 * It allows entries to be sorted and moved within the list using drag-and-drop functionality.
 *
 * Props:
 * - `props` (ReferenceEditorProps): The properties required for configuring the reference editor.
 *
 * State:
 * - `indexToUpdate` (number | undefined): Keeps track of the index of the item being updated
 *   during sorting operations.
 *
 * Functions:
 * - `updateBeforeSortStart`: Updates the `indexToUpdate` state before a sorting operation starts.
 *
 * The component renders a sortable list of `FetchingWrappedEntryCard` components, each representing
 * an entry reference. Entries can be removed or moved to the top or bottom of the list. The component
 * ensures seamless integration with the provided props and supports custom drag handles.
 */

export function MultipleEntryReferenceEditor(props: ReferenceEditorProps) {
  const [indexToUpdate, setIndexToUpdate] = React.useState<number | undefined>(
    undefined
  )

/**
 * Updates the index of the item being sorted.
 *
 * This function is called before a sorting operation starts
 * to keep track of the index of the item that is going to be
 * moved, allowing for any necessary updates or state management
 * related to the sorting process.
 *
 * @param {Object} param - The parameter object.
 * @param {number} param.index - The index of the item to be updated.
 */

/**
 * Updates the state with the index of the item that is about to be sorted.
 *
 * This function is called at the beginning of a sort operation to store
 * the index of the item being dragged. This ensures that any subsequent
 * operations or state changes can reference the correct item during the
 * sorting process.
 *
 * @param {Object} param - The parameter object containing the index.
 * @param {number} param.index - The index of the item to be updated.
 */

  const updateBeforeSortStart = ({ index }: { index: number }) => {
    setIndexToUpdate(index)
  }

  return (
    <MultipleReferenceEditor
      {...props}
      entityType='Entry'
      setIndexToUpdate={setIndexToUpdate}
    >
      {(childrenProps) => (
        <SortableLinkList<ReferenceValue>
          {...childrenProps}
          axis='y'
          useDragHandle={true}
          updateBeforeSortStart={updateBeforeSortStart}
        >
          {({ items, item, index, isDisabled, DragHandle }) => {
            const lastIndex = items.length - 1
            return (
              <FetchingWrappedEntryCard
                {...childrenProps}
                key={`${item.sys.id}`}
                index={index}
                allContentTypes={childrenProps.allContentTypes}
                isDisabled={isDisabled}
                entryId={item.sys.id}
                onRemove={() => {
                  childrenProps.setValue(
                    items.filter((_value, i) => i !== index)
                  )
                }}
                onMoveTop={
                  index !== 0 ? () => childrenProps.onMove(index, 0) : undefined
                }
                onMoveBottom={
                  index !== lastIndex
                    ? () => childrenProps.onMove(index, lastIndex)
                    : undefined
                }
                renderDragHandle={DragHandle}
                isBeingDragged={index === indexToUpdate}
              />
            )
          }}
        </SortableLinkList>
      )}
    </MultipleReferenceEditor>
  )
}
