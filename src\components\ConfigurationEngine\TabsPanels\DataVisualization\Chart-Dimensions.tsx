import {
  Box,
  FormControl,
  Select,
  Table,
  Tabs,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../../../assets/icons/Info'
import ColorPickerPopup from '../../Components/ColorPickerPopup'

function ChartAndDimensions(props: any) {
  const { onChange, value, loading } = props

  const [chartData, setChartData] = useState({
    defaultChart: 'Line-chart',
    dimensionColors: [] as any,
  })

  const [currentDimensionId, setCurrentDimensionId] = useState('')

  const [showColorPicker, setShowColorPicker] = useState(false)

  const handleChartDataChange = (key: string, value: string | boolean) => {
    const dataToUpdate = {
      ...chartData,
      [key]: value,
    }

    setChartData(dataToUpdate)

    onChange({ chart: { ...dataToUpdate } })
  }

  const handleDimensionColorChange = (value: string) => {
    const dataToUpdate = {
      ...chartData,
      dimensionColors: chartData.dimensionColors
        .map((dc: { id: string }) =>
          dc.id === currentDimensionId ? { ...dc, color: value } : dc
        )
        .concat(
          chartData.dimensionColors.find(
            (dc: { id: string }) => dc.id === currentDimensionId
          )
            ? []
            : [{ id: currentDimensionId, color: value }]
        ),
    }

    setChartData(dataToUpdate)

    onChange({ chart: { ...dataToUpdate } })
  }

  const getDimensionColorById = (id: string) => {
    return (
      chartData.dimensionColors.find((dimension: any) => dimension.id === id)
        ?.color || ''
    )
  }

  useEffect(() => {
    if (!value) return
    if (JSON.stringify(value) !== JSON.stringify(chartData)) {
      setChartData(value)
    }
  }, [value])

  return (
    <Tabs.Panel id='dv-chart' className='tabPanelRoot'>
      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.5rem',
        }}
      >
        <FormControl
          id='original-domain'
          style={{
            width: '70%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Default Chart</FormControl.Label>
            <Tooltip
              placement='top'
              id='tooltip-1'
              content='Set this chart as the default view when the data visualization is loaded.'
            >
              <Info />
            </Tooltip>
          </div>
          <Select
            id='optionSelect-controlled'
            name='optionSelect-controlled'
            value={chartData.defaultChart}
            onChange={(e) =>
              handleChartDataChange('defaultChart', e.target.value)
            }
            placeholder='Select Title Alignment'
            style={{
              width: '100%',
            }}
            isDisabled={loading}
          >
            <Select.Option value='Line-chart'>Line-chart</Select.Option>
            <Select.Option value='Bar-chart'>Bar-chart</Select.Option>
            <Select.Option value='Area-chart'>Area-chart</Select.Option>
            <Select.Option value='Area-chart'>Combo-chart</Select.Option>
            <Select.Option value='Area-chart'>Pie-chart</Select.Option>
            <Select.Option value='Area-chart'>Doughnut-chart</Select.Option>
          </Select>
        </FormControl>
      </Box>

      <Box
        style={{
          width: '100%',
          display: 'flex',
          justifyContent: 'start',
          alignItems: 'start',
          paddingTop: '0.5rem',
        }}
      >
        <FormControl
          id='original-domain'
          style={{
            width: '70%',
          }}
        >
          <div className='formLabelWithIcon'>
            <FormControl.Label>Dimensions Default Color</FormControl.Label>
            <Tooltip
              placement='top'
              id='tooltip-1'
              content='Specify the default colors for each dimension in the chart.'
            >
              <Info />
            </Tooltip>
          </div>
          <div className='row'>
            <Table>
              <Table.Head>
                <Table.Row>
                  <Table.Cell>Dimension</Table.Cell>
                  <Table.Cell>Color</Table.Cell>
                </Table.Row>
              </Table.Head>
              <Table.Body>
                {dimensions.map((name, i) => {
                  return (
                    <Table.Row key={name.id}>
                      <Table.Cell>{name.title}</Table.Cell>
                      <Table.Cell>
                        <div
                          className={`${
                            currentDimensionId === name.id && 'bg'
                          } edit`}
                          onClick={() => {
                            setShowColorPicker(true)
                            setCurrentDimensionId(name.id)
                          }}
                        >
                          {getDimensionColorById(name.id) ? (
                            <div
                              className='color'
                              style={{
                                backgroundColor: getDimensionColorById(name.id),
                              }}
                            ></div>
                          ) : (
                            <span>Pick</span>
                          )}
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  )
                })}
              </Table.Body>
            </Table>
          </div>
        </FormControl>
        <ColorPickerPopup
          isOpen={showColorPicker}
          handleClose={() => {
            setShowColorPicker(false)
          }}
          selectedColors={chartData.dimensionColors.find(
            (color: any) => color.id === currentDimensionId
          )}
          onColorPick={(color: any) => {
            handleDimensionColorChange(color.value)
            setShowColorPicker(false)
          }}
        />
      </Box>
    </Tabs.Panel>
  )
}

export default ChartAndDimensions

const dimensions = [
  {
    id: '1',
    title: 'Dimension 1',
  },
  {
    id: '2',
    title: 'Dimension 2',
  },
  {
    id: '3',
    title: 'Dimension 3',
  },
  {
    id: '4',
    title: 'Dimension 4',
  },
  {
    id: '5',
    title: 'Dimension 5',
  },
  {
    id: '6',
    title: 'Dimension 6',
  },
  {
    id: '7',
    title: 'Dimension 7',
  },
  {
    id: '8',
    title: 'Dimension 8',
  },
  {
    id: '9',
    title: 'Dimension 9',
  },
  {
    id: '10',
    title: 'Dimension 10',
  },
]
