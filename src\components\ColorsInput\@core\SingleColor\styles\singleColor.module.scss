.color {
  height: 20px;
  width: 20px;
  background-color: red;
  border-radius: 50%;
  border: 1px solid lightgray;
  transition: all 0.25s ease-in-out;
  cursor: pointer;
}

.color:hover {
  border-radius: 2px;
  border-color: transparent;
  transform: scale(1.5);
}

.colorActive {
  border-radius: 2px;
  border-color: transparent;
  transform: scale(1.5);
}

//apply on active state on color
.color.activeState {
  border: 1px solid #fff;
  box-shadow:
    rgba(0, 0, 0, 0.25) 0px 54px 55px,
    rgba(0, 0, 0, 0.12) 0px -12px 30px,
    rgba(0, 0, 0, 0.12) 0px 4px 6px,
    rgba(0, 0, 0, 0.17) 0px 12px 13px,
    rgba(0, 0, 0, 0.09) 0px -3px 5px;
}
