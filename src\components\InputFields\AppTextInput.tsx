import { FieldAppSDK } from '@contentful/app-sdk'
import { TextInput } from '@contentful/f36-components'
import React,{useState} from 'react'

interface AppTextInputI {
  sdk: FieldAppSDK
  fieldId: string,
  setValues: (newValue:any) => {},
  values:any
}

function AppTextInput(props: AppTextInputI) {

  const { sdk, fieldId, setValues, values } = props

  const [textValue,setTextValue]=useState('')
  
  return <>
    <TextInput
      value={values['useExistingData']===true?values[fieldId]:textValue}
      onChange={ (e) => {

          setValues((prev: any)=>{
            const newValue = e.target.value;
            setTextValue(newValue)

            const updatedVal = { ...prev, [fieldId]: newValue }
            sdk.field.setValue(updatedVal)
            return updatedVal
          })
         
        }
      }
/>
</>
  
}

export default AppTextInput