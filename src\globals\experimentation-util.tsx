import * as contentful from 'contentful-management'
import { useCallback, useState } from 'react'
import { ENV_VARIABLES, } from '../constant/variables'

// Utility function to get entry data by ID
async function getEntryDataById(entryId: string) {
  const client = contentful.createClient({
    accessToken: ENV_VARIABLES.contentfulToken,
  })

  try {
    const space = await client.getSpace(ENV_VARIABLES.contentfulSpaceID)
    const environment = await space.getEnvironment(ENV_VARIABLES.contentfulEnvironment)
    const entry = await environment.getEntry(entryId)
    return entry
  } catch (error) {
    console.error('Error fetching entry:', entryId, error)
    throw new Error('Error fetching entry data')
  }
}

// Utility function to create a new entry
async function createEntry(
  contentType: string,
  fields: any,
  metadata: any
): Promise<string> {
  const client = contentful.createClient({
    accessToken: ENV_VARIABLES.contentfulToken,
  })

  try {
    const space = await client.getSpace(ENV_VARIABLES.contentfulSpaceID)
    const environment = await space.getEnvironment(ENV_VARIABLES.contentfulEnvironment)

    if (fields?.internalName?.['en-CA']) {
      fields.internalName['en-CA'] += ' - Copy'
    } else if (fields?.internalName) {
      fields.internalName['en-CA'] = 'Copy By Duplicator'
    } else {
      fields.internalName = { 'en-CA': 'Copy By Duplicator' }
    }
    const newEntry = await environment.createEntry(contentType, {
      fields,
      metadata,
    })
    // Uncomment to publish the entry if needed
    // await newEntry.publish()
    return newEntry.sys.id
  } catch (error) {
    console.error('Error creating new entry:', { contentType, fields }, error)
    throw new Error('Error creating new entry')
  }
}

// Utility functions to manage progress
function addToCreate(progress: { total: number; done: number; status: any[] }) {
  return {
    ...progress,
    total: progress.total + 1,
    status: [...progress.status],
  }
}

function addToDone(progress: { total: number; done: number; status: any[] }) {
  return {
    ...progress,
    done: progress.done + 1,
    status: [...progress.status],
  }
}

// Recursive function to check and duplicate deeply nested components
async function checkAndDuplicateNestedLinks(
  value: any,
  type: 'duplicate' | 'reference',
  entryTypesToAvoid: Set<string>,
  isNested: boolean,
  entryIdToAvoid: Set<string>,
  updateProgress: (created?: boolean, details?: any) => void
): Promise<any> {
  if (type === 'reference') return value

  if (
    typeof value === 'object' &&
    value !== null &&
    value?.sys?.id &&
    entryIdToAvoid.has(value?.sys?.id)
  ) {
    // const entryId = value.sys.id
    // console.log('Skipping duplication for avoided entry:', entryId)
    return value
  }

  if (Array.isArray(value)) {
    return Promise.all(
      value.map((item) =>
        checkAndDuplicateNestedLinks(
          item,
          type,
          entryTypesToAvoid,
          true,
          entryIdToAvoid,
          updateProgress
        )
      )
    )
  } else if (typeof value === 'object' && value !== null) {
    if (
      value.sys &&
      value.sys.type === 'Link' &&
      value.sys.linkType === 'Entry'
    ) {
      const entryId = value.sys.id

      if (entryIdToAvoid.has(entryId)) {
        // console.log('Skipping duplication for avoided entry:', entryId)
        return value
      }

      const entry = await getEntryDataById(entryId)
      if (isNested && entryTypesToAvoid.has(entry.sys.contentType.sys.id)) {
        // console.log('Skipping duplication for flagged entry:', entryId)
        return value
      } else {
        const newNestedId = await duplicateEntryWithNested(
          entryId,
          type,
          entryTypesToAvoid,
          true,
          entryIdToAvoid,
          updateProgress
        )
        return { ...value, sys: { ...value.sys, id: newNestedId } }
      }
    } else {
      const newObj = { ...value }
      for (const key in newObj) {
        newObj[key] = await checkAndDuplicateNestedLinks(
          newObj[key],
          type,
          entryTypesToAvoid,
          isNested,
          entryIdToAvoid,
          updateProgress
        )
      }
      return newObj
    }
  }

  return value
}

// Recursive function to duplicate components
async function duplicateEntryWithNested(
  entryId: string,
  type: 'duplicate' | 'reference',
  entryTypesToAvoid: Set<string>,
  isNested: boolean,
  entryIdToAvoid: Set<string>,
  updateProgress: (created?: boolean, details?: any) => void
): Promise<string> {
  if (type === 'reference' && isNested) {
    return entryId
  }

  if (entryIdToAvoid.has(entryId)) {
    // console.log('Skipping duplication for avoided entry:', entryId)
    return entryId
  }

  const entry = await getEntryDataById(entryId)
  const contentType = entry.sys.contentType.sys.id

  if (isNested && entryTypesToAvoid.has(contentType)) {
    // console.log('Skipping duplication for flagged entry:', entryId)
    return entryId
  }
  const metadata = entry.metadata
  const fields: any = {}
  for (const [fieldKey, localizedFieldData] of Object.entries(entry.fields)) {
    fields[fieldKey] = {}
    for (const [locale, value] of Object.entries(localizedFieldData as any)) {
      fields[fieldKey][locale] = await checkAndDuplicateNestedLinks(
        value,
        type,
        entryTypesToAvoid,
        isNested,
        entryIdToAvoid,
        updateProgress
      )
    }
  }

  if (type === 'duplicate') {
    updateProgress(false, {
      id: entry.sys.id,
      contentType: entry.sys.contentType.sys.id,
      internalName: entry.fields.internalName
        ? entry.fields.internalName['en-CA']
        : 'N/A',
    })
    const newNestedId = await createEntry(contentType, fields, metadata)
    updateProgress(true, {
      id: entry.sys.id,
      contentType: entry.sys.contentType.sys.id,
      internalName: entry.fields.internalName
        ? entry.fields.internalName['en-CA'] + ''
        : 'N/A',
      newNestedId,
    })
    return newNestedId
  } else if (type === 'reference' && !isNested) {
    updateProgress(false, {
      id: entry.sys.id,
      contentType: entry.sys.contentType.sys.id,
      internalName: entry.fields.internalName
        ? entry.fields.internalName['en-CA']
        : 'N/A',
    })
    const newNestedId = await createEntry(contentType, fields, metadata)
    updateProgress(false, {
      id: entry.sys.id,
      contentType: entry.sys.contentType.sys.id,
      internalName: entry.fields.internalName
        ? entry.fields.internalName['en-CA'] + ''
        : 'N/A',
    })
    return newNestedId
  } else {
    throw new Error('Invalid type specified')
  }
}

// Hook for duplicating components
export function useDuplicateComponent() {
  const [isLoading, setIsLoading] = useState(false)
  const [progress, setProgress] = useState({ total: 0, done: 0, status: [] })

  const updateProgress = (created: boolean = false, details?: any) => {
    // console.log(created,details,"updateProgress")
    setProgress((prev) => {
      const upatedStatus = [
        ...prev.status.filter((el) => el?.id !== details?.id),
        { ...details },
      ]
      return created
        ? addToCreate({ ...prev, status: upatedStatus })
        : addToDone({ ...prev, status: upatedStatus })
    })
  }

  const duplicateComponent = useCallback(
    async (
      entryId: string,
      type: 'duplicate' | 'reference',
      refrenceToavoidDuplicate?: Array<string>
    ): Promise<string> => {
      setIsLoading(true)
      setProgress({ total: 0, done: 0, status: [] })

      const entryTypesToAvoid = new Set(['page', 'componentImage'])
      const entryIdToAvoid = new Set(refrenceToavoidDuplicate ?? [])

      try {
        // console.log('Starting duplication process for component:', {
        //   entryId,
        //   type,
        // })
        // updateProgress(true, { id: entryId, contentType: 'Starting', internalName: '' })
        const result = await duplicateEntryWithNested(
          entryId,
          type,
          entryTypesToAvoid,
          false,
          entryIdToAvoid,
          updateProgress
        )
        return result
      } catch (error) {
        console.error('Error during duplication:', error)
        throw error
      } finally {
        setIsLoading(false)
      }
    },
    []
  )

  return { isLoading, progress, duplicateComponent }
}

export function slugify(input: string): string {
  return input
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s/-]/g, '') // keep alphanumeric, space, hyphen, slash
    .replace(/\s+/g, '-') // spaces to hyphen
    .replace(/-+/g, '-') // collapse multiple hyphens
    .replace(/^-+|-+$/g, '') // remove leading/trailing hyphens
}

export function generateSlugWithTimestamp(
  slug: string,
  title: string,
  timestamp: string | number
): string {
  const fullSlug = `${BLOCKED_PREFIX}/${slug}`.replace(/\/+$/, '')
  // const slugifiedTitle = slugify(title)
  // const fullSlug = `${slug.replace(/\/+$/, '')}/${timestamp}/${slugifiedTitle}`
  const finalSlug = fullSlug.slice(0, 256)

  // Ensure no trailing slash at the end
  return finalSlug.replace(/\/+$/, '')
}
export function cleanSlug(slug?: string): string {
  if (!slug) return ''

  // Remove the BLOCKED_PREFIX if it exists
  let cleanedSlug = slug.replace(new RegExp(`^/?${BLOCKED_PREFIX}/?`), '')

  // Ensure it does not start with "/"
  cleanedSlug = cleanedSlug.replace(/^\/+/, '')

  return cleanedSlug
}
export const BLOCKED_PREFIX = 'i'

export const ALLOWED_DOMAINS = ['domainAltusGroupCom', 'domainReonomyCom']
