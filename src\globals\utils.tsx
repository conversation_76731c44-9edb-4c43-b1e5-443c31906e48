import { entityHelpers } from '@contentful/field-editor-shared'
import * as contentful from 'contentful-management'
import { domainsConfig } from '../components/Crosspost/utils'
import { ACCESS_TOKEN, ENVIRONMENT, SPACE_ID, TOKEN } from '../constant/variables'
import { getConfigurationsCollectionQuery } from './queries'
import { DateTimeFormatOptions, GetDateAndTimeOutputI } from './types'

/**
 * Add a node to the HTML Head tag
 * @param {Node} node - The node to be added to the head tag.
 */
export const add2Head = (node: Node) => {
  // Find the HTML head element in the document
  const head = document.querySelector('head') as HTMLHeadElement

  // Append the given node to the head element
  head.appendChild(node)
}

/**
 * Inject SEO data into the document using a JSON object.
 * @param {object} seoJsonData - The SEO data in JSON format to be injected.
 */
export const injectSEO = (seoJsonData: object) => {
  // Create a new script element
  const script = document.createElement('script')

  // Set the script type to 'application/ld+json' (JSON-LD for SEO)
  script.type = 'application/ld+json'

  // Convert the SEO data object to a JSON string and set it as the script's content
  script.innerHTML = JSON.stringify(seoJsonData)

  // Add the script element to the head tag using the 'add2Head' function
  add2Head(script)
}

/**
 * Check if the code is running in a browser environment.
 * @returns {boolean} - Returns true if the code is running in a browser, false otherwise.
 */
export const isBrowser = () => typeof window !== 'undefined'

/**
 * Scroll the window to the top of the page.
 */
export const scrollToTop = () => {
  // Scroll the window to the top (x=0, y=0)
  window.scrollTo(0, 0)
}

export function registerScrollEvent() {
  // on dom ready add event listner update page progress on scroll
  window.onscroll = function () {
    // calculate scroll percentage
    const scrollPercentage =
      (document.documentElement.scrollTop /
        (document.documentElement.scrollHeight -
          document.documentElement.clientHeight)) *
      100
    console.log('scrollPercentage', scrollPercentage)
    // dispatch scroll percentage to redux store
    // store.dispatch(setPageScroll(scrollPercentage))
  }
}

/**
 * Function to truncate the description to a specified word limit
 * @param text Specify the text that needs to be truncated.
 * @param limit Specify the number of words to which the text needs to be shortend
 * @param showEllipsis Specify whether and elipses should be shown at the end of truncated text.
 */
export function truncateText(
  text: string,
  limit: number,
  showEllipsis?: boolean
): string {
  let updatedText = text
  const words = text.split(' ')
  if (words.length > limit) {
    const ellipsis = showEllipsis === true ? '...' : ''
    updatedText = words.slice(0, limit).join(' ') + ellipsis
  }
  return updatedText
}

/**
 * Function to Get Time, Date and Timezone.
 * @param start this will take start date or time in string (e.g "2023-10-19T00:00:00.000Z" OR "2023-10-10T10:00:00.000+05:30")
 * @param end this will take end date or time in string
 */

export const getDateAndTime = (
  start: string,
  end: string | null
): GetDateAndTimeOutputI => {
  // converting strings into Date type
  const startDateAndTime = new Date(start)
  const endDateAndTime = end !== null ? new Date(end) : ''

  // options related to Time, Date and TimeZone
  const dateOption: DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }
  const timeOptions: DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  }

  // Code below converts date into these format
  // Date format is MMM/DD/YYYY =  Sep 30, 2023
  // If event duration extends more than 1 day, date format is Sep 29 - 30, 2023

  const startDate = startDateAndTime.toLocaleDateString('en-US', {
    ...dateOption,
    year: !endDateAndTime ? 'numeric' : undefined,
  })
  const endDate = endDateAndTime
    ? endDateAndTime.toLocaleDateString('en-US', {
        ...dateOption,
      })
    : null

  // Code below converts time into these format
  // Time format is HH:MM AM/PM =  04:30 AM
  // If event time contains Start and End time, time format is 9:00 - 5:00 PM

  const startTime = startDateAndTime.toLocaleTimeString('en-US', timeOptions)
  const endTime = endDateAndTime
    ? endDateAndTime.toLocaleTimeString('en-US', timeOptions)
    : null

  // Code below return Timezone in string type
  const timeZoneAbbreviation = (
    Intl.DateTimeFormat('en-US', { timeZoneName: 'short' })
      .formatToParts(startDateAndTime)
      .find((part) => part.type === 'timeZoneName') || {}
  ).value

  const Result = {
    date: ` ${startDate} ${endDate !== null ? '- ' + endDate : ''}`,
    time: ` ${endTime !== null ? startTime.split(' ')[0] : startTime} ${
      endTime !== null ? '- ' + endTime : ''
    } `,
    timeZone: timeZoneAbbreviation,
  }
  return Result
}

export async function updateCurrentEntry(
  entryId: string,
  tags: any,
  slugs: any
) {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  const res = await client
    .getSpace(SPACE_ID)
    .then((space) => {
      return space.getEnvironment(ENVIRONMENT).then((environment) => {
        return environment.getEntry(entryId).then(async (entry) => {
          let fields = { ...entry.fields }

          if (Object.keys(slugs).length > 0) fields['slug'] = slugs

          let newMetaData: any = { ...entry?.metadata }
          newMetaData['tags'] = tags

          entry.fields = fields
          entry.metadata = newMetaData

          await entry.update()
          // updatedMetadataEntry.publish()
        })
      })
    })
    .catch((error) => console.error(error))

  return res
}
export async function categorizeTags(tags: any) {
  const categories: any = {}

  tags.forEach((tag: any) => {
    const parts = tag.name.split(':').map((part: any) => part.trim()) // Split by ':' and trim spaces
    if (parts.length > 1) {
      // Ensure there is a category and a tag part
      const category = parts[0]
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(tag) // Push the entire tag object
    }
  })

  return categories
}

export async function getAllTags() {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  let skip = 0
  let allTags = []
  let totalTags = 0

  try {
    do {
      const environment = await client
        .getSpace(SPACE_ID)
        .then((space) => space.getEnvironment(ENVIRONMENT))
      const response = await environment.getTags({
        skip: skip,
        limit: 100,
      })

      allTags.push(...response.items)
      totalTags = response.total
      skip += response.limit
    } while (skip < totalTags)
  } catch (error) {
    console.error('Error fetching tags:', error)
    return []
  }

  return allTags
}

export function sortByKey(array: any[], key: string, order = 'asc') {
  return array.sort((a, b) => {
    let x = a[key]
    let y = b[key]

    // Handling string comparison in a case-insensitive manner
    if (typeof x === 'string') {
      x = x.toLowerCase()
    }
    if (typeof y === 'string') {
      y = y.toLowerCase()
    }

    // Determine the order of sorting
    if (order === 'asc') {
      return x > y ? 1 : x < y ? -1 : 0
    } else {
      return x < y ? 1 : x > y ? -1 : 0
    }
  })
}

export const Languages = [
  {
    title: 'English (en-CA)',
    value: 'en-CA',
    text: 'English',
  },
  {
    title: 'French (fr-CA)',
    value: 'fr-CA',
    text: 'French',
  },
  {
    title: 'German (de-DE)',
    value: 'de-DE',
    text: 'German',
  },
  {
    title: 'Italian (it)',
    value: 'it',
    text: 'Italian',
  },
  {
    title: 'Spanish (es)',
    value: 'es',
    text: 'Spanish',
  },
  {
    title: 'Dutch (nl)',
    value: 'nl',
    text: 'Dutch',
  },
]

export const createOrUpdateAsset = async (
  file: any,
  locale: string,
  id?: string,
  description?: string
) => {
  try {
    const client = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN,
    })

    const space = await client.getSpace(SPACE_ID)
    const environment = await space.getEnvironment(ENVIRONMENT)
    const fileName = file.name.substring(0, file.name.lastIndexOf('.'))
    const title = fileName
    let asset

    if (id) {
      // Update existing asset by ID
      try {
        asset = await environment.getAsset(id)
        await asset.unpublish()
        await asset.delete()
      } catch (e) {
        console.error(e)
      }
      asset = await environment.createAssetFromFiles({
        fields: {
          title: {
            [locale]: title,
          },
          description: {
            [locale]: description ?? 'Uploaded via FileUpload component',
          },
          file: {
            [locale]: {
              contentType: file.type,
              fileName: file.name,
              file: file,
            },
          },
        },
      })
    } else {
      // Create a new asset
      asset = await environment.createAssetFromFiles({
        fields: {
          title: {
            [locale]: title,
          },
          description: {
            [locale]: description ?? 'Uploaded via FileUpload component',
          },
          file: {
            [locale]: {
              contentType: file.type,
              fileName: file.name,
              file: file,
            },
          },
        },
      })
    }

    if (!asset.isPublished()) {
      // Wait for asset to be processed
      await asset.processForAllLocales()
    }
    // Ensure the asset is processed
    const processedAsset = await environment.getAsset(asset.sys.id)
    if (!asset.isPublished()) await processedAsset.publish()
    return processedAsset
  } catch (error) {
    console.error('Error creating or updating asset:', error)
    throw error
  }
}

export const deleteAsset = async (id: string) => {
  try {
    const client = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN,
    })

    const space = await client.getSpace(SPACE_ID)
    const environment = await space.getEnvironment(ENVIRONMENT)

    // Get the asset by its ID
    const asset = await environment.getAsset(id)

    // Unpublish the asset (required before deletion)
    if (asset.isPublished()) {
      await asset.unpublish()
    }

    // Delete the asset
    await asset.delete()

    console.log(`Asset with ID ${id} has been successfully deleted.`)
    return { success: true, message: `Asset with ID ${id} deleted.` }
  } catch (error) {
    console.error('Error deleting asset:', error)
    throw error
  }
}

export async function getAssetInfo(asset: any, currentLocale: string) {
  const status = entityHelpers.getEntryStatus(asset.sys)
  const contentType = asset.fields.file[currentLocale].contentType
  const title = asset.fields.title[currentLocale]
  return { status, title, contentType }
}

export async function getAssetData(AssetId: string) {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  try {
    const space = await client.getSpace(SPACE_ID)
    const environment = await space.getEnvironment(ENVIRONMENT)
    // Fetch the linked asset
    const asset = await environment.getAsset(AssetId)

    return asset // Adjust this according to your needs
  } catch (error) {
    console.error(error)
  }
}

export async function fetchGraphQL(query: string): Promise<unknown> {
  return fetch(
    `https://graphql.contentful.com/content/v1/spaces/${SPACE_ID}/environments/${ENVIRONMENT}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${ACCESS_TOKEN}`,
      },
      body: JSON.stringify({ query }),
    }
  ).then((response) => response.json())
}

export async function getEntryDataById(entryId: string) {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  const res = await client
    .getSpace(SPACE_ID)
    .then((space) => {
      return space
        .getEnvironment(ENVIRONMENT)
        .then((environment) =>
          environment.getEntry(entryId).then((entry) => entry)
        )
    })
    .catch((error) => console.error(error))

  return res
}

interface Sys {
  id: string
}

interface ConfigurationItem {
  sys: Sys
  data: {
    json: unknown
  }
}

interface ConfigurationsResponse {
  data?: {
    configurationsCollection?: {
      items: ConfigurationItem[]
    }
  }
}
export async function getConfigurationByScopeAndType(
  type: string,
  scope: string
): Promise<unknown | null> {
  const query = `
    {
      configurationsCollection(preview: true, where: { type: "${type}", scope: "${scope}" }) {
        items {
          sys {
            id
          }
          data {
            json
          }
        }
      }
    }
  `

  try {
    const response: ConfigurationsResponse = await fetchGraphQL(query)
    const items = response?.data?.configurationsCollection?.items

    if (items && items.length > 0) {
      const data = await JSON.parse(
        items?.[0]?.data?.json?.content?.[0]?.content?.[0]?.value
      )
      // Return the JSON data from the first matched item
      return { data, id: items?.[0]?.sys?.id }
    } else {
      return null // No matching configuration found
    }
  } catch (error) {
    console.error('Error fetching configuration by scope and type:', error)
    return null
  }
}
export const SaveConfigurationandData = async (
  data: any,
  contentId: string,
  dopublish = false
) => {
  let updatedPayload = {
    data: {
      'en-CA': {
        content: [
          {
            content: [
              {
                data: {},
                marks: [],
                nodeType: 'text',
                value: JSON.stringify(data),
              },
            ],
            data: {},
            nodeType: 'paragraph',
          },
        ],
        data: {},
        nodeType: 'document',
      },
    },
  }
  await updateEntryData(contentId, updatedPayload, dopublish)
}

export const getDomainDataByDomainName = (domainName: string) => {
  return domainsConfig.find((d: any) => d.key === domainName)
}

export async function createContentEntry(
  contentTypeId: string,
  fields?: any,
  tags?: any,
  doPublish?: boolean
) {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  let payload: any = {
    fields: fields || {},
  }

  if (tags) {
    payload = {
      ...payload,
      metadata: {
        tags: tags,
      },
    }
  }

  const res = await client.getSpace(SPACE_ID).then((space) => {
    return space.getEnvironment(ENVIRONMENT).then(async (environment) => {
      const res = await environment.createEntry(contentTypeId, payload)

      if (doPublish) {
        await res.publish()
      }

      return res
    })
  })

  return res
}

export function findValueByKey(obj: any, key: any): any {
  let result = null

  // Check if the current object has the key
  if (obj.hasOwnProperty(key)) {
    return obj[key]
  }

  // Iterate through the keys of the object
  for (const k in obj) {
    if (obj.hasOwnProperty(k) && typeof obj[k] === 'object') {
      // Recursively search in the nested objects
      result = findValueByKey(obj[k], key)
      if (result !== null) {
        return result
      }
    }
  }

  return result
}

export function updateValuesInObject(originalObj: any, updates: any) {
  const targetObj = JSON.parse(JSON.stringify(originalObj))

  // Recursive function to update values
  function recursiveUpdate(obj: any, updates: any) {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        if (typeof obj[key] === 'object' && obj[key] !== null) {
          // Recursively update nested objects
          recursiveUpdate(obj[key], updates)
        } else {
          // Update the value if the key is found in the updates object
          if (updates.hasOwnProperty(key)) {
            obj[key] = updates[key]
          }
        }
      }
    }
  }

  // Start the recursive update
  recursiveUpdate(targetObj, updates)

  return targetObj
}

export async function updateEntryData(
  entryId: string,
  data: any,
  doPublish?: boolean
) {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  const res = await client
    .getSpace(SPACE_ID)
    .then((space) => {
      return space.getEnvironment(ENVIRONMENT).then((environment) => {
        return environment.getEntry(entryId).then(async (entry) => {
          entry.fields = { ...entry.fields, ...data }

          const res = await entry.update()
          if (doPublish) {
            res.publish()
          }
          return res
        })
      })
    })
    .catch((error) => console.error(error))

  return !!res
}

export async function archiveEntry(entryId: string) {
  try {
    const client = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN,
    })
    const space = await client.getSpace(SPACE_ID)
    const environment = await space.getEnvironment(ENVIRONMENT)
    const entry = await environment.getEntry(entryId)
    if (entry) {
      if (entry?.sys?.publishedAt) {
        await entry?.unpublish()
        console.log(`Entry with ID ${entryId} unpublished successfully.`)
      }
      await entry?.archive()
    }
    console.log(`Entry with ID ${entryId} archived successfully.`)
  } catch (error) {
    console.error(`Error archiving entry with ID ${entryId}:`, error)
    throw error
  }
}

export async function unpublishEntry(entryId: string) {
  try {
    const client = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN,
    })
    const space = await client.getSpace(SPACE_ID)
    const environment = await space.getEnvironment(ENVIRONMENT)
    const entry = await environment.getEntry(entryId)
    if (entry) {
      if (entry?.sys?.publishedAt) {
        await entry?.unpublish()
        console.log(`Entry with ID ${entryId} unpublished successfully.`)
      }
      // await entry?.archive()
    }
    // console.log(`Entry with ID ${entryId} archived successfully.`)
  } catch (error) {
    console.error(`Error archiving entry with ID ${entryId}:`, error)
    throw error
  }
}

export async function deleteEntry(entryId: string) {
  try {
    const client = contentful.createClient({
      space: SPACE_ID,
      accessToken: TOKEN,
    })
    const space = await client.getSpace(SPACE_ID)
    const environment = await space.getEnvironment(ENVIRONMENT)
    const entry = await environment.getEntry(entryId)
    if (entry) {
      if (entry?.sys?.publishedAt) {
        await entry?.unpublish()
        console.log(`Entry with ID ${entryId} unpublished successfully.`)
      }
      await entry?.delete()
    }

    console.log(`Entry with ID ${entryId} deleted successfully.`)
  } catch (error) {
    console.error(`Error deleting entry with ID ${entryId}:`, error)
    throw error
  }
}

export const fetchGlobalConfigurationData = async () => {
  const res = await fetchGraphQL(getConfigurationsCollectionQuery()).then(
    (res: any) => res?.data?.configurationsCollection?.items
  )

  const matchedObj = res?.find((item: any) => {
    return item.type === 'Global'
  })
  const matchedData = matchedObj?.data?.json?.content?.[0]?.content?.[0]?.value

  if (matchedData) {
    return {
      data: JSON.parse(matchedData),
      contentId: matchedObj?.sys?.id,
    }
  }
}

export const fetchMSAConfigurationData = async (selectedDomain: string) => {
  const res = await fetchGraphQL(getConfigurationsCollectionQuery()).then(
    (res: any) => res?.data?.configurationsCollection?.items
  )

  const matchedObj = res?.find((item: any) => {
    return (
      item?.internalName?.split(' ')?.includes(selectedDomain?.toUpperCase()) &&
      item?.type === 'MSA'
    )
  })
  const matchedData = matchedObj?.data?.json?.content?.[0]?.content?.[0]?.value

  if (matchedData) {
    return {
      data: JSON.parse(matchedData),
      contentId: matchedObj?.sys?.id,
    }
  }
}

export async function getEntryFieldData({
  environment,
  entryId,
  locale = 'en-CA',
}: any) {
  const response = await fetch(
    `https://preview.contentful.com/spaces/8jgyidtgyr4v/environments/${environment}/entries/${entryId}?access_token=${process.env.REACT_APP_ACCESS_TOKEN}&locale=${locale}`
  )
    .then((response) => response.json())
    .then((result) => result.fields)
    .catch((error) => console.error(error))

  return response
}

export function getRevalidationDomainByTag(domainTag?: string) {
  // return "http://localhost:3000"
  switch (domainTag) {
    case 'domainAltusGroupCom':
      return 'https://msa-agl-v3w-git-staging-altus.vercel.app'
    case 'domainVerifinoCom':
      return 'https://msa-ver-v3w-git-staging-altus.vercel.app'
    case 'domainReonomyCom':
      return 'https://msa-reo-v3w-git-staging-altus.vercel.app'
    case 'domainFinanceActiveCom':
      return 'https://msa-fia-v3w-git-staging-altus.vercel.app'
    case 'domainRethinkCom':
      return 'https://msa-rth-v3w-git-staging-altus.vercel.app'
    case 'domainOne11Com':
      return 'https://msa-o11-v3w-git-staging-altus.vercel.app'
    case 'domainForburyCom':
      return 'https://msa-for-v3w-git-staging-altus.vercel.app'
    case 'domainAgStudio':
      return 'https://msa-adx-v3w-git-staging-altus.vercel.app'
    default:
      return null
  }
}

export function getBranchDomainByTag(
  domainTag?: string,
  branchname: string = 'staging'
) {
  // return "http://localhost:3000"
  switch (domainTag) {
    case 'domainAltusGroupCom':
      return `https://msa-agl-v3w-git-${branchname}-altus.vercel.app`
    case 'domainVerifinoCom':
      return `https://msa-ver-v3w-git-${branchname}-altus.vercel.app`
    case 'domainReonomyCom':
      return `https://msa-reo-v3w-git-${branchname}-altus.vercel.app`
    case 'domainFinanceActiveCom':
      return `https://msa-fia-v3w-git-${branchname}-altus.vercel.app`
    case 'domainRethinkCom':
      return `https://msa-rth-v3w-git-${branchname}-altus.vercel.app`
    case 'domainOne11Com':
      return `https://msa-o11-v3w-git-${branchname}-altus.vercel.app`
    case 'domainForburyCom':
      return `https://msa-for-v3w-git-${branchname}-altus.vercel.app`
    case 'domainAgStudio':
      return `https://msa-adx-v3w-git-${branchname}-altus.vercel.app`
    default:
      return null
  }
}

export async function updateSingleEntryData(
  entryId: string,
  updates: { fieldId: string; locale: string; value: string }[]
) {
  const client = contentful.createClient({
    accessToken: TOKEN,
  })

  try {
    const space = await client.getSpace(SPACE_ID)
    const environment = await space.getEnvironment(ENVIRONMENT)
    const entry = await environment.getEntry(entryId)

    updates.forEach(({ fieldId, locale, value }) => {
      if (entry.fields[fieldId]) {
        entry.fields[fieldId][locale] = value
      } else {
        entry.fields[fieldId] = { [locale]: value }
      }
    })

    await entry.update()
    return { success: true, message: '' }
  } catch (error) {
    let errorMessage = ''

    try {
      // console.log('Raw error:', error)

      // Attempt to parse the error message
      const parsedError = JSON.parse(error.message)

      // Extract the error details if available
      const msg = parsedError?.details?.errors?.[0]?.details

      if (msg) {
        errorMessage = msg
      } else if (parsedError?.message) {
        errorMessage = parsedError?.message
      }
    } catch (parsingError) {
      errorMessage = 'An error occurred, but details could not be retrieved.'
    }

    return { success: false, message: errorMessage }
  }
}

export async function updateEntryTags(newTags: string[], entryId: string) {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  try {
    // Get the space
    const space = await client.getSpace(SPACE_ID)

    // Get the environment
    const environment = await space.getEnvironment(ENVIRONMENT)

    // Get the entry you want to update
    const entry = await environment.getEntry(entryId)

    // Update the tags
    entry.metadata.tags = newTags.map((tagId) => ({
      sys: {
        type: 'Link',
        linkType: 'Tag',
        id: tagId,
      },
    }))

    await entry.update()
  } catch (error) {
    console.error('Error updating entry:', error)
  }
}
export async function getAllContentTypes() {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })
  try {
    // Get the space
    const space = await client.getSpace(SPACE_ID)

    // Get the environment
    const environment = await space.getEnvironment(ENVIRONMENT)

    // Get the entry you want to update
    const contentTypes = await environment.getContentTypes()
    return contentTypes

    // Update the tags
  } catch (error) {
    console.error('Error updating entry:', error)
  }
}

export async function publishEntry(entryId: string) {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  try {
    const space = await client.getSpace(SPACE_ID)
    const environment = await space.getEnvironment(ENVIRONMENT)
    const entry = await environment.getEntry(entryId)
    await entry.publish()

    return true
  } catch (error) {
    console.error('Error updating entry:', error)
    return false
  }
}

export async function addFieldToContentModel(contentTypeId: string) {
  const client = contentful.createClient({
    space: SPACE_ID,
    accessToken: TOKEN,
  })

  try {
    await client
      .getSpace(SPACE_ID)
      .then((space) => space.getEnvironment(ENVIRONMENT))
      .then((environment) => environment.getContentType(contentTypeId))
      .then((contentType) => {
        contentType.fields.push({
          id: 'configurations',
          name: 'Configurations',
          type: 'Object',
          localized: false,
          required: false,
          validations: [],
          disabled: false,
          omitted: false,
        })
        return contentType.update()
      })
      .then((updatedContentType) => updatedContentType.publish()) // Publish the updated content type
      .then((publishedContentType) =>
        console.log('Content model updated successfully:', publishedContentType)
      )
      .catch((error) => console.error('Error updating content model:', error))
  } catch (error) {
    console.error('Error updating entry:', error)
    return false
  }
}

// Utility function for sorting strings
export const sortStrings = (
  a: Record<string, any>,
  b: Record<string, any>,
  key: string
): number => {
  const valueA: string = a?.[key]?.toLowerCase() || ''
  const valueB: string = b?.[key]?.toLowerCase() || ''
  return valueA.localeCompare(valueB)
}

// Utility function for sorting dates
export const sortDates = (
  a: Record<string, any>,
  b: Record<string, any>,
  key: string
): number => {
  const dateA: number = new Date(a?.[key]).getTime()
  const dateB: number = new Date(b?.[key]).getTime()
  return dateA - dateB
}

// Utility function for filtering values
export const filterValues = (
  value: any,
  record: Record<string, any>,
  key: string
): boolean => {
  return record?.[key] === value
}

// Utility function to get nested values
const getNestedValue = (
  obj: { [key: string]: any },
  keys: string | string[]
) => {
  if (typeof keys === 'string') return obj?.[keys]
  return keys.reduce((value, key) => value?.[key], obj)
}

// Global utility function for sorting
export const sortValues = (
  a: { [key: string]: any },
  b: { [key: string]: any },
  key: string | string[]
): number => {
  const valueA = getNestedValue(a, key)
  const valueB = getNestedValue(b, key)

  // Handle undefined values
  if (!valueA && !valueB) return 0
  if (!valueA) return 1
  if (!valueB) return -1

  // Handle dates
  const dateA = new Date(valueA)
  const dateB = new Date(valueB)
  if (!isNaN(dateA.getTime()) && !isNaN(dateB.getTime())) {
    return dateA.getTime() - dateB.getTime()
  }

  // Handle strings
  return valueA.toString().localeCompare(valueB.toString())
}
