import {
  Button,
  Form,
  FormControl,
  Modal,
  Select,
  Tooltip,
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import Info from '../../../assets/icons/Info'
import styleVar from '../../../globals/styles/_nextSCSSVars.module.scss'
import ColorPickerPopup from '../../ConfigurationEngine/Components/ColorPickerPopup'
const StyleList = {
  'Font Sizes': {
    styles: [
      { label: 'fs1', size: '12', lineHeight: '20' },
      { label: 'fs2', size: '14', lineHeight: '22' },
      { label: 'fs3', size: '16', lineHeight: '24' },
      { label: 'fs4', size: '18', lineHeight: '26' },
      { label: 'fs5', size: '20', lineHeight: '28' },
      { label: 'fs6', size: '24', lineHeight: '32' },
    ],
  },

  'Font Family': {
    styles: [
      { label: 'fSerif', fontFamily: styleVar.fSerif },
      { label: 'fSansBld', fontFamily: styleVar.fSansBld },
      { label: 'fSansReg', fontFamily: styleVar.fSansReg },
      { label: 'fSansMed', fontFamily: styleVar.fSansMed },
      { label: 'fSans', fontFamily: styleVar.fSans },
    ],
  },
}

interface Props {
  handleClose: () => void
  onStyleApply: (styles: {}) => void
  styles?: {}
  isOpen: boolean
  isBgColorShow?: boolean
}

function StylingModal(props: Props) {
  const {
    isOpen,
    handleClose,
    onStyleApply,
    styles,
    isBgColorShow = false,
  } = props
  const [data, setData] = useState<any>({})
  const [showColorPicker, setShowColorPicker] = useState(false)
  const [selectedColor, setSelectedColor] = useState('')

  useEffect(() => {
    setData((pre) => {
      return {
        ...pre,
        ...styles,
      }
    })
  }, [styles])

  const handleApplyStyles = (key: string, value: any) => {
    if (key === 'fontSize') {
      value = JSON.parse(value)
    }
    const dataToUpdate = {
      ...data,
      [key]: value,
    }
    setData(dataToUpdate)
  }

  const handleApply = () => {
    onStyleApply(data)
  }

  return (
    <>
      <Modal
        onClose={handleClose}
        isShown={isOpen}
        shouldCloseOnOverlayClick={false}
        shouldCloseOnEscapePress={false}
        className='style-picker'
      >
        {() => (
          <>
            <Modal.Header
              className='m-header'
              title='Specify Styles'
              onClose={handleClose}
            />
            <Modal.Content
              style={{
                paddingBottom: '20px',
              }}
            >
              <Form>
                <FormControl
                  id='dv-pri-font-family'
                  className='w-100 fieldsFormControl'
                >
                  <div className='SwithWithTooltip'>
                    <FormControl.Label>Font Family</FormControl.Label>
                    <Tooltip
                      placement='top'
                      id='dv-pri-font-family-tooltip'
                      content='Specify font family'
                    >
                      <Info />
                    </Tooltip>
                  </div>
                  <Select
                    id='dv-pri-font-family-controlled'
                    name='dv-pri-font-family-controlled'
                    value={data.fontFamily || ''}
                    onChange={(e) =>
                      handleApplyStyles('fontFamily', e.target.value)
                    }
                  >
                    <Select.Option value='' isDisabled>
                      Please select an option...
                    </Select.Option>
                    {StyleList?.['Font Family']?.styles.map((option) => (
                      <Select.Option
                        value={
                          option.fontFamily /* { JSON.stringify({size: option.size, lineHeight: option.lineHeight }) */
                        }
                      >
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </FormControl>
                <FormControl
                  id='dv-pri-font-size'
                  className='w-100 fieldsFormControl'
                >
                  <div className='SwithWithTooltip'>
                    <FormControl.Label>Font Size</FormControl.Label>
                    <Tooltip
                      placement='top'
                      id='dv-pri-font-size-tooltip'
                      content='Specify font size'
                    >
                      <Info />
                    </Tooltip>
                  </div>
                  <Select
                    id='dv-pri-font-size-controlled'
                    name='dv-pri-font-size-controlled'
                    value={data.fontSize || ''}
                    onChange={(e) =>
                      handleApplyStyles('fontSize', e.target.value)
                    }
                  >
                    <Select.Option value='' isDisabled>
                      Please select an option...
                    </Select.Option>
                    {StyleList?.['Font Sizes']?.styles.map((option) => (
                      <Select.Option value={option.size}>
                        {option.label}
                      </Select.Option>
                    ))}
                  </Select>
                </FormControl>
                <FormControl
                  id='dv-font-color'
                  className='w-100 fieldsFormControl'
                >
                  <div className='SwithWithTooltip'>
                    <FormControl.Label>font color</FormControl.Label>
                    <Tooltip
                      placement='top'
                      id='dv-font-color-tooltip'
                      content='Pick a color for the font.'
                    >
                      <Info />
                    </Tooltip>
                  </div>
                  <div
                    className={`bg edit`}
                    onClick={() => {
                      setShowColorPicker(!showColorPicker)
                      setSelectedColor('fontColor')
                    }}
                  >
                    {data.fontColor ? (
                      <div
                        className='color'
                        style={{
                          backgroundColor: data.fontColor,
                        }}
                      ></div>
                    ) : (
                      <span>Pick</span>
                    )}
                  </div>
                </FormControl>
                {isBgColorShow && (
                  <FormControl
                    id='dv-bg-color'
                    className='w-100 fieldsFormControl'
                  >
                    <div className='SwithWithTooltip'>
                      <FormControl.Label>Bg & Line color</FormControl.Label>
                      <Tooltip
                        placement='top'
                        id='dv-font-color-tooltip'
                        content='Pick a color for the Background.'
                      >
                        <Info />
                      </Tooltip>
                    </div>
                    <div
                      className={`bg edit`}
                      onClick={() => {
                        setShowColorPicker(!showColorPicker)
                        setSelectedColor('bgColor')
                      }}
                    >
                      {data.bgColor ? (
                        <div
                          className='color'
                          style={{
                            backgroundColor: data.bgColor,
                          }}
                        ></div>
                      ) : (
                        <span>Pick</span>
                      )}
                    </div>
                  </FormControl>
                )}
              </Form>
            </Modal.Content>
            <Modal.Controls>
              <Button size='small' variant='transparent' onClick={handleClose}>
                Close
              </Button>
              <Button size='small' variant='positive' onClick={handleApply}>
                Create
              </Button>
            </Modal.Controls>
          </>
        )}
      </Modal>

      <ColorPickerPopup
        isOpen={showColorPicker}
        handleClose={() => {
          setShowColorPicker(false)
        }}
        selectedColors={data?.[selectedColor]}
        onColorPick={(color: any) => {
          handleApplyStyles(selectedColor, color.value)
          setShowColorPicker(false)
        }}
      />
    </>
  )
}

export default StylingModal
