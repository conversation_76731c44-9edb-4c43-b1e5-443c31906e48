export const playerFields = {
  Cohost: ['heading', 'contentId', 'image', 'videoFile', 'description', 'htmlAttributes'],
  Transistor: ['heading', 'contentId', 'image', 'videoFile', 'description', 'htmlAttributes'],
  YouTube: ['heading', 'contentId', 'image', 'videoFile', 'description', 'videoSettings', 'htmlAttributes'],
  'Carousel Player': [
    'heading',
    'contentId',
    'image',
    'videoFile',
    'description',
    'htmlAttributes'
  ],
  Wistia: ['contentId', 'heading', 'description', 'height', 'htmlAttributes'],
  Iframe: ['contentId', 'heading', 'height', 'maxWidth', 'htmlAttributes'],
  Cvent: ['contentId', 'height', 'maxWidth', 'htmlAttributes'],
  'Calconic - Development feasibility': ['contentId', 'htmlAttributes'],
  'Calconic - Property Tax': ['contentId', 'htmlAttributes'],
  EstateMaster: ['contentId', 'htmlAttributes'],
  'ARGUS EstateMaster - All products': ['contentId', 'htmlAttributes'],
  Trinity: ['contentId', 'htmlAttributes'],
  Qualtrics: ['contentId', 'htmlAttributes'],
  'Auto Download': ['contentId', 'htmlAttributes'],
  'Microsoft My Bookings': ['contentId', 'htmlAttributes'],
  'Apple Podcast Player': ['contentId', 'isLightMode', 'htmlAttributes'],
  "Altus Video Immersive": ['videoFile', 'heading', 'description', 'height', 'htmlAttributes'],
  "Altus Bg Video": ['videoFile', 'videoSettings', 'htmlAttributes'],
  "Altus Video Player": ['videoFile', 'image', 'videoSettings', 'hasShadow', 'htmlAttributes'],
}
