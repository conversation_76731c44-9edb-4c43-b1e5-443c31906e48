import {
  FormControl,
  Tabs,
  TextInput,
  Tooltip,
} from '@contentful/f36-components'
import React from 'react'
import Info from '../../../../assets/icons/Info'
import { ComponentProps } from '../../interface'

function Redirects(props: ComponentProps) {
  return (
    <Tabs.Panel id='redirects' className='tabPanelRoot'>
      <FormControl
        id='original-domain'
        style={{
          padding: '1rem',
          paddingBottom: '0rem',
          paddingLeft: '0rem',
        }}
      >
        <div className='formLabelWithIcon'>
          <FormControl.Label>Redirects File</FormControl.Label>
          <Tooltip
            placement='top'
            id='tooltip-1'
            content='Add scripts to the footer of the website.'
          >
            <Info />
          </Tooltip>
        </div>
        <TextInput
          size='medium'
          placeholder='Legal Documents'
          type='file'
          multiple={false}
        />
      </FormControl>
    </Tabs.Panel>
  )
}

export default Redirects
