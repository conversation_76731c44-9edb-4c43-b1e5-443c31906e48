import { EditorAppSDK } from '@contentful/app-sdk'
import axios from 'axios'
import _ from 'lodash'
import {
	getEnFieldData,
	getObject,
	replaceSymbols,
	reverseSymbols,
	setObject,
} from '../components/Translations/utils'

const url = `${process.env.REACT_APP_BASE_URL}/api/translate-text/`
const ignoreKeys = ['slug', 'htmlAttr', 'htmlAttributes']
const defaultLocale = 'en-CA'
const defaultLocaleCode = defaultLocale.split('-')[0]

const translateAndSetValue: Function = async (
  locale: string,
  data: any,
  key: string,
  type: string,
  defaultData: any,
  sdk: EditorAppSDK
) => {
  if (_.isEmpty(data)) {
    return true
  }

  const postData = data.filter((elem: string) => !_.isEmpty(elem))

  let checkData =
    !_.isEmpty(postData) && postData.map((elem: string) => replaceSymbols(elem))
  if (_.isEmpty(checkData)) {
    return true
  }

  const response = await axios.post(
    url,
    {
      source_language: defaultLocaleCode,
      target_language: locale.split('-')[0],
      text_array: checkData,
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-vercel-protection-bypass': `${process.env.REACT_APP_VERCEL_BYPASS_SECRET}`,
      },
    }
  )
  if (!response) return false
  const translatedData = response.data.message.map((elem: any) =>
    reverseSymbols(elem.translatedText)
  )
  if (type == 'Object') {
    const translatedDataResult = setObject(defaultData, translatedData)
    sdk.entry.fields[key].setValue(translatedDataResult, locale)
  } else if (type == 'Array') {
    sdk.entry.fields[key].setValue(translatedData, locale)
  } else {
    sdk.entry.fields[key].setValue(translatedData.join(''), locale)
  }

  return true
}

export const HandleGroupTranslate = async (
  sdk: EditorAppSDK,
  selectedLocale: string,
  entries: any,
  fieldsToTranslate?: any
) => {
  try {
    const allFields = await getEnFieldData(sdk.ids.entry)
    let res

    for (const { key, value } of entries) {
      if (fieldsToTranslate && !fieldsToTranslate.includes(key)) continue

      const data = allFields?.fields?.[key]

      if (value.locales.includes(selectedLocale) && !ignoreKeys.includes(key)) {
        if (value.type === 'Object') {
          if (!_.isEmpty(data)) {
            const result = getObject(data)

            res = await translateAndSetValue(
              selectedLocale,
              result,
              key,
              value.type,
              data,
              sdk
            )
          }
        } else if (value.type === 'Array') {
          if (!_.isEmpty(data)) {
            res = await translateAndSetValue(
              selectedLocale,
              data,
              key,
              value.type,
              data,
              sdk
            )
          }
        } else {
          if (!_.isEmpty(data)) {
            const textArray = [data]
            const keysArray = [key]
            res = await translateAndSetValue(
              selectedLocale,
              textArray,
              keysArray[0],
              value.type,
              data,
              sdk
            )
          }
        }
      }
    }

    if (res) {
      let x: { [key: string]: string | boolean } =
        sdk.entry.fields?.['configurations']?.getValue()
      x = {
        ...x,
        isTranslated: true,
      }
      sdk.entry.fields?.['configurations']?.setValue(x)
      sdk.entry.save()
    }

    // }
    // window.location.reload();
  } catch (error: any) {}
}

export const categoryFieldKeyToTranslate = {
  General: ['authorsHeading'],
  SEO: ['seoTitle', 'seoDescription'],
  AFS: ['afsCardTitle', 'afsDescription'],
  Search: ['shortTitle'],
}
