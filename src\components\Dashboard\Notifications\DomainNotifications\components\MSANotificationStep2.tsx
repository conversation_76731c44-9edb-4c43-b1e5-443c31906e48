import {
  <PERSON>,
  <PERSON><PERSON>,
  Select,
  Switch,
  <PERSON><PERSON><PERSON>,
} from '@contentful/f36-components'
import { LinkIcon } from '@contentful/f36-icons'
import React from 'react'
import Info from '../../../../../assets/icons/Info'
import FormControlComp from '../../../../Shared/FormControlComp'
import CustomEntryCard from '../../components/CustomEntryCard'
import { BgColors } from '../../utils/constants'
import {
  DomainNotiState,
  UpdateNotiStateFunction,
} from '../../utils/notifications.interface'

/**
 * Step 2 of the notification creation flow for domain notifications.
 *
 * This step allows the user to configure the notification settings, such as
 * whether the notification is enabled, closable, and whether it is site wide.
 *
 * Additionally, the user can select specific pages and categories to show the
 * notification on.
 *
 * @param specificPageRemove - Function to remove a specific page
 * @param specificPageSelector - Function to select a specific page
 * @param categoriesPageRemove - Function to remove a categories page
 * @param categoriesPageSelector - Function to select a categories page
 * @param state - The current state of the notification
 * @param updateState - Function to update the state of the notification
 * @returns A JSX element representing the second step of the notification creation flow
 */
function MSANotificationStep2({
  specificPageRemove,
  specificPageSelector,
  categoriesPageRemove,
  categoriesPageSelector,
  state,
  updateState,
}: {
  specificPageRemove: any
  specificPageSelector: any
  categoriesPageRemove: any
  categoriesPageSelector: any
  state: DomainNotiState
  updateState: UpdateNotiStateFunction<DomainNotiState>
}) {
  const {
    bgColor,
    isClosable,
    isEnabled,
    isGloballyEnabled,
    specificPages,
    categoriesPages,
    stickyNotificationTemplate,
  } = state

  return (
    <Box className='flex w-full flex-col justify-start items-start '>
      {/*<Box className="w-full py-4">*/}
      {/*  <h6 className="my-2">Settings</h6>*/}
      {/*  <hr className="endHorizontalLine w-full" />*/}
      {/*</Box>*/}
      <Box className='flex justify-start items-start w-full h-full gap-5 py-4'>
        <Box className='flex flex-col justify-start items-start w-full gap-5'>
          <FormControlComp tooltip=''>
            <Switch
              name='allow-cookies-controlled'
              id='allow-cookies-controlled'
              isChecked={isGloballyEnabled}
              onChange={() =>
                updateState({ isGloballyEnabled: !isGloballyEnabled })
              }
              className='switchNoPadding'
            >
              Is Site Wide
              <Tooltip
                placement='right'
                content={'Is notification site wide?'}
                className='cursor-pointer flex items-center'
              >
                <Info />
              </Tooltip>
            </Switch>
          </FormControlComp>
          <FormControlComp tooltip=''>
            <Switch
              name='allow-cookies-controlled'
              id='allow-cookies-controlled'
              isChecked={isEnabled}
              onChange={() => updateState({ isEnabled: !isEnabled })}
              className='switchNoPadding '
            >
              Is Enabled
              <Tooltip
                placement='right'
                content={'Is notification enabled?'}
                className='cursor-pointer flex items-center'
              >
                <Info />
              </Tooltip>
            </Switch>
          </FormControlComp>
          <FormControlComp tooltip=''>
            <Switch
              name='allow-cookies-controlled'
              id='allow-cookies-controlled'
              isChecked={isClosable}
              onChange={() => updateState({ isClosable: !isClosable })}
              className='switchNoPadding'
            >
              Is Closable
              <Tooltip
                placement='right'
                content={'Is notification closable by user?'}
                className='cursor-pointer flex items-center'
              >
                <Info />
              </Tooltip>
            </Switch>
          </FormControlComp>
          {stickyNotificationTemplate !== 'Carousel' && (
            <Box className='flex w-full justify-start items-start'>
              <FormControlComp label='Background Color' tooltip=''>
                <Select
                  value={bgColor}
                  onChange={(e) => updateState({ bgColor: e?.target?.value })}
                >
                  {BgColors.map((option) => (
                    <Select.Option value={option.value}>
                      {option.title}
                    </Select.Option>
                  ))}
                </Select>
              </FormControlComp>
            </Box>
          )}
        </Box>
        {stickyNotificationTemplate !== 'Carousel' && (
          <Box className='flex w-full justify-start items-center flex-col gap-5'>
            <FormControlComp
              label='Specific Page'
              tooltip='Select specific page to show notification'
            >
              <Box className='flex flex-wrap gap-3 w-full'>
                {specificPages?.map((specificPage: any, index: number) => {
                  return (
                    <CustomEntryCard
                      key={index}
                      data={specificPage}
                      onRemoveEntry={specificPageRemove}
                      field={'formFloating'}
                    />
                  )
                })}
              </Box>
              <Box className='h-1' />
              <Button
                onClick={specificPageSelector}
                variant='secondary'
                startIcon={<LinkIcon />}
                size='small'
              >
                Link existing entry
              </Button>
            </FormControlComp>

            <FormControlComp
              label='Categories Page'
              tooltip='Select categories page to show notification'
            >
              <Box className='flex flex-wrap gap-3 w-full'>
                {categoriesPages?.map((categoriesPage: any, index: number) => {
                  return (
                    <CustomEntryCard
                      key={index}
                      data={categoriesPage}
                      onRemoveEntry={categoriesPageRemove}
                      field={'formFloating'}
                    />
                  )
                })}
              </Box>
              <Box className='h-1' />
              <Button
                onClick={categoriesPageSelector}
                variant='secondary'
                startIcon={<LinkIcon />}
                size='small'
              >
                Link existing entry
              </Button>
            </FormControlComp>
          </Box>
        )}
      </Box>
    </Box>
  )
}

export default MSANotificationStep2
