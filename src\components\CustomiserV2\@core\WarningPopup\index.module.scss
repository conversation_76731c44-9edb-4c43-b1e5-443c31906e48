.overlayContainer {
  position: absolute;
  background: rgba(0, 0, 0, 0.25);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 7;
  backdrop-filter: blur(0.5rem);
  display: flex;
  justify-content: center;
  align-items: center;

}

.modalContent {
  height: 75%;
  width: 75%;
  background: white;
  border-radius: 10px;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
  padding: 20px;
  position: relative;
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  .modalHeader{
    display: flex;
    justify-content: space-between;
    align-items: start;
  }

  .modalClose {
    //position: absolute;
    //right: 5px;
    //top: 5px;
    margin-top: -8px;
    margin-right: -10px;

      &:hover {
        svg {
          fill: #e2222b;
        }
      }
  }

  .modalFooter {
      margin-top: 20px;
    text-align: center;

    .modalInnerContent {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
    }

    .modalBtns {
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 10px;

      .applyBtn, .discardBtn {
        color: #fff;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        //border-radius: 5px;
        padding: 0 10px;
        height: 30px;
        font-size: 14px;
        opacity: 1;
      }

      .applyBtn {
        background-color: #00ad23;
      }

      .discardBtn {
        border: 1px solid #e2222b;
        color: #e2222b;
      }
    }
  }
}

.textEllipsis {
  text-overflow: ellipsis;
  text-wrap: nowrap;
  overflow: hidden;
}