import React from 'react'
import { BsExclamationTriangle } from 'react-icons/bs'
import { GrClose } from 'react-icons/gr'
import { useSelector } from 'react-redux'
import { deleteAsset, SaveConfigurationandData } from '../../../../../globals/utils'
import { deleteHostedFile, updateHostedFileState } from '../../../../../redux/slices/dashboard/hostedFiles'
import { RootState, useAppDispatch } from '../../../../../redux/store'
import { Button, Popconfirm, Tooltip } from '../../../../atoms'
import './index.scss'

const DeleteHostedFileModal = ({
  id,
  HostedFileData,
  setIsButtonDisabled,
}: {
  id?: string
  HostedFileData?: HostedFile
  setIsButtonDisabled: Function
}) => {
  const state = useSelector((state: RootState) => state.hostedFiles)
  const dispatch = useAppDispatch()
  //   const [isVisible, setIsVisible] = React.useState<boolean>(false)
  const handleConfirm = async () => {
    if (!HostedFileData?.assetId || !id || !state?.id) {
      return
    }
    try {
      // console.log({ id, HostedFileData, Dele: 'delete beta' })
      await deleteAsset(HostedFileData?.assetId)
    } catch (error) {
      console.log(error)
    }
    const res = deleteHostedFile(state, id)
    // const res = updateHostedFile(state, payload)
    await SaveConfigurationandData([...res.dataSource], state?.id, true)
    dispatch(updateHostedFileState([...res.dataSource]))
    setIsButtonDisabled(false)
  }
  return (
    <>
      <Popconfirm
        title={
        <b style={{fontSize: '13px'} }>Permanently remove this file?</b>
      }
        //description={
        //  <p
        //    //style={{
        //    //  marginTop: '10px',
        //    //  marginBottom: '7px',
        //    //}}
        //  >
        //    Click yes to delete.
        //  </p>
        //}
        okText='Yes'
        okButtonProps={{
          type: 'primary',
          className: `bg-neutral-950`,
        }}
        styles={{
          body: {
            padding: '15px',
          },
        }}
        align={{}}
        placement='topLeft'
        icon={<BsExclamationTriangle size={20} className='mr-2  bg-ac' />}
        cancelText='No'
        cancelButtonProps={{
          type: 'default',
          //className: 'bg-neutral-950',
        }}
        onConfirm={handleConfirm}
      >
        <Tooltip title={`Remove this file`}>
        <Button type='primary' className={'text-neutral-950 bg-transparent'}>

          <GrClose size={'20'}/>
          {/* Delete Document */}
        </Button>
        </Tooltip>
      </Popconfirm>
      {/* <div className={'bg-neutral-950'}></div> */}
    </>
  )
}

export default DeleteHostedFileModal