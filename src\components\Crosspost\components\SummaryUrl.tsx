import React from 'react'
import { CrosspostJson } from '../../../globals/types'
import CrosspostDetailTable from '../../PageSettings/components/Crosspost/CrosspostDetailTable.tsx'

interface Props {
  summaryProp: CrosspostJson
  isSummaryView: boolean
}

/**
 * SummaryUrl renders a summary of the crossposting domains and their slugs
 * selected by the user. It also shows the primary CTA for insights articles
 * @param {Props} props component props
 * @returns {JSX.Element}
 */
function SummaryUrl(props: Props) {
  const { summaryProp, isSummaryView } = props

  return (
    <div className='flex flex-col p-4 h-full overflow-y-scroll'>
      <h6 className='mt-0 mb-3'>Summary</h6>
      <p className='text-[14.5px] pb-2.5'>
        This Page will be crossposted to the undermentioned websites.
        <br /> If everything looks good, click Apply to save. You can always go
        back to update / amend.
      </p>

      <CrosspostDetailTable
        jsonData={summaryProp}
        isSummaryView={isSummaryView}
      />
    </div>
  )
}

export default SummaryUrl
