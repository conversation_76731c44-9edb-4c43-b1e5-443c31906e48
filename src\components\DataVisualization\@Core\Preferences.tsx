import { Tabs } from '@contentful/f36-components'
import React from 'react'
import '../index.scss'
import DVAxes from './Axes'
import DvGeneral from './DvGenerals'
import DvGrid from './DvGrid'
import DvLegend from './DvLegend'
import DvMarkLine from './DvMarkLine'
import DVStyles from './DvStyles'

export default function Preferences(props) {
  const {
    onDataChange,
    currentTab,
    setCurrentTab,
    template,
    asset,
    changedData,
    isGlobal,
    setDataLoading,
    dataLoading,
  } = props
  
  const DVPreferences = {
    title: 'Private',
    value: 1,
    catFields: [
      { title: 'General', value: 'dv-general' },
      { title: 'Styles', value: 'dv-styles' },
      { title: 'Axes', value: 'dv-axes' },
      { title: isGlobal ? 'Legend/Asset' : 'Legend', value: 'dv-chart' },
      { title: 'Grid', value: 'dv-grid' },
      { title: 'MarkLine', value: 'dv-mark' },
    ],
  }
  
  const handleDvConfigJsonDataChange = (key: string, data: any) => {
    onDataChange(key, data)
  }

  return (
    <>
      <div className={'customiserRoot'}>
        <div className={'tabsRoot'} style={{ width: '100%' }}>
          <Tabs
            currentTab={currentTab}
            onTabChange={setCurrentTab}
            style={{
              width: '100%',
            }}
          >
            <Tabs.List
              variant='horizontal-divider'
              style={{ marginBottom: '20px' }}
              className={'tab-list'}
            >
              {DVPreferences?.catFields?.map((item, index) => {
                return (
                  !(isGlobal && item.title === 'MarkLine') && (
                    <Tabs.Tab key={index} panelId={item.value}>
                      {item.title}
                    </Tabs.Tab>
                  )
                )
              })}
            </Tabs.List>
            <DvGeneral
              onChange={(data: any) =>
                handleDvConfigJsonDataChange('general', data)
              }
              changedData={changedData?.['general']}
              template={template}
              isGlobal={isGlobal}
            />
            <DVAxes
              onChange={(data: any) =>
                handleDvConfigJsonDataChange('axes', data)
              }
              changedData={changedData?.['axes']}
              template={template}
              isGlobal={isGlobal}
            />

            <DVStyles
              onChange={(data: any) =>
                handleDvConfigJsonDataChange('styles', data)
              }
              changedData={changedData?.['styles']}
              template={template}
            />
            <DvGrid
              onChange={(data: any) =>
                handleDvConfigJsonDataChange('grid', data)
              }
              changedData={changedData?.['grid']}
              template={template}
            />
            <DvLegend
              onChange={(data: any) =>
                handleDvConfigJsonDataChange('chart', data)
              }
              changedData={changedData?.['chart']}
              isGlobal={isGlobal}
              asset={asset}
              axesData={changedData?.['axes']}
              template={template}
              setDataLoading={setDataLoading}
              dataLoading={dataLoading}
            />
            {!isGlobal && (
              <DvMarkLine
                onChange={(data: any) =>
                  handleDvConfigJsonDataChange('mark', data)
                }
                changedData={changedData?.['mark']}
                chartData={changedData?.['chart']}
                axisData={changedData?.['axes']}
                template={template}
                asset={asset}
                isGlobal={false}
              />
            )}
          </Tabs>
        </div>
      </div>
    </>
  )
}

