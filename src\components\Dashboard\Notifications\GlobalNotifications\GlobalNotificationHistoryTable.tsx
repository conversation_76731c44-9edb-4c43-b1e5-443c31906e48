import { TextInput } from '@contentful/f36-components'
import { Button, Modal, notification, Table, TableProps, Tooltip } from 'antd'
import React, { useEffect, useState } from 'react'
import { BiSolidTrashAlt } from 'react-icons/bi'
import { FaPlus } from 'react-icons/fa6'
import { LuSend } from 'react-icons/lu'
import { generateRandomId, sendMessage } from '../../../../globals/firebase/utils'
import { getDomainDataByDomainName, SaveConfigurationandData } from '../../../../globals/utils'
import {
  fetchNotificationHistory,
  setIsAddNewNotification,
} from '../../../../redux/slices/dashboard/notificationsSlice'
import { useAppDispatch, useAppSelector } from '../../../../redux/store'
import { domainsConfig } from '../../../Crosspost/utils'
import PushNotificationDetailedView from '../components/PushNotificationDetailedView'
import {
  DELETE_NOTIFICATION_BODY,
  DELETE_NOTIFICATION_HEAD,
  DELETE_NOTIFICATION_SUCCESS,
  DELETE_NOTIFICATION_TOOLTIP,
  RESEND_NOTIFICATION_BODY,
  RESEND_NOTIFICATION_HEAD,
  RESEND_NOTIFICATION_SUCCESS,
  RESEND_NOTIFICATION_TOOLTIP,
} from '../components/constants'
import { nonHttpsHandler } from '../utils'

function GlobalNotificationHistoryTable() {

  const { dataSource, id } = useAppSelector(state => state.notificationData)

  const [globalNotifications, setGlobalNotifications] = React.useState([])

  const dispatch = useAppDispatch()

  const [search, setSearch] = useState('')

  useEffect(() => { // fetch notification history
    dispatch(fetchNotificationHistory('MSA'))
  }, [])

  useEffect(() => {
    // filter global notifications
    if (dataSource?.global?.realTime) {
      if (search !== '') {
        setGlobalNotifications(
          dataSource?.global?.realTime?.filter((item: any) =>
            item.title.toLowerCase().includes(search.toLowerCase()),
          ),
        )
      } else {
        setGlobalNotifications(dataSource?.global?.realTime || [])
      }
    }
  }, [search, dataSource])


  /**
   * Deletes a notification from global history, and updates the local state.
   * @param {string} notiId - The id of the notification to delete.
   */
  const handleDeleteNotificationById = async (notiId: string) => {
    const payload = {
      ...dataSource,
      'global': {
        ...dataSource?.['global'],
        'realTime': dataSource?.['global']?.['realTime'].filter((item: any) => item.id !== notiId),
      },
    }

    await SaveConfigurationandData(payload, id ? id : '')

    notification.success({
      message: DELETE_NOTIFICATION_SUCCESS,
      placement: 'topRight',
      duration: 5,
      showProgress: true,
    })
    dispatch(fetchNotificationHistory('MSA'))
  }


  /**
   * Confirmation modal to delete a notification from global history.
   * @param {string} notiId - The id of the notification to delete.
   */
  const deleteModal = (notiId: string) => {
    Modal.confirm({
      title: DELETE_NOTIFICATION_HEAD,
      content: <p>{DELETE_NOTIFICATION_BODY}</p>,
      async onOk() {
        try {
          await handleDeleteNotificationById(notiId)
          Modal.destroyAll()
        } catch (error) {
          console.error('Deletion failed:', error)
          // Optionally throw to prevent modal from closing
          throw error
        }
      },
    })
  }


  /**
   * Confirmation modal to re-send a notification from global history.
   * @param {Object} data - The notification data to re-send.
   */
  const reSendModal = (data: any) => {
    Modal.confirm({
      title: RESEND_NOTIFICATION_HEAD,
      content: <p>{RESEND_NOTIFICATION_BODY}</p>,
      async onOk() {
        try {
          await reSendNotification(data)
          Modal.destroyAll()
        } catch (error) {
          console.error('Deletion failed:', error)
          throw error
        }
      },
    })
  }


  /**
   * Resends a notification from global history to all domains.
   * @param {Object} data - The notification data to re-send.
   * @returns {Promise<void>}
   */
  const reSendNotification = async (data: any) => {
    const domainsArray = domainsConfig
      .filter((d) => d.domainKey !== 'domainOne11Com')
      .map((d) => d.key)

    let payload = {...data}
    const promises = domainsArray.map(async (domain) => {
      payload.url = nonHttpsHandler({ url: payload?.url, domain })
      return await sendMessage({
        ...payload,
        timeStamp: new Date(),
        id: generateRandomId(),
      }, getDomainDataByDomainName(domain)?.domainKey)
    })

    const results = await Promise.all(promises)

    const allSuccessful = results.every((res) => res?.message !== undefined)

    if (allSuccessful) {
      notification.success({
        message: RESEND_NOTIFICATION_SUCCESS,
        placement: 'topRight',
        duration: 5,
        showProgress: true,
      })
    }
  }

  // columns for global notifications
  const columns: TableProps['columns'] = [
    {
      title: '',
      dataIndex: 'index',
      key: 'name',
      width: '10%',
      render: (_, __, index) => index + 1,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      sorter: (a, b) =>
        a.title.localeCompare(b.title),
      width: '40%',
    },
    {
      title: 'Description',
      dataIndex: 'body',
      key: 'body',
      sorter: (a, b) =>
        a.body.localeCompare(b.body),
      width: '40%',
    },
    {
      title: 'Action',
      key: 'action',
      width: '10%',
      render: (_, record) => (
        <div className={'flex italic'}>
          <PushNotificationDetailedView noti={record} />
          <Tooltip title={RESEND_NOTIFICATION_TOOLTIP}>
            <Button
              size={'large'}
              type="primary"
              className="text-neutral-950 bg-transparent border-none"
              icon={<LuSend />}
              onClick={() => {
                reSendModal(record)
              }}
            />
          </Tooltip>
          <Tooltip title={DELETE_NOTIFICATION_TOOLTIP}>
            <Button
              size={'large'}
              type="primary"
              onClick={() => deleteModal(record.id)}
              className="text-neutral-950 bg-transparent border-none"
              icon={<BiSolidTrashAlt />} />
          </Tooltip>
        </div>
      ),
    },
  ]


  return (
    <div className={'w-full flex flex-col py-10 px-2'}>
      <div className="w-full flex flex-col xl:flex-row gap-3">
        <div
          className="w-full xl:w-2/3 flex flex-col lg:flex-row items-start lg:items-center justify-start mb-0 xl:mb-5 gap-3">
          <h3>Global Notifications</h3>
          <div className="flex justify-center items-center gap-3">
            <Button
              type="primary"
              onClick={() => {
                dispatch(setIsAddNewNotification(true))
              }}
            >
              <FaPlus /> Create Notification
            </Button>
          </div>
        </div>
        <div className="w-1/3 flex justify-between mb-5">
          <TextInput
            placeholder="Search"
            onChange={(e) => {
              setSearch(e?.target?.value ?? '')
            }}
          />
        </div>
      </div>
      <Table
        columns={columns}
        dataSource={globalNotifications}
        pagination={{
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
          defaultPageSize: 10,
          showQuickJumper: true,
          showPrevNextJumpers: true,
        }}
        size={'small'}
      />
    </div>
  )
}

export default GlobalNotificationHistoryTable
