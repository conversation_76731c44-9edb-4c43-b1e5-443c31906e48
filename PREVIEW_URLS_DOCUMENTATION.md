# PreviewUrls Component Documentation

## Overview

The `PreviewUrls` component is a Contentful sidebar widget that provides content editors with quick access to preview their pages across different deployment environments. It dynamically generates preview URLs based on the page's domain configuration and deployment branch, enabling seamless content preview and testing workflows.

## Component Location

- **File**: `src/components/Preview/index.tsx`
- **Type**: Contentful Sidebar App Component
- **Framework**: React with Contentful App SDK

## Key Features

### 🌐 Multi-Domain Support

- Supports multiple brand domains (Altus Group, Finance Active, Reonomy, Verifino, One11)
- Automatic domain detection from entry tags or configuration fields
- Domain-specific URL generation for accurate previews

### 🚀 Multi-Environment Preview

- **Preview Environment**: Development/staging branch preview
- **Production Environment**: Live production site preview
- Branch-aware URL generation with environment-specific logic

### 📱 Real-Time State Management

- Monitors entry publication status in real-time
- Tracks domain configuration changes
- Updates button states based on content availability

### 🔒 Intelligent Access Control

- Disables preview buttons when domain is not configured
- Restricts production preview to published content only
- Provides clear user feedback for missing configurations

## Architecture

### Component Structure

```
PreviewUrls Component
├── State Management
│   ├── isPagePublished (boolean)
│   └── pageDomain (string)
├── Event Listeners
│   ├── Entry System Changes
│   ├── Configuration Field Changes
│   └── Metadata/Tags Changes
├── URL Generation
│   ├── getDomainShortName()
│   └── getPreviewUrlByBranch()
└── UI Components
    ├── Preview Button
    ├── Production Button
    └── Error Messages
```

### Dependencies

- **Contentful SDK**: `@contentful/app-sdk` (SidebarAppSDK)
- **UI Components**: `@contentful/f36-components`
- **Environment Variables**: `ENV_VARIABLES` configuration
- **Utilities**: `getUpdatedBranchFrontName` from PageSettings

## Domain Configuration

### Supported Domains

The component supports five distinct brand domains:

| Domain Tag               | Short Code | Production URL                                | Description           |
| ------------------------ | ---------- | --------------------------------------------- | --------------------- |
| `domainAltusGroupCom`    | `agl`      | https://www.altusgroup.com/                   | Altus Group main site |
| `domainFinanceActiveCom` | `fia`      | https://financeactive.com/                    | Finance Active site   |
| `domainReonomyCom`       | `reo`      | https://reonomy.com/                          | Reonomy platform      |
| `domainVerifinoCom`      | `ver`      | https://verifino.com/                         | Verifino service      |
| `domainOne11Com`         | `o11`      | https://msa-o11-v3w-git-main-altus.vercel.app | One11 application     |

### Domain Detection Logic

The component uses a two-tier approach to determine the page domain:

1. **Primary**: Checks the `configurations.domain` field value
2. **Fallback**: Scans entry metadata tags for domain-prefixed tags

```javascript
// Domain detection from configuration field
let domain = value?.domain

// Fallback to metadata tags
if (!domain) {
  const pageTags = sdk.entry.getMetadata()?.tags.map((tag) => tag.sys.id)
  domain = pageTags?.find((tag) => tag.startsWith('domain'))
  if (domain) domain = getDomainShortName(domain)
}
```

## URL Generation System

### Preview URL Structure

The component generates different URL patterns based on the deployment environment:

#### Production URLs (main branch)

- Direct production domain URLs
- Used when `branch === 'main'`
- Domain-specific production endpoints

#### Preview URLs (development branches)

- Vercel deployment pattern: `https://msa-{domain}-v3w-git-{branch}-altus.vercel.app/`
- Dynamic branch and domain substitution
- Consistent naming convention across environments

### URL Generation Function

```javascript
export const getPreviewUrlByBranch = (branch: string, domain: string) => {
  if (branch === 'main') {
    // Return production URLs for main branch
    switch (domain) {
      case 'agl':
        return 'https://www.altusgroup.com/'
      case 'fia':
        return 'https://financeactive.com/'
      // ... other domains
    }
  } else {
    // Return preview URLs for development branches
    return `https://msa-${domain}-v3w-git-${branch}-altus.vercel.app/`
  }
}
```

## State Management

### Publication Status Tracking

```javascript
useEffect(() => {
  sdk.entry.onSysChanged(() => {
    const isDraft = sdk.entry.getSys().fieldStatus['*']['en-CA'] === 'draft'
    setIsPagePublished(!isDraft)
  })
}, [sdk])
```

### Domain Configuration Monitoring

```javascript
useEffect(() => {
  // Listen for configuration field changes
  const fieldUnsubscribe =
    sdk.entry.fields['configurations'].onValueChanged(handleFieldChange)

  // Listen for metadata tag changes
  const tagsUnsubscribe = sdk.entry.onMetadataChanged(handleTagsChange)

  return () => {
    fieldUnsubscribe()
    tagsUnsubscribe()
  }
}, [sdk])
```

## User Interface

### Button States

The component provides two main action buttons with intelligent state management:

#### Preview Button

- **Label**: "Open {BranchName}" (e.g., "Open Preview")
- **Enabled When**: Domain is configured
- **Disabled When**: No domain selected
- **Action**: Opens preview environment URL

#### Production Button

- **Label**: "Open {mainBranch}" (e.g., "Open main")
- **Enabled When**: Content is published AND domain is configured
- **Disabled When**: Content is unpublished OR no domain selected
- **Action**: Opens production environment URL

### Error Handling

```javascript
{
  !pageDomain && (
    <p style={{ fontSize: '0.75rem', color: '#ff1313' }}>
      Please select a domain tag
    </p>
  )
}
```

## Integration Points

### Environment Variables

- `ENV_VARIABLES.MainPreviewBranch`: Preview environment branch name
- `ENV_VARIABLES.mainBranch`: Production branch name

### Contentful SDK Integration

- **Entry Fields**: Accesses `slug` and `configurations` fields
- **Metadata**: Monitors entry tags for domain configuration
- **System Changes**: Tracks publication status changes

### External Dependencies

- **PageSettings Utils**: Uses `getUpdatedBranchFrontName` for branch name formatting
- **Global Context**: Integrates with application-wide state management

## Usage Examples

### Basic Implementation

```jsx
import PreviewUrls from './components/Preview'

// In Contentful sidebar app
;<PreviewUrls sdk={sdk} />
```

### URL Generation Usage

```javascript
import { getPreviewUrlByBranch } from './components/Preview'

// Generate preview URL
const previewUrl = getPreviewUrlByBranch('dev', 'agl')
// Result: "https://msa-agl-v3w-git-dev-altus.vercel.app/"

// Generate production URL
const prodUrl = getPreviewUrlByBranch('main', 'agl')
// Result: "https://www.altusgroup.com/"
```

## Error Scenarios

### Missing Domain Configuration

- **Symptom**: Both buttons disabled, error message displayed
- **Cause**: No domain tag or configuration field set
- **Resolution**: Add appropriate domain tag or set domain in configurations

### Unpublished Content

- **Symptom**: Production button disabled, preview button enabled
- **Cause**: Content exists in draft state
- **Resolution**: Publish the entry to enable production preview

### Invalid Domain Tag

- **Symptom**: Buttons disabled, no error message
- **Cause**: Domain tag doesn't match supported domains
- **Resolution**: Use valid domain tag from supported list

## Best Practices

### Content Editor Workflow

1. **Configure Domain**: Ensure page has proper domain tag or configuration
2. **Preview Development**: Use preview button to test changes in staging
3. **Publish Content**: Publish entry when ready for production
4. **Verify Production**: Use production button to confirm live deployment

### Developer Considerations

1. **URL Consistency**: Maintain consistent URL patterns across environments
2. **Error Handling**: Provide clear feedback for configuration issues
3. **State Synchronization**: Ensure real-time updates reflect content changes
4. **Domain Management**: Keep domain mappings updated with infrastructure changes

## Related Components

- **PageSettings**: Provides domain configuration interface
- **Cache Component**: Handles cache invalidation for preview URLs
- **Crosspost**: Manages multi-domain content distribution
- **Dashboard Notifications**: Uses similar URL generation patterns

## Technical Implementation Details

### Component Props Interface

```typescript
interface PreviewUrlsProps {
  sdk: SidebarAppSDK // Contentful Sidebar SDK instance
}
```

### Internal State Types

```typescript
// Publication status tracking
const [isPagePublished, setIsPagePublished] = React.useState<boolean>(false)

// Domain configuration tracking
const [pageDomain, setPageDomain] = React.useState<string>('')
```

### Event Handler Signatures

```typescript
// Configuration field change handler
const handleFieldChange = (value: any) => void

// Metadata tags change handler
const handleTagsChange = () => void

// Preview URL navigation handler
const handlePreviewUrlByBranch = (branch: string) => void
```

## Testing Considerations

### Unit Testing Scenarios

- **Domain Detection**: Test domain extraction from tags and configuration
- **URL Generation**: Verify correct URL patterns for all domains and branches
- **State Updates**: Ensure proper state changes on SDK events
- **Button States**: Validate button enable/disable logic

### Integration Testing

- **SDK Integration**: Test with actual Contentful SDK instances
- **Real-time Updates**: Verify live updates when entry changes
- **Cross-Domain**: Test URL generation across all supported domains
- **Publication Flow**: Test complete publish/unpublish workflows

### Manual Testing Checklist

- [ ] Preview button opens correct staging URL
- [ ] Production button opens correct live URL
- [ ] Buttons disabled when domain not configured
- [ ] Production button disabled for unpublished content
- [ ] Error message displays when domain missing
- [ ] Real-time updates work when entry changes
- [ ] URL generation works for all supported domains

## Performance Considerations

### Optimization Strategies

- **Event Listener Management**: Proper cleanup prevents memory leaks
- **State Updates**: Minimal re-renders through targeted state changes
- **URL Caching**: Consider caching generated URLs for repeated access
- **Async Operations**: Handle domain detection asynchronously

### Memory Management

```javascript
// Proper cleanup in useEffect
useEffect(() => {
  const fieldUnsubscribe =
    sdk.entry.fields['configurations'].onValueChanged(handleFieldChange)
  const tagsUnsubscribe = sdk.entry.onMetadataChanged(handleTagsChange)

  return () => {
    fieldUnsubscribe() // Prevent memory leaks
    tagsUnsubscribe() // Clean up event listeners
  }
}, [sdk])
```

## Security Considerations

### URL Safety

- **Domain Validation**: Only generate URLs for approved domains
- **Branch Sanitization**: Ensure branch names are safe for URL construction
- **Access Control**: Respect Contentful permissions for preview access

### Data Privacy

- **Slug Handling**: Ensure slug data doesn't contain sensitive information
- **URL Logging**: Be cautious about logging generated URLs with sensitive paths

## Troubleshooting Guide

### Common Issues

#### "Please select a domain tag" Error

**Cause**: No domain configuration found
**Solution**:

1. Add domain tag to entry metadata
2. Set domain field in configurations
3. Verify tag naming matches expected format

#### Preview Button Not Working

**Cause**: Domain detection failure or URL generation error
**Solution**:

1. Check browser console for JavaScript errors
2. Verify domain tag format matches `getDomainShortName` function
3. Confirm environment variables are properly configured

#### Production Button Disabled

**Cause**: Content not published or domain not configured
**Solution**:

1. Publish the entry if content is ready
2. Verify domain configuration is present
3. Check entry publication status in Contentful

### Debug Information

```javascript
// Add to component for debugging
console.log('Domain:', pageDomain)
console.log('Published:', isPagePublished)
console.log('Slug:', slug)
console.log('Preview Branch:', MainPreviewBranch)
console.log('Main Branch:', mainBranch)
```

## API Reference

### Exported Functions

#### `getPreviewUrlByBranch(branch: string, domain: string): string`

Generates preview URL based on branch and domain parameters.

**Parameters:**

- `branch`: Deployment branch name ('main', 'dev', etc.)
- `domain`: Domain short code ('agl', 'fia', etc.)

**Returns:** Complete preview URL string

**Example:**

```javascript
const url = getPreviewUrlByBranch('dev', 'agl')
// Returns: "https://msa-agl-v3w-git-dev-altus.vercel.app/"
```

#### `getDomainShortName(domain: string): string`

Converts full domain tag to short domain code.

**Parameters:**

- `domain`: Full domain tag ('domainAltusGroupCom', etc.)

**Returns:** Short domain code ('agl', 'fia', etc.)

**Example:**

```javascript
const shortName = getDomainShortName('domainAltusGroupCom')
// Returns: "agl"
```

## Future Enhancements

### Potential Improvements

- **Multi-Locale Support**: Generate locale-specific preview URLs
- **Custom Branch Selection**: Allow editors to preview specific feature branches
- **URL Validation**: Verify URL accessibility before opening
- **Preview History**: Track and display recently accessed preview URLs
- **Batch Operations**: Enable bulk preview operations for multiple entries
- **QR Code Generation**: Generate QR codes for mobile preview testing
- **Preview Screenshots**: Capture and display preview thumbnails
- **A/B Testing Integration**: Support for experimentation preview URLs
