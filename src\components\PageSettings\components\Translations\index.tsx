import { EditorAppSDK, SidebarAppSDK } from '@contentful/app-sdk'
import { Box, Button, Checkbox, Notification } from '@contentful/f36-components'
import _ from 'lodash'
import React, { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import {
  Languages,
  publishEntry,
  updateSingleEntryData,
} from '../../../../globals/utils'
import { useTabVisibility } from '../../../../hooks/useTabVisibility'
import { RootState } from '../../../../redux/store'
import UpcomingFeatComp from '../../../ConfigurationEngine/Components/UpcomingFeatComp'
import { domainsConfig, getLocaleFullName } from '../../../Crosspost/utils'
import FormControlComp from '../../../Shared/FormControlComp'
import '../../index.scss'
import TranslationStatusTable from './components/TranslationStatusTable'
import {
  extractEntryIds,
  generateEntryKeyValueFromFields,
  getEnFieldData,
  translateData,
} from './utils'

interface Props {
  sdk: EditorAppSDK | SidebarAppSDK
  pageDomainAllowedLocale: string[]
}

interface Status {
  entryId: string
  internalName: string
  status: 'pending' | 'done' | 'error' | 'N/A'
  type: string
  message?: string
}

function Translations(props: Props) {
  const { sdk, pageDomainAllowedLocale } = props

  const sdkFields = sdk.entry.fields

  const isTabFocused = useTabVisibility()

  const pageConfigurations = sdkFields?.['configurations']?.getValue() || {}

  const domainCode = pageConfigurations?.domain || 'agl'

  const selectedDomainObj = domainsConfig.find(
    (item) => item?.key === domainCode
  )

  const primaryLang = selectedDomainObj?.primaryLang || 'en-CA'

  const [loading, setLoading] = useState(false)

  const [rePublishLoading, setRePublishLoading] = useState(false)

  const [notificationToShow, setNotificationToShow] = useState({
    show: false,
    message: '',
    type: 'success',
  })

  const [selectedLocale, setSelectedLocale] = useState<string[]>(['en-CA'])

  const [entryTranslationStatus, setEntryTranslationStatus] = useState<
    Status[]
  >([])

  let processedEntryIds: string[] = []

  const referenceIds: Set<string> = new Set([])

  const contentTypesToExclude = ['page', 'componentForm']

  const contentTypesWithNoTranslation = [
    'componentLayoutRow',
    'componentLayoutColumn',
    'componentLayoutContainer',
    'componentNavigationHeader',
    'componentLogo',
    'formSection',
  ]

  const tableContainerRef: any = useRef(null)

  const allContentTypes: any = useSelector(
    (state: RootState) => state.pageData.contentTypes
  )

  const updateStatus = (x: Status) => {
    setEntryTranslationStatus((prevState) => {
      const oldData = [...prevState] // Ensure you're working with the latest state

      const index = oldData.findIndex((el) => el.entryId === x.entryId)
      if (index !== -1) oldData[index] = x
      else oldData.push(x)

      return oldData
    })
  }

  /**
   * Function to handle locale changes. If the locale is already selected,
   * it removes it from the list; otherwise, it adds it to the list.
   * @param {string} x - The locale to be toggled in the list of selected locales.
   */
  const handleOnclick = (x: string) => {
    if (selectedLocale.includes(x))
      setSelectedLocale(selectedLocale.filter((locale) => locale !== x))
    else setSelectedLocale([...selectedLocale, x])
  }

  /**
   * Function to update the set of referenceIds. It takes an array of IDs
   * and adds each one to the set. This is used to keep track of the IDs of
   * all the entries that need to be processed.
   * @param {string[]} ids - An array of IDs to be added to the set of referenceIds.
   */
  const updateReferenceIds = (ids: string[]) =>
    ids.forEach((id) => referenceIds.add(id))

  /**
   * Function to handle the click event on the "Translate" button. It:
   * 1. Resets the notification and translation status.
   * 2. Clears the set of referenceIds.
   * 3. Sets loading to true.
   * 4. Gets the ID of the current entry.
   * 5. Adds the ID to the set of referenceIds.
   * 6. Translates the entry and its nested entries.
   * 7. Saves the entry.
   * 8. Sets loading to false.
   * 9. Checks if any of the nested entries have failed to translate.
   * 10. Shows a notification based on the result.
   * 11. Clears the set of referenceIds and processedEntryIds.
   */
  const handleClick = async () => {
    setNotificationToShow({ show: false, message: '', type: 'success' })
    setEntryTranslationStatus([])
    referenceIds.clear()
    setLoading(true)

    const entryId = sdk.entry.getSys().id

    updateReferenceIds([entryId])

    const localEntryTranslationStatus: Status[] = []

    for (let i = 0; i < referenceIds.size; i++) {
      const array = Array.from(referenceIds)

      const id = array[i]
      await handleNestedTranslation(
        array[i],
        id === entryId,
        localEntryTranslationStatus
      )
    }

    sdk.entry.save()
    setLoading(false)

    const isFailed = localEntryTranslationStatus.some((x: any) => {
      return x.status === 'error'
    })

    if (isFailed) {
      setNotificationToShow({
        show: true,
        message: 'Some Nested Entries are not translated successfully',
        type: 'error',
      })
    } else {
      setNotificationToShow({
        show: true,
        message: 'All Nested Entries are translated successfully',
        type: 'success',
      })
    }

    referenceIds.clear()
    processedEntryIds = []
  }

  /**
   * Recursively translates an entry and its nested entries.
   * @param entryId The ID of the entry to translate.
   * @param isRootPage Whether the entry is the root page.
   * @param localEntryTranslationStatus An array to store the status of each entry.
   * @returns A boolean indicating whether the translation was successful.
   */
  const handleNestedTranslation = async (
    entryId: string,
    isRootPage: boolean,
    localEntryTranslationStatus: Status[]
  ) => {
    if (checkIsEntryProcessed(entryId)) return

    const entry = await getEnFieldData(entryId)

    const contentType = entry?.sys?.contentType?.sys?.id || ''

    if (contentTypesToExclude.includes(contentType) && !isRootPage) return

    let entryFields = entry?.fields

    const internalName = entryFields?.internalName

    let p: Status = {
      entryId,
      internalName,
      status: 'pending',
      type: contentType,
    }

    updateStatus(p)

    const entryContentModel = allContentTypes?.items?.find(
      (item: any) => item?.sys?.id === contentType
    )

    const newEntries = generateEntryKeyValueFromFields(entryFields)

    const contentConfigurations = entryFields?.['configurations'] || {}

    const isContentTranslated = contentConfigurations?.isTranslated || false

    const translatedFor = contentConfigurations?.translatedFor || []

    const nestedReferenceId = extractEntryIds(entry)

    updateReferenceIds(nestedReferenceId)

    if (contentTypesWithNoTranslation.includes(contentType)) {
      p = {
        ...p,
        status: 'N/A',
      }
      updateStatus(p)
      localEntryTranslationStatus.push(p)

      return true
    }

    if (
      isContentTranslated &&
      JSON.stringify(translatedFor) === JSON.stringify(selectedLocale)
    ) {
      p = {
        ...p,
        status: 'done',
      }
      updateStatus(p)
      localEntryTranslationStatus.push(p)

      return true
    }

    const translationPromises = selectedLocale.map(async (locale) => {
      if (locale === 'en-CA') return // Skip 'en-CA'

      return await translateData(
        locale,
        newEntries,
        entryFields,
        entryId,
        entryContentModel
      )
    })
    let res: any[]
    let finalRes = false
    let errorMsg = ''
    try {
      res = await Promise.all(translationPromises.filter(Boolean))
      res = res.filter((x) => x !== undefined)

      finalRes = res.every((x) => x.success === true)

      errorMsg = res[0].message

      await updateSingleEntryData(entryId, [
        {
          fieldId: 'configurations',
          locale: 'en-CA',
          value: {
            ...contentConfigurations,
            isTranslated: finalRes,
            translatedFor: selectedLocale,
          },
        },
      ])
    } catch (e) {}

    entryTranslationStatus.pop()

    p = {
      ...p,
      status: finalRes ? 'done' : 'error',
      message: errorMsg,
    }

    updateStatus(p)
    localEntryTranslationStatus.push(p)

    return finalRes
  }

  /**
   * Checks if an entry has been processed. If it has, returns true. Otherwise,
   * adds the entry ID to the list of processed entries and returns false.
   * @param id The ID of the entry to check.
   * @returns Whether the entry has been processed.
   */
  const checkIsEntryProcessed = (id: string) => {
    if (processedEntryIds.includes(id)) return true
    else {
      processedEntryIds.push(id)

      return false
    }
  }

  /**
   * Handles the republish button click.
   *
   * It clears all the IDs from the set of reference IDs and processed entry IDs,
   * sets the entry translation status to an empty array, sets the republish loading
   * to true, and then iterates over all the IDs in the set of reference IDs, calling
   * handleNestedPublish for each one. After all the IDs have been processed, it sets
   * the republish loading to false and checks if any of the nested entries have failed
   * to publish. If any have, it shows an error notification. Otherwise, it shows a
   * success notification.
   */
  const handleRepublish = async () => {
    setNotificationToShow({ show: false, message: '', type: 'success' })
    referenceIds.clear()
    processedEntryIds = []
    setEntryTranslationStatus([])
    setRePublishLoading(true)
    const localEntryPublishStatus: Status[] = []

    const entryId = sdk.entry.getSys().id

    updateReferenceIds([entryId])

    for (let i = 0; i < referenceIds.size; i++) {
      const array = Array.from(referenceIds)
      const id = array[i]
      await handleNestedPublish(
        array[i],
        id === entryId,
        localEntryPublishStatus
      )
    }

    setRePublishLoading(false)

    const isFailed = localEntryPublishStatus.some((x) => {
      return x.status === 'error'
    })

    if (isFailed) {
      setNotificationToShow({
        show: true,
        message: 'Some Nested Entries are not published successfully',
        type: 'error',
      })
    } else {
      setNotificationToShow({
        show: true,
        message: 'All Nested Entries are published successfully',
        type: 'success',
      })
    }
  }

  /**
   * Handles the nested publish logic for a given entry ID.
   *
   * It fetches the entry data for the given ID, checks if the entry is a page or
   * component form (in which case it just adds the nested reference IDs to the set
   * of reference IDs and returns), adds the nested reference IDs to the set of
   * reference IDs, fetches the entry data again, and then attempts to publish the
   * entry. If the publish is successful, it updates the status of the entry in the
   * local entry publish status array to 'done'. If the publish fails, it updates the
   * status to 'error'.
   * @param entryId ID of the entry to publish.
   * @param isRootPage Whether the entry is the root page.
   * @param localEntryPublishStatus Array of the status of each entry.
   */
  const handleNestedPublish = async (
    entryId: string,
    isRootPage: boolean,
    localEntryPublishStatus: Status[]
  ) => {
    if (checkIsEntryProcessed(entryId)) return

    const entry = await getEnFieldData(entryId)

    const contentType = entry?.sys?.contentType?.sys?.id || ''

    const nestedReferenceId = extractEntryIds(entry)

    if (contentType === 'page' || contentType ==='componentForm') {
      if (isRootPage) updateReferenceIds(nestedReferenceId)

      return
    }
    updateReferenceIds(nestedReferenceId)

    let entryFields = entry?.fields

    const internalName = entryFields?.internalName

    let p: Status = {
      entryId,
      internalName,
      status: 'pending',
      type: contentType,
    }

    updateStatus(p)

    let res
    res = await publishEntry(entryId)

    p = {
      ...p,
      status: res ? 'done' : 'error',
    }

    updateStatus(p)
    localEntryPublishStatus.push(p)
  }

  useEffect(() => {
    // show toast message only when tab is active, incase content editor started translation and moved to another tab and came back later
    if (notificationToShow.show && isTabFocused) {
      if (notificationToShow.type === 'success') {
        Notification.success(notificationToShow.message)
      } else {
        Notification.error(notificationToShow.message)
      }

      setNotificationToShow({ show: false, message: '', type: 'success' })
    }
  }, [notificationToShow, isTabFocused])

  useEffect(() => {
    if (!primaryLang) return

    if (!selectedLocale.includes(primaryLang)) {
      setSelectedLocale([...selectedLocale, primaryLang])
    }
  }, [primaryLang])

  useEffect(() => {
    const previousTranslatedLocale = pageConfigurations?.translatedFor || []

    if (previousTranslatedLocale.length > 0) {
      setSelectedLocale(previousTranslatedLocale)
    } else {
      setSelectedLocale(pageDomainAllowedLocale)
    }
  }, [pageDomainAllowedLocale])

  return (
    <Box className='flex w-full p-3 h-full'>
      <Box className='w-1/3 h-full flex flex-col gap-5'>
        <Box className='flex items-start lg:w-4/5 w-full'>
          <FormControlComp
            label='Primary Language'
            tooltip='This is the source language of the selected domain.'
          >
            <p className='font-bold text-lg'>
              {getLocaleFullName(primaryLang?.split('-')?.[0])} ({primaryLang})
            </p>
          </FormControlComp>
        </Box>
        <FormControlComp
          label='Translate to...'
          tooltip='Select the target languages you wish to translate the content into.'
        >
          {Languages.map((locale) => {
            if (pageDomainAllowedLocale.includes(locale.value))
              return (
                <Checkbox
                  isDisabled={locale.value === 'en-CA'}
                  isChecked={selectedLocale?.includes(locale?.value)}
                  onChange={() => handleOnclick(locale?.value)}
                  className='py-2'
                  key={locale.value}
                >
                  {locale?.title}
                </Checkbox>
              )
          })}
        </FormControlComp>
        <Box className='flex flex-row items-center justify-start gap-5'>
          <Button
            variant='primary'
            onClick={handleClick}
            isLoading={loading}
            isDisabled={
              loading ||
              rePublishLoading ||
              (selectedLocale.length === 1 && selectedLocale[0] === 'en-CA')
            }
            className='w-32'
          >
            Translate
          </Button>
          <Button
            className='w-32'
            variant='secondary'
            onClick={handleRepublish}
            isLoading={rePublishLoading}
            isDisabled={rePublishLoading || loading}
          >
            Republish all
          </Button>
        </Box>
        <Box className='pb-5'>
          <UpcomingFeatComp
            bgColor="#ffeea4"
            borderColor="#bdd8ff"
            color="#000000"
            title=""
            subtitle="Warning"
            desc="Republish all will only publish the nested components, and not the current page or form and it's sub component."
          />
        </Box>
      </Box>
      {!_.isEmpty(entryTranslationStatus) && (
        <Box
          className='w-2/3 min-w-[700px] h-full flex flex-col gap-8 overflow-y-auto max-h-[80vh]'
          ref={tableContainerRef}
        >
          <TranslationStatusTable data={entryTranslationStatus} />
        </Box>
      )}
    </Box>
  )
}

export default Translations
