import { Notification } from '@contentful/f36-components'
import axios from 'axios'

/**
 * Updates the robots.txt file for a specified domain.
 * 
 * @param newContent - The new content to be written to the robots.txt file.
 * @param domainCode - The code representing the domain for which the robots.txt should be updated.
 * 
 * Sends a POST request to update the robots.txt file with the provided content. 
 * Displays a success notification if the update is successful, otherwise shows an error notification.
 */

export const updateRobotsTxt = async (newContent: any, domainCode: string) => {
  let url = `${process.env.REACT_APP_BASE_URL}/api/update-robots/`
  const x = url.split('-')
  x[1] = domainCode
  url = x.join('-')
  try {
    const response = await axios.post(
      url,
      { content: newContent },
      {
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )

    if (response.status === 200)
      Notification.success('robots.txt updated successfully')
    else Notification.error('Failed to update robots.txt')
  } catch (error) {
    console.error('Error updating robots.txt:', error)
    Notification.error('Failed to update robots.txt')
  }
}
