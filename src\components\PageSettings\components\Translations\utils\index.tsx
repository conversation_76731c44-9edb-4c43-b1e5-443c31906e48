import axios from 'axios'
import _ from 'lodash'
import { ENVIRONMENT, SPACE_ID } from '../../../../../constant/variables'
import { updateSingleEntryData } from '../../../../../globals/utils'

const url = `${process.env.REACT_APP_BASE_URL}/api/translate-text/`
const ignoreKeys = ['slug', 'htmlAttr', 'htmlAttributes']
const defaultLocale = 'en-CA'
const defaultLocaleCode = defaultLocale.split('-')[0]

  /**
   * Retrieves the field data for a given entry ID in the default locale, en-CA.
   *
   * @param {string} entryId - The ID of the entry.
   * @returns {Promise<any>} - A promise that resolves with the field data of the entry.
   */
export async function getEnFieldData(entryId: string) {
  const response = await fetch(
    `https://preview.contentful.com/spaces/${SPACE_ID}/environments/${ENVIRONMENT}/entries/${entryId}?access_token=${process.env.REACT_APP_ACCESS_TOKEN}`
  )
    .then((response) => response.json())
    .then((result) => result)
    .catch((error) => console.error(error))

  return response
}

  /**
   * Translates an entry and its fields given a selected locale.
   *
   * @param {string} selectedLocale - The locale to translate to.
   * @param {any} entries - An array of entries with their fields.
   * @param {any} allFields - An object containing all the fields of the entry.
   * @param {string} entryId - The ID of the entry.
   * @param {any} contentModel - The content model of the entry.
   * @returns {{ success: boolean }} - An object with a success boolean indicating
   * whether the translation was successful.
   */
export const translateData = async (
  selectedLocale: string,
  entries: any,
  allFields: any,
  entryId: string,
  contentModel?: any
) => {
  try {
    if (!selectedLocale) return { success: false }
    let response = { success: false }
    for (const { key, value } of entries) {
      // let locale = selectedLocale;
      const data = allFields?.[key]
      let isFieldLocalized

      let fieldType = value?.type

      if (contentModel) {
        const filteredContentModel = contentModel?.fields?.find(
          (item: any) => item?.id === key
        )

        isFieldLocalized = filteredContentModel?.localized

        fieldType = filteredContentModel?.type
      }
      if (
        (value?.locales?.includes(selectedLocale) || isFieldLocalized) &&
        !ignoreKeys.includes(key)
      ) {
        if (_.isEmpty(data)) return { success: true }

        if (fieldType === 'Object') {
          const result = getObject(data)

          response = await translateAndSetValue(
            selectedLocale,
            result,
            key,
            fieldType,
            data,
            entryId
          )
        } else if (fieldType === 'Array') {
          response = await translateAndSetValue(
            selectedLocale,
            data,
            key,
            fieldType,
            data,
            entryId
          )
        } else {
          const textArray = [data]
          response = await translateAndSetValue(
            selectedLocale,
            textArray,
            key,
            fieldType,
            data,
            entryId
          )
        }
      }
    }

    return response
  } catch (error: any) {
    return { success: false }
  }
}

  /**
   * Translates the given data and updates the Contentful entry field with the translated data.
   * @param locale The locale to translate the data to.
   * @param data The data to translate.
   * @param key The field ID of the entry to be updated.
   * @param type The type of the data to translate.
   * @param defaultData The default data to be used when the translation fails.
   * @param entryId The ID of the entry to be updated.
   * @returns An object with a 'success' property indicating whether the translation and update was successful.
   */
export const translateAndSetValue: Function = async (
  locale: string,
  data: any,
  key: string,
  type: string,
  defaultData: any,
  entryId: string
) => {
  if (_.isEmpty(data)) return { success: true }

  const postData = data.filter((elem: string) => !_.isEmpty(elem))

  let checkData =
    !_.isEmpty(postData) && postData.map((elem: string) => replaceSymbols(elem))

  if (_.isEmpty(checkData)) return { success: true }

  const response = await axios.post(
    url,
    {
      source_language: defaultLocaleCode,
      target_language: locale.split('-')[0],
      text_array: checkData,
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-vercel-protection-bypass': `${process.env.REACT_APP_VERCEL_BYPASS_SECRET}`,
      },
    }
  )

  const translatedData = response?.data?.message?.map((elem: any) =>
    reverseSymbols(elem.translatedText)
  )

  let updateEntryRes

  if (type == 'Object') {
    const newDefaultData = JSON.parse(JSON.stringify(defaultData))
    const translatedDataResult = setObject(newDefaultData, translatedData)
    updateEntryRes = await updateSingleEntryData(entryId, [
      { fieldId: key, locale: locale, value: translatedDataResult },
    ])
  } else if (type == 'Array') {
    updateEntryRes = await updateSingleEntryData(entryId, [
      { fieldId: key, locale: locale, value: translatedData },
    ])
  } else {
    updateEntryRes = await updateSingleEntryData(entryId, [
      { fieldId: key, locale: locale, value: translatedData.join('') },
    ])
  }

  return updateEntryRes
}

  /**
   * A recursive function that replaces the 'text' property of an object or any of its nested objects with the
   * corresponding element from an array. The replacement is done in-place.
   * @param theObject The object to be modified.
   * @param arr1 The array of values to be inserted into the object.
   * @returns The modified object.
   */
export function setObject(theObject: any, arr1: any[]): any {
  let newResult: any = null

  if (theObject instanceof Array) {
    for (let i = 0; i < theObject?.length; i++) {
      newResult = setObject(theObject?.[i], arr1)
    }
  } else {
    for (const prop in theObject) {
      if (Object.prototype.hasOwnProperty.call(theObject, prop)) {
        if (prop === 'type') {
          if (theObject?.[prop] === 'text') {
            theObject.text = arr1.shift()
          }
        }
        if (
          theObject?.[prop] instanceof Object ||
          theObject?.[prop] instanceof Array
        ) {
          newResult = setObject(theObject?.[prop], arr1)
        }
      }
    }
  }

  return theObject
}

  /**
   * Returns an array of text values from a given object or array of objects, by recursively traversing the nested objects.
   * @param objects The object or array of objects to be traversed.
   * @returns An array of text values.
   */
export function getObject(objects: any) {
  const array: any[] = []

  function getNestedObjectText(theObject: any): void {
    let result: any = null

    if (theObject instanceof Array) {
      for (let i = 0; i < theObject?.length; i++) {
        result = getNestedObjectText(theObject?.[i])
      }
    } else {
      for (const prop in theObject) {
        if (Object.prototype.hasOwnProperty.call(theObject, prop)) {
          if (prop === 'type') {
            if (theObject?.[prop] === 'text') {
              array.push(theObject?.text)
            }
          }
          if (
            theObject?.[prop] instanceof Object ||
            theObject?.[prop] instanceof Array
          ) {
            result = getNestedObjectText(theObject?.[prop])
          }
        }
      }
    }
  }

  getNestedObjectText(objects)
  return array
}

export const TRANSLATION_SYMBOLS: any = {
  '"': '#',
  // Add other symbols as needed
  // Example:
  // "'": "@@",
}

export const translationSymbolsRegex = new RegExp(
  `[${Object.keys(TRANSLATION_SYMBOLS)
    ?.map((symbol) => `\\${symbol}`)
    ?.join('')}]`,
  'g'
)

export const reverseTranslationSymbolsRegex = new RegExp(
  `[${Object.values(TRANSLATION_SYMBOLS)
    ?.map((symbol) => `\\${symbol}`)
    ?.join('')}]`,
  'g'
)

/**
 * Replaces specific symbols in a given string with their corresponding
 * values from the TRANSLATION_SYMBOLS map.
 *
 * @param {any} originalString - The string in which symbols will be replaced. 
 * If the input is not a string, an empty string is returned.
 * @returns {string} - The modified string with symbols replaced. Returns 
 * an empty string if the input is not a string.
 */

export const replaceSymbols = (originalString: any): string => {
  if (typeof originalString !== 'string') return ''

  const stringWithSymbolsReplaced: string = originalString?.replace(
    translationSymbolsRegex,
    (match: string) => TRANSLATION_SYMBOLS?.[match]
  )
  return stringWithSymbolsReplaced
}

  /**
   * Replaces the symbols in a given string with their original
   * character values from the TRANSLATION_SYMBOLS map.
   *
   * @param {string} stringWithSymbolsReplaced - The string in which symbols will be replaced.
   * If the input is not a string, an empty string is returned.
   * @returns {string} - The modified string with symbols replaced. Returns
   * an empty string if the input is not a string.
   */
export const reverseSymbols = (stringWithSymbolsReplaced: string): string => {
  if (typeof stringWithSymbolsReplaced !== 'string') return ''

  const stringRevertedSymbols: string = stringWithSymbolsReplaced?.replace(
    reverseTranslationSymbolsRegex,
    (match: string) => {
      for (const key in TRANSLATION_SYMBOLS) {
        if (TRANSLATION_SYMBOLS[key] === match) {
          return key
        }
        return match
      }
    }
  )

  return stringRevertedSymbols
}

  /**
   * Extracts the IDs from the given data of type "Entry".
   * This is a recursive function that will traverse the given data and
   * extract the IDs from any field that is of type "Entry".
   *
   * @param {any} data - The data from which to extract the IDs.
   * @returns {string[]} - An array of IDs of type "Entry".
   */
export const extractEntryIds = (data: any) => {
  const ids: string[] = []

  // Extract IDs from content with linkType "Entry"
  function searchForIds(obj: any) {
    if (Array.isArray(obj)) {
      obj.forEach((item) => searchForIds(item))
    } else if (typeof obj === 'object' && obj !== null) {
      if (obj?.sys && obj?.sys?.linkType === 'Entry' && obj?.sys?.id) {
        ids.push(obj?.sys?.id)
      }
      Object.values(obj).forEach((value) => searchForIds(value))
    }
  }

  searchForIds(data)

  return ids
}

  /**
   * Transforms an object with key-value pairs into an array of objects, each object
   * containing a key and a value.
   *
   * @param {Object} fields - The object to transform.
   * @returns {Array} - An array of objects with key-value pairs.
   */
export const generateEntryKeyValueFromFields = (fields: any) => {
  let newEntries: any = []

  fields &&
    Object.entries(fields)?.forEach(([key, value]) => {
      newEntries.push({ key, value })
    })

  return newEntries
}
