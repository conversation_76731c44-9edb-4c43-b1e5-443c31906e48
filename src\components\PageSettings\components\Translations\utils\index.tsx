import axios from 'axios'
import _ from 'lodash'
import { ENV_VARIABLES } from '../../../../../constant/variables'
import { updateSingleEntryData } from '../../../../../globals/utils'

const url = `${ENV_VARIABLES.appBaseURL}/api/translate-text/`
const ignoreKeys = ['slug', 'htmlAttr', 'htmlAttributes']
const defaultLocale = 'en-CA'
const defaultLocaleCode = defaultLocale.split('-')[0]

/**
 * Fetches the data for a given entry ID from the Contentful Preview API.
 * @param {string} entryId - The ID of the entry to fetch.
 * @returns {Promise<Object>} - A promise that resolves to the entry data.
 */
export async function getEnFieldData(entryId: string) {
  const response = await fetch(
    `https://preview.contentful.com/spaces/${ENV_VARIABLES.contentfulSpaceID}/environments/${ENV_VARIABLES.contentfulEnvironment}/entries/${entryId}?access_token=${ENV_VARIABLES.contentfulAccessToken}`
  )
    .then((response) => response.json())
    .then((result) => result)
    .catch((error) => console.error(error))

  return response
}

export const translateData = async (
  selectedLocale: string,
  entries: any,
  allFields: any,
  entryId: string,
  contentModel?: any
) => {
  try {
    if (!selectedLocale) return { success: false }
    let response = { success: false }
    for (const { key, value } of entries) {
      // let locale = selectedLocale;
      const data = allFields?.[key]
      let isFieldLocalized

      let fieldType = value?.type

      if (contentModel) {
        const filteredContentModel = contentModel?.fields?.find(
          (item: any) => item?.id === key
        )

        isFieldLocalized = filteredContentModel?.localized

        fieldType = filteredContentModel?.type
      }
      if (
        (value?.locales?.includes(selectedLocale) || isFieldLocalized) &&
        !ignoreKeys.includes(key)
      ) {
        if (_.isEmpty(data)) return { success: true }

        if (fieldType === 'Object') {
          const result = getObject(data)

          response = await translateAndSetValue(
            selectedLocale,
            result,
            key,
            fieldType,
            data,
            entryId
          )
        } else if (fieldType === 'Array') {
          response = await translateAndSetValue(
            selectedLocale,
            data,
            key,
            fieldType,
            data,
            entryId
          )
        } else {
          const textArray = [data]
          response = await translateAndSetValue(
            selectedLocale,
            textArray,
            key,
            fieldType,
            data,
            entryId
          )
        }
      }
    }

    return response
  } catch (error: any) {
    return { success: false }
  }
}

  /**
   * Translates the given text data and updates the Contentful entry with the translated data.
   * @param {string} locale - The locale of the translation.
   * @param {any} data - The data to be translated.
   * @param {string} key - The field ID of the data to be translated.
   * @param {string} type - The type of the data to be translated. Can be 'Object', 'Array', or any other type.
   * @param {any} defaultData - The default data to be used if the translation fails.
   * @param {string} entryId - The ID of the Contentful entry to be updated.
   * @returns {Promise<Object>} - A promise that resolves to an object with a success property that is true if the translation was successful, and false otherwise.
   */
export const translateAndSetValue: Function = async (
  locale: string,
  data: any,
  key: string,
  type: string,
  defaultData: any,
  entryId: string
) => {
  if (_.isEmpty(data)) return { success: true }

  const postData = data.filter((elem: string) => !_.isEmpty(elem))

  let checkData =
    !_.isEmpty(postData) && postData.map((elem: string) => replaceSymbols(elem))

  if (_.isEmpty(checkData)) return { success: true }

  const response = await axios.post(
    url,
    {
      source_language: defaultLocaleCode,
      target_language: locale.split('-')[0],
      text_array: checkData,
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'x-vercel-protection-bypass': `${ENV_VARIABLES.vercelByPassSecret}`,
      },
    }
  )

  const translatedData = response?.data?.message?.map((elem: any) =>
    reverseSymbols(elem.translatedText)
  )

  let updateEntryRes

  if (type == 'Object') {
    const newDefaultData = JSON.parse(JSON.stringify(defaultData))
    const translatedDataResult = setObject(newDefaultData, translatedData)
    updateEntryRes = await updateSingleEntryData(entryId, [
      { fieldId: key, locale: locale, value: translatedDataResult },
    ])
  } else if (type == 'Array') {
    updateEntryRes = await updateSingleEntryData(entryId, [
      { fieldId: key, locale: locale, value: translatedData },
    ])
  } else {
    updateEntryRes = await updateSingleEntryData(entryId, [
      { fieldId: key, locale: locale, value: translatedData.join('') },
    ])
  }

  return updateEntryRes
}

  /**
   * Sets the value of all fields in the given object to the corresponding value in the given array.
   * Recursively traverses the object and its nested objects and arrays, and sets the value of all fields
   * with the name 'text' to the corresponding value in the array.
   * @param {any} theObject - The object whose fields are to be set.
   * @param {any[]} arr1 - The array of values to set the fields to.
   * @returns {any} The modified object.
   */
export function setObject(theObject: any, arr1: any[]): any {
  let newResult: any = null

  if (theObject instanceof Array) {
    for (let i = 0; i < theObject?.length; i++) {
      newResult = setObject(theObject?.[i], arr1)
    }
  } else {
    for (const prop in theObject) {
      if (Object.prototype.hasOwnProperty.call(theObject, prop)) {
        if (prop === 'type') {
          if (theObject?.[prop] === 'text') {
            theObject.text = arr1.shift()
          }
        }
        if (
          theObject?.[prop] instanceof Object ||
          theObject?.[prop] instanceof Array
        ) {
          newResult = setObject(theObject?.[prop], arr1)
        }
      }
    }
  }

  return theObject
}

  /**
   * Gets all the text values from the given object and its nested objects and arrays.
   * @param {any} objects - The object to traverse.
   * @returns {any[]} An array of all the text values found in the object.
   */
export function getObject(objects: any) {
  const array: any[] = []

  function getNestedObjectText(theObject: any): void {
    let result: any = null

    if (theObject instanceof Array) {
      for (let i = 0; i < theObject?.length; i++) {
        result = getNestedObjectText(theObject?.[i])
      }
    } else {
      for (const prop in theObject) {
        if (Object.prototype.hasOwnProperty.call(theObject, prop)) {
          if (prop === 'type') {
            if (theObject?.[prop] === 'text') {
              array.push(theObject?.text)
            }
          }
          if (
            theObject?.[prop] instanceof Object ||
            theObject?.[prop] instanceof Array
          ) {
            result = getNestedObjectText(theObject?.[prop])
          }
        }
      }
    }
  }

  getNestedObjectText(objects)
  return array
}

export const TRANSLATION_SYMBOLS: any = {
  '"': '#',
  // Add other symbols as needed
  // Example:
  // "'": "@@",
}

export const translationSymbolsRegex = new RegExp(
  `[${Object.keys(TRANSLATION_SYMBOLS)
    ?.map((symbol) => `\\${symbol}`)
    ?.join('')}]`,
  'g'
)

export const reverseTranslationSymbolsRegex = new RegExp(
  `[${Object.values(TRANSLATION_SYMBOLS)
    ?.map((symbol) => `\\${symbol}`)
    ?.join('')}]`,
  'g'
)

  /**
   * Replace symbols in the given string with their corresponding values in TRANSLATION_SYMBOLS.
   * This is used to handle symbols that are not supported by the translation service (e.g. double quotes).
   * @param {any} originalString - The string to replace symbols in.
   * @returns {string} The string with symbols replaced.
   */
export const replaceSymbols = (originalString: any): string => {
  if (typeof originalString !== 'string') return ''

  const stringWithSymbolsReplaced: string = originalString?.replace(
    translationSymbolsRegex,
    (match: string) => TRANSLATION_SYMBOLS?.[match]
  )
  return stringWithSymbolsReplaced
}

  /**
   * Reverse the replacement of symbols in the given string with their corresponding values in TRANSLATION_SYMBOLS.
   * This is used to handle symbols that are not supported by the translation service (e.g. double quotes).
   * @param {string} stringWithSymbolsReplaced - The string to reverse the replacement of symbols in.
   * @returns {string} The string with symbols replaced reversed.
   */
export const reverseSymbols = (stringWithSymbolsReplaced: string): string => {
  if (typeof stringWithSymbolsReplaced !== 'string') return ''

  const stringRevertedSymbols: string = stringWithSymbolsReplaced?.replace(
    reverseTranslationSymbolsRegex,
    (match: string) => {
      for (const key in TRANSLATION_SYMBOLS) {
        if (TRANSLATION_SYMBOLS[key] === match) {
          return key
        }
        return match
      }
    }
  )

  return stringRevertedSymbols
}

  /**
   * Recursively traverse the given object and its nested objects and arrays
   * and return an array of IDs of content with linkType "Entry".
   * @param {any} data - The object to traverse.
   * @returns {string[]} An array of IDs of content with linkType "Entry".
   */
export const extractEntryIds = (data: any) => {
  const ids: string[] = []

  // Extract IDs from content with linkType "Entry"
  function searchForIds(obj: any) {
    if (Array.isArray(obj)) {
      obj.forEach((item) => searchForIds(item))
    } else if (typeof obj === 'object' && obj !== null) {
      if (obj?.sys && obj?.sys?.linkType === 'Entry' && obj?.sys?.id) {
        ids.push(obj?.sys?.id)
      }
      Object.values(obj).forEach((value) => searchForIds(value))
    }
  }

  searchForIds(data)

  return ids
}

  /**
   * Converts an object of field names to their values to an array of objects
   * with key-value pairs.
   * @param {object} fields - An object of field names to their values.
   * @returns {object[]} An array of objects with key-value pairs.
   */
export const generateEntryKeyValueFromFields = (fields: any) => {
  let newEntries: any = []

  fields &&
    Object.entries(fields)?.forEach(([key, value]) => {
      newEntries.push({ key, value })
    })

  return newEntries
}
