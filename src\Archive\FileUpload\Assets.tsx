import { EditorAppSDK } from '@contentful/app-sdk'
import { AssetCard, MenuItem } from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { getAssetInfo } from '../../../globals/utils'

interface AssetInfoI {
  sdk: EditorAppSDK
  asset: any
  onFileUpdate: (asset: any) => void
  currentLocale: string
}

const Asset = (props: AssetInfoI) => {
  const { asset, currentLocale, sdk, onFileUpdate } = props

  const [AssetInfo, setAssetInfo] = useState<any>()

  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const fetchAsset = async () => {
      try {
        const assetData = await getAssetInfo(asset, currentLocale)
        setAssetInfo(assetData)
      } catch (error) {
        console.error('Error fetching asset:', error)
      }
    }
    fetchAsset()
  }, [asset, currentLocale])

  const handleMenuClick = async (status: string) => {
    setIsLoading(true)
    if (status === 'edit') {
      try {
        const result = await sdk.navigator.openAsset(asset.sys.id, {
          slideIn: true,
        })

        if (result) {
          const updatedEntry = result.entity
          const assetInfo = await getAssetInfo(updatedEntry, currentLocale)
          setAssetInfo(assetInfo)
          onFileUpdate(updatedEntry)
        }
        setIsLoading(false)
      } catch (error) {
        setAssetInfo((pre: any) => {
          return {
            ...pre,
            status: 'deleted',
          }
        })
        onFileUpdate(null)
        console.error('Error opening slide-in panel:', error)
      }
    } else if (status === 'delete') {
      setAssetInfo((pre: any) => {
        return {
          ...pre,
          status: 'deleted',
        }
      })
      onFileUpdate(null)
    }
  }
  return (
    <AssetCard
      status={AssetInfo?.status}
      title={AssetInfo?.title}
      type={AssetInfo?.contentType}
      size='small'
      actions={[
        <MenuItem key='edit' onClick={() => handleMenuClick('edit')}>
          Edit
        </MenuItem>,
        <MenuItem key='remove' onClick={() => handleMenuClick('delete')}>
          Delete
        </MenuItem>,
      ]}
      onClick={() => handleMenuClick('edit')}
      isLoading={isLoading}
    />
  )
}
export default Asset
