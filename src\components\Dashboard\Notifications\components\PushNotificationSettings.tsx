import React, { useEffect } from 'react'
import { Box, Select, Switch, TextInput } from '@contentful/f36-components'
import { Multiselect } from '@contentful/f36-multiselect'
import {
  DomainNotiState,
  GlobalNotiState,
  NotificationTypes,
  UpdateNotiStateFunction,
} from '../utils/notifications.interface'
import FormControlComp from '../../../Shared/FormControlComp'
import './index.css'
import { AllowedNotificationType, CTATargets, NotificationDuration, NotificationType } from '../utils/constants'

interface Props {
  state: DomainNotiState | GlobalNotiState,
  updateState: UpdateNotiStateFunction<DomainNotiState | GlobalNotiState>
}

function PushNotificationSettings({ state, updateState }: Props) {

  const {
    notificationDuration,
    ctaTarget,
    notificationType,
    ctaText,
    notificationCategory,
    isSystemNotification,
    attentionRequired,
    groupingCategory,
    renotify, isSystemNotificationDismissible
  } = state

  const isGeneralSelected = notificationCategory.includes('general')

  /**
   * Handles the selection of notification categories.
   *
   * @param {Object} event - The event triggered by selecting a notification type.
   * @param {string} event.target.value - The value of the selected notification type.
   *
   * If "General" is selected and not all categories are already selected, it selects all categories, including "General".
   * If all categories are selected, it deselects all.
   * For individual types, it toggles their selection.
   * If all individual types are selected, "General" is automatically included.
   * Otherwise, it ensures "General" is not included unless all types are selected.
   */
  const handleSelectItem = (event: any) => {
    const { value } = event.target
    const currentTypes = notificationCategory  // Current selection state
    const allValues = AllowedNotificationType.map((type) => type.value)  // All available notification types

    if (value === 'general') {
      // If "General" is selected and not all are already selected, select all
      const areAllSelected = allValues.every((type) => currentTypes.includes(type))

      if (!areAllSelected) {
        // If not all are selected, select all
        updateState({
          notificationCategory: [...allValues, 'general'],
        })
      } else {
        // If all are selected, uncheck all
        updateState({
          notificationCategory: [],
        })
      }
    } else {
      // Toggle individual type
      const updated = currentTypes.includes(value)
        ? currentTypes.filter((v: any) => v !== value)
        : [...currentTypes, value]

      const areAllNowSelected = allValues.every((type) => updated.includes(type))

      updateState({
        notificationCategory: areAllNowSelected
          ? [...updated, 'general']
          : updated.filter((v: any) => v !== 'general'),
      })
    }
  }

  /**
   * Toggles the selection of all notification categories.
   *
   * @param {Object} event - The change event triggered by the checkbox.
   * @param {boolean} event.target.checked - Indicates if the toggle-all checkbox is checked.
   *
   * If checked, all notification categories are selected. Otherwise, all categories are deselected.
   */
  const toggleAll = (event: any) => {
    const { checked } = event.target
    if (checked) {
      updateState({
        notificationCategory: AllowedNotificationType.map(item => item.value),
      })
    } else {
      updateState({
        notificationCategory: [],
      })
    }
  }

  useEffect(() => {
    if (isGeneralSelected) {
      updateState({
        notificationCategory: AllowedNotificationType.map(item => item.value),
      })
    }
  }, [isGeneralSelected])


  return (
    <Box className="flex justify-start items-start gap-5 w-full h-full pt-5">
      <Box className={'flex flex-col w-full justify-start items-start gap-5'}>
        <FormControlComp
          label="Notification Type:"
          isRequired={true}
          tooltip="Choose option to either send only On Page or On Browser or Both Notification"
        >
          <Select
            onChange={(e) => {
              updateState({
                notificationType: e?.target?.value as NotificationTypes,
                attentionRequired: e?.target?.value ==="page"? false: attentionRequired
              })
            }}
            value={notificationType}
            className="w-full"
          >
            <Select.Option value="" isDisabled>
              Select
            </Select.Option>
            {NotificationType.map((option) => (
              <Select.Option value={option.value}>
                {option.title}
              </Select.Option>
            ))}
          </Select>
        </FormControlComp>
        <FormControlComp
          label="Notification Category:"
          isRequired={true}
          tooltip="Select one or more notification category"
        >
          <Multiselect
            currentSelection={notificationCategory.map(item => AllowedNotificationType.find(space => space.value === item)?.title || '')}
            popoverProps={{ isFullWidth: true }}
          >
            <Multiselect.SelectAll
              onSelectItem={toggleAll}
              isChecked={isGeneralSelected}
            />
            {AllowedNotificationType.map((notiType) => {
              const val = notiType.value.toLowerCase().replace(/\s/g, '-')
              return (
                <Multiselect.Option
                  key={`key-${val}`}
                  itemId={`space-${val}`}
                  value={notiType.value}
                  label={notiType.title}
                  onSelectItem={handleSelectItem}
                  isChecked={notificationCategory.includes(notiType.value)}
                />
              )
            })}
          </Multiselect>
        </FormControlComp>
        {notificationType !== 'page' && (
          <FormControlComp
            label="Grouping Category"
            tooltip="Select Grouping Category for notification to display"
            className="smallSelect"
          >
            <Select
              value={groupingCategory}
              onChange={(e: any) => {
                updateState({
                  groupingCategory: e?.target?.value,
                  renotify: !e?.target?.value? false : renotify
                })
              }}
            >
              <Select.Option value="">
                Select Grouping Category
              </Select.Option>
              {AllowedNotificationType?.map((option) => (
                <Select.Option value={option?.value}>
                  {option?.title}
                </Select.Option>
              ))}
            </Select>
          </FormControlComp>
        )}
        {notificationType !== 'browser' && (
          <FormControlComp
            label="Duration"
            tooltip="Select Duration for notification to display"
            isRequired
            className="smallSelect"
          >
            <Select
              value={notificationDuration}
              onChange={(e: any) => {
                updateState({
                  notificationDuration: e?.target?.value,
                })
              }}
            >
              <Select.Option value="" isDisabled>
                Select
              </Select.Option>
              {NotificationDuration?.map((option) => (
                <Select.Option value={option?.value}>
                  {option?.title}
                </Select.Option>
              ))}
            </Select>
          </FormControlComp>
        )}

      </Box>
      <Box className={'flex flex-col w-full justify-start items-start gap-5'}>
      {notificationType !== 'browser' && (
        <>
        <FormControlComp
          label="CTA Text"
          isRequired={true}
          tooltip={`Enter CTA Text for this Notification`}
        >
          <TextInput
            value={ctaText}
            placeholder="Enter Title"
            onChange={(e) =>
              updateState({
                ctaText: e?.target?.value,
              })
            }
          />
        </FormControlComp>
        <FormControlComp
          label="CTA Target"
          tooltip="Select Target for notification to display"
          isRequired
          className="smallSelect"
        >
          <Select
            value={ctaTarget}
            onChange={(e: any) => {
              updateState({
                ctaTarget: e?.target?.value,
              })
            }}
          >
            <Select.Option value="" isDisabled>
              Select
            </Select.Option>
            {CTATargets?.map((option) => (
              <Select.Option value={option?.value}>
                {option?.title}
              </Select.Option>
            ))}
          </Select>
        </FormControlComp>
        </>)}
        <Box className={'grid grid-cols-2 gap-3 w-full'}>
          {/*{notificationType !== 'browser' && (*/}
          {/*  <FormControlComp*/}
          {/*    label="Mark as System Notification"*/}
          {/*    tooltip="Mark Current Notification as a System Notification"*/}
          {/*    className="smallSelect">*/}
          {/*    <Switch*/}
          {/*      isChecked={isSystemNotification}*/}
          {/*      onChange={() =>*/}
          {/*        updateState({ isSystemNotification: !isSystemNotification, isSystemNotificationDismissible:!isSystemNotification? false: isSystemNotificationDismissible })*/}
          {/*      }*/}
          {/*      className="switchNoPadding"*/}
          {/*    />*/}
          {/*  </FormControlComp>*/}
          {/*)}*/}
          {/*{isSystemNotification && (*/}
          {/*  <FormControlComp*/}
          {/*    label="Is System Notification Dismissible"*/}
          {/*    tooltip="Set true if you want to allow user to dismiss the system notification and keep it as Previous Notifications"*/}
          {/*    className="smallSelect">*/}
          {/*    <Switch*/}
          {/*      isChecked={isSystemNotificationDismissible}*/}
          {/*      onChange={() =>*/}
          {/*        updateState({ isSystemNotificationDismissible: !isSystemNotificationDismissible })*/}
          {/*      }*/}
          {/*      className="switchNoPadding"*/}
          {/*    />*/}
          {/*  </FormControlComp>*/}
          {/*)}*/}
          {notificationType !== 'page' && (
            <FormControlComp
              label="Is Attention Required"
              tooltip="Set if User Needs to Manually Dismiss The Browser Notification"
              className="smallSelect">
              <Switch
                isChecked={attentionRequired}
                onChange={() =>
                  updateState({ attentionRequired: !attentionRequired })
                }
                className="switchNoPadding"
              />
            </FormControlComp>
          )}
          {notificationType !== 'page' && groupingCategory !=="" && (
            <FormControlComp
              label="Renotify"
              tooltip="Set is Same grouping category notification should be re-notified"
              className="smallSelect">
              <Switch
                isChecked={renotify}
                onChange={() =>
                  updateState({ renotify: !renotify })
                }
                className="switchNoPadding"
              />
            </FormControlComp>
          )}
        </Box>
      </Box>
    </Box>
  )
}

export default PushNotificationSettings
