import React from "react";

interface RibbonProps {
  border?: string; // Tailwind class for border (e.g., "border-2 border-green-500")
  ribbonText?: string; // Text displayed on the ribbon
  ribbonColor?: string; // Tailwind class for ribbon background color (e.g., "bg-red-500")
  ribbonTextColor?: string; // Tailwind class for ribbon text color (e.g., "text-white")
  ribbonCornerStyle?: string; // Tailwind class for corner style (e.g., "rounded-tr-lg", "rounded-md")
  ribbonShadow?: string; // Tailwind class for ribbon shadow (e.g., "shadow-lg")
  ribbonClassName?: string; // Additional Tailwind classes for the ribbon
  ribbonPosition?: "top-left" | "top-right" | "bottom-left" | "bottom-right"; // Position of the ribbon
  className?: string; // Additional Tailwind classes for the container
  children?: React.ReactNode; // Content inside the component
}

const RibbonComponent: React.FC<RibbonProps> = ({
  border = "border-2 border-gray-300",
  ribbonText = "Ribbon",
  ribbonColor = "bg-red-500",
  ribbonTextColor = "text-white",
  ribbonCornerStyle = "rounded-tr-lg",
  ribbonShadow = "",
  ribbonClassName = "",
  ribbonPosition = "top-left", // Default position
  className = "",
  children,
}) => {
  // Positioning classes based on ribbonPosition
  const positionClasses = {
    "top-left": "-top-2 -left-2",
    "top-right": "-top-2 -right-2",
    "bottom-left": "-bottom-2 -left-2",
    "bottom-right": "-bottom-2 -right-2",
  };

  return (
    <div
      className={`relative p-4 rounded-lg ${border} ${className}`}
    >
      {/* Ribbon */}
      <div
        className={`absolute px-2 py-1 text-sm font-bold ${ribbonColor} ${ribbonTextColor} ${ribbonCornerStyle} ${ribbonShadow} ${ribbonClassName} ${positionClasses[ribbonPosition]}`}
      >
        {ribbonText}
      </div>

      {/* Children */}
      <div className="content">{children}</div>
    </div>
  );
};

export default RibbonComponent;

// import React from "react";

// interface RibbonProps {
//   border?: string; // Tailwind class for border (e.g., "border-2 border-green-500")
//   borderColor?: string; // Tailwind class for border color (e.g., "border-green-500")
//   ribbonText?: string; // Text displayed on the ribbon
//   ribbonColor?: string; // Tailwind class for ribbon background color (e.g., "bg-red-500")
//   ribbonTextColor?: string; // Tailwind class for ribbon text color (e.g., "text-white")
//   className?: string; // Additional Tailwind class names
//   children?: React.ReactNode; // Content inside the component
// }

// const RibbonComponent: React.FC<RibbonProps> = ({
//   border = "border-2 border-gray-300",
//   borderColor = "border-gray-300",
//   ribbonText = "Ribbon",
//   ribbonColor = "bg-red-500",
//   ribbonTextColor = "text-white",
//   className = "",
//   children,
// }) => {
//   return (
//     <div
//       className={`relative p-4 rounded-lg ${border} ${className}`}
//     >
//       {/* Ribbon */}
//       <div
//         className={`absolute -top-2 -left-2 px-2 py-1 text-sm font-bold ${ribbonColor} ${ribbonTextColor} rounded-tr-lg`}
//       >
//         {ribbonText}
//       </div>

//       {/* Children */}
//       <div className="content">{children}</div>
//     </div>
//   );
// };

// export default RibbonComponent;
