import { EditorAppSDK } from '@contentful/app-sdk'
import { Box } from '@contentful/f36-components'
import React from 'react'
import logo from '../../../../assets/page_settings.png'

interface Props {
  sdk: EditorAppSDK
  isCloseClicked: boolean
}

function ModalHeader({ isCloseClicked, sdk }: Props) {
  return (
    <Box className='flex items-center justify-between w-full'>
      <Box
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'start',
          gap: '2px',
        }}
      >
        <img
          src={logo}
          height={38}
          width={38}
          className='aspect-auto'
          alt='configurator logo'
        />

        <p className='flex w-full justify-start items-start gap-1 pl-1'>
          <span className='subtitleHeading'>Page Settings: </span>
          <span>{sdk.entry.fields['seoTitle'].getValue()}</span>
        </p>
      </Box>

      {isCloseClicked && (
        <Box className='flex items-center'>
          <p className='text-[#E2222B]'>
            Please resolve required tags before saving
          </p>
        </Box>
      )}
    </Box>
  )
}

export default ModalHeader
