import React, { useEffect, useState } from 'react';
import { DragHandle, Flex, FormControl, Paragraph, Select, Stack, Table, Text } from '@contentful/f36-components';
import { useSDK } from '@contentful/react-apps-toolkit';
import { createClient } from 'contentful-management';
import { DndContext } from '@dnd-kit/core';
import {
  arrayMove,
  horizontalListSortingStrategy,
  SortableContext,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { css } from 'emotion';
import { FieldAppSDK } from '@contentful/app-sdk';
import { DraggableTableRow } from './DraggableTableRow';
import { arraysToJson, getHelpIcon, getMappableFields } from '../../utils';


function TransformFields() {

    
  const [ct, setCt] = useState<any>([])
  const [from, setFrom] = useState<any>("")
  const [to, setTo] = useState<any>("")
  const [fromList, setFromList] = useState<any>([])
  const [toList, setToList] = useState<any>([])

  const sdk = useSDK<FieldAppSDK>();
  /*
     To use the cma, inject it as follows.
     If it is not needed, you can remove the next line.
  */
  // const cma = useCMA();
  const space = sdk.space

  useEffect(() => {
    space.getContentTypes().then(res => { setCt(res.items) })
  }, [])


  useEffect(() => {
    sdk.window.startAutoResizer();
    sdk.entry.fields["dataSource"].onValueChanged(async(value) => {

      if(value?.sys?.id){
        const selectedEntry =  await sdk.cma.entry.get({entryId:value?.sys?.id})
        setFrom(selectedEntry.sys.contentType.sys.id)
        console.log('value', value?.sys?.id,selectedEntry)
      }else{
        setFrom("")
      }

    })

    const publishedFieldMapping = sdk.field.getValue()
    if(publishedFieldMapping){
      setFrom(publishedFieldMapping["fromContentType"])
      setTo(publishedFieldMapping["toContentType"])
      
      console.log('publishedFieldMapping', publishedFieldMapping)
    }
   
    
    // delete fieldCopy["fromContentType"]
    // delete fieldCopy["toContentType"]

    // setFromList(Object.keys(fieldCopy))
    // setToList(Object.values(fieldCopy))
    
  }, []);

  useEffect(() => {
    if (from !== "") {
      const allFields:Array<any> = ct?.filter(item => item.sys.id === from)?.at(0)?.fields
      const fields = getMappableFields(allFields)

      const publishedFieldMapping = sdk.field.getValue()
      if(fields && publishedFieldMapping && from === publishedFieldMapping["fromContentType"]){

        const fromFieldIds =  publishedFieldMapping.fromFieldIds
        const sortedFields:Array<any>=[]
        fromFieldIds.map(fieldId=>{
          const index = fields.findIndex((val)=>val.id === fieldId)
          if(index>=0){
            sortedFields.push(fields[index])
          }
        })
        setFromList(sortedFields)

      }else{
        setFromList(fields)
      }
    }
  }, [from,ct])

  useEffect(() => {
    if (to !== "") {
      const allFields = ct?.filter(item => item.sys.id === to)?.at(0)?.fields
      const fields = getMappableFields(allFields)
      
      const publishedFieldMapping = sdk.field.getValue()
      if(fields && publishedFieldMapping && to === publishedFieldMapping["toContentType"]){

        const toFieldIds = publishedFieldMapping.toFieldIds
        const sortedFields:Array<object>=[]
        toFieldIds?.map(fieldId=>{
          const index = fields.findIndex((val)=>val.id === fieldId)
          if(index>=0){
            sortedFields.push(fields[index])
          }
        })
        setToList(sortedFields)

      }else{
        setToList(fields)
      }
      console.log("fields", fields, to);
    }
  }, [to,ct])

  useEffect(() => {

    if (fromList?.length > 0 && toList?.length > 0) {
      
      const publishedFieldMapping = sdk.field.getValue()
      const meta = {
        fromContentType: from,
        toContentType: to,
        fromFieldIds:fromList.map(item=>item.id),
        toFieldIds:toList.map(item=>item.id),
      }
      if(JSON.stringify(publishedFieldMapping)!==JSON.stringify(meta)){
        sdk.field.setValue(meta)
      }
    }

  }, [fromList, toList])


  const handleDragEnd = (event, setList,flag) => {
    const { active, over } = event;
    
    if (active.id !== over.id) {
      setList((items) => {
        const oldIndex = items.findIndex(i=>i.id===active.id);
        const newIndex = items.findIndex(i=>i.id===over.id);
        const oppositeList = flag === "from" ?toList : fromList
        if(items[oldIndex]?.type !== oppositeList[newIndex]?.type){
          return items
        }
        return arrayMove(items, oldIndex, newIndex);
      });
    }
  };

  return <Flex justifyContent='center' alignItems='center'><Flex alignItems="flex-start">
    <Stack flexDirection="column" spacing="spacingS">
      <FormControl>
        <FormControl.Label>From (Source Object)</FormControl.Label>
        <Select onChange={e => setFrom(e.target.value)} value={from} id="optionSelect" name="optionSelect">
          {
            ct?.map((item: any) => <Select.Option isDisabled={item.name!=="Person Object"} value={item.sys.id}>{item.name}</Select.Option>)
          }
        </Select>
      </FormControl>

      {fromList?.length > 0 && <DndContext onDragEnd={(e) => handleDragEnd(e, setFromList,"from")}>
        <SortableContext items={fromList} strategy={horizontalListSortingStrategy}>
          <Table>
            {fromList.map((item) => (
              <DraggableTableRow id={item.id} fieldData={item} key={item.id} />
            ))}
          </Table>
        </SortableContext>

      </DndContext>}
    </Stack>

    <Stack flexDirection="column" spacing="spacingS">
      <FormControl>
        <FormControl.Label>To (Style / Template)</FormControl.Label>
        <Select onChange={e => setTo(e.target.value)} value={to} id="optionSelect" name="optionSelect">
          {
            ct?.map((item: any) => <Select.Option isDisabled={item.name!=="Card / Insights" && item.name!=="Link"} value={item.sys.id}>{item.name}</Select.Option>)
          }
        </Select>
      </FormControl>

      {toList?.length > 0 && <DndContext onDragEnd={(e) => handleDragEnd(e, setToList,"to")}>
        <SortableContext items={toList} strategy={horizontalListSortingStrategy}>
          <Table>
            {toList?.map((item) => (
               <DraggableTableRow id={item.id} fieldData={item} key={item.id} />
            ))}
          </Table>
        </SortableContext>

      </DndContext>}
    </Stack>
  </Flex>
  </Flex>
  
}

export default TransformFields