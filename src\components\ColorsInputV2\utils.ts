import { ActiveColorIndexI, ColorI } from '.'
import { colorsValues } from './colorValues'

// Function to find the index of a color or variant by its hex value
export const findColorIndexByHex = (
  query: string
): ActiveColorIndexI | null => {
  if (!query) return null

  let index: ActiveColorIndexI | null = null
  let queryValues = query.split('/').map((q) => q.toLowerCase().trim())
  let angle: Number | null = null
  const isGradient = query.includes('gradient') || queryValues.length > 1
  if (isGradient) {
    queryValues = query.split(',').map((q) => q.toLowerCase().trim().replace(')', ''))
    const angleVal = queryValues.shift()?.match(/(\d+)deg/)?.[1]
    if (angleVal) {
      angle = Number(angleVal);
    }
  }
  colorsValues.forEach((group, categoryIndex) => {
    group.colors.forEach((color, colorIndex) => {
      // Check if the query matches main color properties

      if (
        color.name.toLowerCase() === query.toLowerCase() ||
        color.codeName.toLowerCase() === query.toLowerCase() ||
        color.hex?.toLowerCase() === query.toLowerCase() ||
        (color.rgb && cleanRgb(color.rgb) === query.toLowerCase())
      ) {
        index = { categoryIndex, colorIndex, variantIndex: null } // Found in main color
      }

      // Check if the query matches any variant properties
      color?.variants?.forEach((variant, variantIndex) => {
        if (
          variant.name.toLowerCase() === query.toLowerCase() ||
          variant.codeName.toLowerCase() === query.toLowerCase() ||
          variant.hex?.toLowerCase() === query.toLowerCase() ||
          (variant.rgb && cleanRgb(variant.rgb) === query.toLowerCase())
        ) {
          index = { categoryIndex, colorIndex, variantIndex } // Found in variants
        }

        if (isGradient && variant.gradientColors?.length) {
          const gradientHexValues = variant.gradientColors.map((gc) =>
            gc.hex.toLowerCase()
          )
          const gradientRgbValues = variant.gradientColors.map((gc) =>
            cleanRgb(gc.rgb)
          )

          const matchAngle = (angle !== null) ? variant.angle === (angle) : true

          if (matchAngle && (
            queryValues.every((q, i) => q === gradientHexValues[i]) ||
            queryValues.every((q, i) => q === gradientRgbValues[i]))
          ) {
            index = { categoryIndex, colorIndex, variantIndex: variantIndex }
            // Found in gradient colors
          }
        }
      })

      if (isGradient) {
        // Check if the query matches any gradient color properties
        color?.gradientColors?.forEach((gradientColor, gradientIndex) => {
          const gradientHexValues = color.gradientColors.map((gc) =>
            gc.hex.toLowerCase()
          )
          const gradientRgbValues = color.gradientColors.map((gc) =>
            cleanRgb(gc.rgb)
          )
          const matchAngle = (angle !== null) ? color.angle === (angle) : true

          if (matchAngle && (
            queryValues.every((q, i) => q === gradientHexValues[i]) ||
            queryValues.every((q, i) => q === gradientRgbValues[i]))
          ) {
            index = { categoryIndex, colorIndex, variantIndex: null } // Found in gradient colors
          }
        })
      }
    })
  })

  return index
}

export function getColorStyles(color: ColorI) {
  if (!color)
    return {
      background: 'white',
    }
  if (color.hex) {
    return {
      background: color.hex,
    }
  } else {
    return {
      background: `linear-gradient(${color.angle}deg,${color.gradientColors[0].hex},${color.gradientColors[1].hex})`,
    }
  }
}

export function cleanRgb(rgb: string) {
  return rgb?.replace('rgb(', '').replace(')', '')
}

// function to check whether searched color category is disabled or not.
export const isCategoryDisabled = (categoryIndex: number, disabledState: any) => {
  if (!disabledState) return false
  if (!categoryIndex) return false
  if (!colorsValues[categoryIndex]) return false
  if (!colorsValues[categoryIndex]?.name) return false

  const categoryName = colorsValues[categoryIndex]?.name ?? ''
  return Boolean(disabledState[categoryName])
}