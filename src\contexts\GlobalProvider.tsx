import React, { useState } from 'react'
import { GlobalContext, initialGlobalState } from './globalContext'

function GlobalProvider({ children }: { children: React.ReactNode }) {
  const [currentLocale, setCurrentLocale] = useState(
    initialGlobalState.currentLocale
  )
  const [isHyperLinkModalOpen, setIsHyperLinkModalOpen] = useState(
    initialGlobalState.isHyperLinkModalOpen
  )
  const [isCrossPostingModalOpen, setIsCrossPostingModalOpen] = useState(
    initialGlobalState.isCrossPostingModalOpen
  )

  const [isConfigSettingsModalOpen, setIsConfigSettingsModalOpen] = useState(
    initialGlobalState.isConfigSettingsModalOpen
  )

  const [isStylingModalOpen, setIsStylingModalOpen] = useState(
    initialGlobalState.isStylingModalOpen
  )

  const [chartFileModal, setCharFileModal] = useState(
    initialGlobalState.chartFileModal
  )
  const [activeColor, setActiveColor] = useState(initialGlobalState.activeColor)
  const [isColorPickerActive, setIsColorPickerActive] = useState(
    initialGlobalState.isColorPickerActive
  )

  const [isPageSettingModalOpen, setIsPageSettingModalOpen] = useState(false)

  const [isTranslationModalOpen, setIsTranslationModalOpen] = useState(false)
  const [isDataTableModalOpen, setIsDataTableModalOpen] = useState(initialGlobalState.isDataTableModalOpen)

  return (
    <GlobalContext.Provider
      value={{
        currentLocale,
        setCurrentLocale,
        isHyperLinkModalOpen,
        setIsHyperLinkModalOpen,
        isStylingModalOpen,
        setIsStylingModalOpen,
        activeColor,
        setActiveColor,
        isCrossPostingModalOpen,
        setIsCrossPostingModalOpen,
        isConfigSettingsModalOpen,
        setIsConfigSettingsModalOpen,
        chartFileModal,
        setCharFileModal,
        isColorPickerActive,
        setIsColorPickerActive,
        isPageSettingModalOpen,
        setIsPageSettingModalOpen,
        isTranslationModalOpen,
        setIsTranslationModalOpen,
        isDataTableModalOpen,
        setIsDataTableModalOpen
      }}
    >
      {children}
    </GlobalContext.Provider>
  )
}

export default GlobalProvider
