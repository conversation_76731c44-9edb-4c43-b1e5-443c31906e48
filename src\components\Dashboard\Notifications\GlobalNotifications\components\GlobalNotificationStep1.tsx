import { Box } from '@contentful/f36-components'
import React from 'react'
import { GlobalNotiState, UpdateNotiStateFunction } from '../../utils/notifications.interface'
import DetailsSection from '../../components/DetailsSection'
import SourceSection from '../../components/SourceSection'

function GlobalNotificationStep1({state,
updateState,
pageSelector,
handleFileChange,
selectSingleAsset,
}: {
  state: GlobalNotiState
  updateState: UpdateNotiStateFunction<GlobalNotiState>
  pageSelector: () => void
  selectSingleAsset: (type: 'thumbnail' | 'badge') => void
  handleFileChange: (e: any, type: 'thumbnail' | 'badge') => void
}) {
  return (
    <Box className="flex justify-start items-start gap-5 w-full h-full ">
      <SourceSection
        state={state}
        updateState={updateState}
        pageSelector={pageSelector}
        fromGlobal={true}/>
      <DetailsSection
        selectSingleAsset={selectSingleAsset}
        handleFileChange={handleFileChange}
        state={state}
        updateState={updateState}
        fromGlobal={true}
      />
    </Box>
  )
}

export default GlobalNotificationStep1
