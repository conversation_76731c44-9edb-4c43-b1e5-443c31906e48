import { Box } from '@contentful/f36-components'
import React from 'react'
import DetailsSection from '../../components/DetailsSection'
import SourceSection from '../../components/SourceSection'
import { GlobalNotiState, UpdateNotiStateFunction } from '../../utils/notifications.interface'

/**
 * Renders the first step of the Global Notification creation process.
 *
 * This component is responsible for displaying the source and details sections
 * of the global notification form. It allows users to select a page and manage
 * assets such as thumbnails and badges.
 *
 * Props:
 * - state: The current state of the global notification.
 * - updateState: A function to update the notification state.
 * - pageSelector: A function to select a page.
 * - handleFileChange: A function to handle changes to file inputs.
 * - selectSingleAsset: A function to select a single asset, either a thumbnail or badge.
 */

function GlobalNotificationStep1({state,
updateState,
pageSelector,
handleFileChange,
selectSingleAsset,
}: {
  state: GlobalNotiState
  updateState: UpdateNotiStateFunction<GlobalNotiState>
  pageSelector: () => void
  selectSingleAsset: (type: 'thumbnail' | 'badge') => void
  handleFileChange: (e: any, type: 'thumbnail' | 'badge') => void
}) {
  return (
    <Box className="flex justify-start items-start gap-5 w-full h-full ">
      <SourceSection
        state={state}
        updateState={updateState}
        pageSelector={pageSelector}
        fromGlobal={true}/>
      <DetailsSection
        selectSingleAsset={selectSingleAsset}
        handleFileChange={handleFileChange}
        state={state}
        updateState={updateState}
        fromGlobal={true}
      />
    </Box>
  )
}

export default GlobalNotificationStep1
