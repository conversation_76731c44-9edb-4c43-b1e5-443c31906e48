export interface Thumbnail {
  url: string;
  status: string;
}

export interface BaseNotiState {
  status: string;
  activeStep: number;
  isLoading: boolean;
  externalLink: string;
  title: string;
  url: string;
  description: string;
  thumbnail: Thumbnail;
  typeOfNotification: string;
  notificationType: NotificationTypes;
  dataFetching: boolean;
  notificationDuration: string;
  showConfirmBox: boolean;
  titleSource: string;
  descriptionSource: string;
  ctaText: string
  ctaTarget: '_blank' | '_self'
  notificationCategory: string[]
  isSystemNotification:boolean
  isSystemNotificationDismissible:boolean
  badge:Thumbnail,
  attentionRequired:boolean
  groupingCategory:string
  renotify: boolean
}

export interface DomainNotiState extends BaseNotiState {
  internalName: string;
  stickyNotificationTemplate: string;
  selectedPage: any; // Replace `any` with a more specific type if possible
  specificPages: any[]; // Replace `any` with a more specific type if possible
  carouselData: any[]; // Replace `any` with a more specific type if possible
  formFloatingPage: any; // Replace `any` with a more specific type if possible
  categoriesPages: any[]; // Replace `any` with a more specific type if possible
  isGloballyEnabled: boolean;
  selectedCTA: any; // Replace `any` with a more specific type if possible
  isEnabled: boolean;
  isClosable: boolean;
  bgColor: string;
}

export interface GlobalNotiState extends BaseNotiState {
  extension: any; // Replace `any` with a more specific type if possible
  selectedCTA: any; // Replace `any` with a more specific type if possible
  selectedPage: any; // Replace `any` with a more specific type if possible
}

export type UpdateNotiStateFunction<T> = (
  updates: Partial<T>,
) => void;

export type NotificationTypes = 'browser' | 'page' | 'both';


export interface RealTimeNotificationPayload {
  title: string,
  body: string
  icon: string
  url: string
  id: string
  domain: string
  type: string,
  duration: string,
  timeStamp: Date,
  ctaText: string,
  ctaTarget: string,
  category: string,
  isSystemNotification:string
  attentionRequired:string
  badge:string
  tag:string
  renotify: string
  isDismissible:string
}