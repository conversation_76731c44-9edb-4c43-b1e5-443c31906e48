import {
  FormControl,
  Select,
  TextInput
} from '@contentful/f36-components'
import React, { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { defaults } from '../utils'

const MarkLineFields = (props: any) => {
  const {
    index,
    markLineFieldsData,
    handleFieldsUpdate,
    headers,
    chartPreference,
    categories,
    axisPreference,
    template
  } = props
  /*  const[isSettingMoadalOpen,setIsSettingMoadalOpen]=useState(false)
    const [fontStyles,setFontStyles]=useState('') */
  const [data, setData] = useState(markLineFieldsData ?? {})
  const [categoriesData, setCategoriesData] = useState<any>([])
  const dvGlobalData: any = useSelector(
    (state: RootState) => state.dvDashboard.globalConfigData
  )
  const preferences = dvGlobalData?.DataVisualization?.[template] ?? defaults?.[template]
  const axisData = { ...preferences?.axes, ...axisPreference }
  const chartData = {
    ...preferences?.chart, ...chartPreference, 'x-axis': {
      ...preferences?.chart?.['x-axis'],
      ...(chartPreference?.['x-axis'] ?? {}),
    },
    'y-axis': {
      ...preferences?.chart?.['y-axis'],
      ...(chartPreference?.['y-axis'] ?? {}),
    },
  }

  useEffect(() => {
    const i = headers.findIndex((header) => header === data.series)
    const axisIndex = chartData?.[data?.axis]
      ? chartData?.[data?.axis]?.[`dimension${i + 1}`] || 1
      : 1
    const axis=axisData?.reverseAxisType ? data?.axis === 'x-axis' ? 'y-axis' : 'x-axis' : data?.axis
    const cat = axisData?.[axis]?.[axisIndex - 1]?.type === 'category' && categories?.[`${axis}-${axisIndex}`]
    setCategoriesData(cat)
  }, [axisData?.reverseAxisType])

  const handleAxesFieldsDataChange = (key: string, value: any) => {
    if (key === 'series') {
      const i = headers.findIndex((header) => header === value)
      const axisIndex = chartData?.[data?.axis]
        ? chartData?.[data?.axis]?.[`dimension${i + 1}`] || 1
        : 1
        const axis=axisData?.reverseAxisType ? data?.axis === 'x-axis' ? 'y-axis' : 'x-axis' : data?.axis
      const cat = axisData?.[axis]?.[axisIndex - 1]?.type === 'category' && categories?.[`${axis}-${axisIndex}`]
      setCategoriesData(cat)
    } else if (key === 'axis') {
      const i = headers.findIndex((header) => header === data.series)
      const axisIndex = chartData?.[value]
        ? chartData?.[value]?.[`dimension${i + 1}`] || 1
        : 1
      const cat = axisData?.[value]?.[axisIndex - 1]?.type === 'category' && categories?.[`${value}-${axisIndex}`]
      setCategoriesData(cat)
    }
    const dataToUpdate = {
      ...data,
      [key]: value,
    }
    setData(dataToUpdate)
    handleFieldsUpdate(dataToUpdate)
  }

  return (
    <>
      <div className='markLineDiv'>
        <FormControl
          id={`dv-pri-name of Markline ${index}`}
          className='w-100 fieldsFormControl'
        >
          <FormControl.Label>Markline title</FormControl.Label>
          <TextInput
            aria-label={`dv-name of Markline-input ${index}`}
            id={`dv-name of Markline-input ${index}`}
            placeholder='Mark line name'
            type='text'
            value={data?.MarkLineName}
            onChange={(e) =>
              handleAxesFieldsDataChange('MarkLineName', e.target.value)
            }
          />
        </FormControl>
        <FormControl
          id={`dv-select-axis-${index}`}
          className='w-100 fieldsFormControl'
        >
          <FormControl.Label> Select axis</FormControl.Label>
          <Select
            id='axis-controlled'
            name='axis-controlled'
            value={data?.axis || ''}
            onChange={(e) => handleAxesFieldsDataChange('axis', e.target.value)}
            style={{
              width: '100%',
            }}
          >
            <Select.Option value='' isDisabled>
              Select axis
            </Select.Option>
            {['x-axis', 'y-axis'].map((type) => (
              <Select.Option value={type}>{type}</Select.Option>
            ))}
          </Select>
        </FormControl>
        <FormControl
          id={`dv-series-${index}`}
          className='w-100 fieldsFormControl'
        >
          <FormControl.Label> Select legend series</FormControl.Label>
          <Select
            id='series-controlled'
            name='series-controlled'
            value={data?.series || ''}
            onChange={(e) =>
              handleAxesFieldsDataChange('series', e.target.value)
            }
            style={{
              width: '100%',
            }}
          >
            <Select.Option value='' isDisabled>
              Select series
            </Select.Option>
            {headers.map((type) => (
              <Select.Option key={type} value={type}>
                {type}
              </Select.Option>
            ))}
          </Select>
        </FormControl>
        {categoriesData ? (
          <FormControl
            id={`dv-pri-axis-point of Markline ${index}`}
            className='w-100 fieldsFormControl'
          ><FormControl.Label> Select axis point</FormControl.Label>
            <Select
              id='Type-controlled'
              name='Type-controlled'
              value={data?.axisPont || ''}
              onChange={(e) =>
                handleAxesFieldsDataChange('axisPont', e.target.value)
              }
              style={{
                width: '100%',
              }}
            >
              <Select.Option value='' isDisabled>
                Axis Point
              </Select.Option>
              {categoriesData.map((type) => (
                <Select.Option value={type}>{type}</Select.Option>
              ))}
            </Select>
          </FormControl>
        ) : (
          <FormControl
            id={`dv-pri-axis-point of Markline ${index}`}
            className='w-100 fieldsFormControl'
            >
              <FormControl.Label> Select axis point</FormControl.Label>
            <TextInput
              aria-label={`'dv-axis-point of Markline ${index} input'`}
              id={`'dv-axis-point of Markline ${index} input'`}
                placeholder='Axis Point'
              type='text'
              value={data?.axisPont}
              onChange={(e) =>
                handleAxesFieldsDataChange('axisPont', e.target.value)
              }
            />
          </FormControl>
        )}
      </div>
      {/* <StylingModal 
    isOpen={isSettingMoadalOpen}
    isBgColorShow
        onStyleApply={(data)=>{
          handleAxesFieldsDataChange(fontStyles,data)
        setIsSettingMoadalOpen(false)
        }
        } styles={data?.[fontStyles]} handleClose={()=>setIsSettingMoadalOpen(false)}/>  */}
    </>
  )
}

export default MarkLineFields
