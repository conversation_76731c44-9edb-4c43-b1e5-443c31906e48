import * as React from 'react'

import { Card, SectionHeading } from '@contentful/f36-components'
import { css } from 'emotion'

const styles = {
  card: css({
    position: 'relative',
  }),
}

/**
 * A card that is displayed when the user selects a resource type that is not yet supported.
 *
 * @param {{ linkType: string, isSelected?: boolean }} props
 * @param {string} props.linkType The type of the resource (e.g. 'Entry', 'Asset', etc.)
 * @param {boolean} [props.isSelected=false] Whether the card should be displayed as selected
 */
export function UnsupportedEntityCard(props: {
  linkType: string
  isSelected?: boolean
}) {
  return (
    <Card className={styles.card} isSelected={props.isSelected}>
      <SectionHeading marginBottom='none'>
        Resource type {props.linkType} is currently not supported
      </SectionHeading>
    </Card>
  )
}
