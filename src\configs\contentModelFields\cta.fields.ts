export const CTAFields = {
  Primary: [
    'text',
    'externalLink',
    'icon',
    'iconPlacement',
    'isLightMode',
    'openInNewTab',
    'IsChevron2Arrow',
    'image',
    'actionContent',
    'sectionId',
    'internalLink',
    'asset',
  ],
  Secondary: [
    'text',
    'externalLink',
    'icon',
    'iconPlacement',
    'isLightMode',
    'openInNewTab',
    'IsChevron2Arrow',
    'image',
    'actionContent',
    'sectionId',
    'internalLink',
    'asset',
  ],
  Tertiary: [
    'text',
    'externalLink',
    'icon',
    'iconPlacement',
    'isLightMode',
    'openInNewTab',
    'IsChevron2Arrow',
    'image',
    'actionContent',
    'sectionId',
    'internalLink',
    'asset',
  ],
  Tertiary2: [
    'text',
    'externalLink',
    'icon',
    'iconPlacement',
    'isLightMode',
    'openInNewTab',
    'IsChevron2Arrow',
    'image',
    'actionContent',
    'sectionId',
    'internalLink',
    'asset',
  ],
  Tertiary3: [
    'text',
    'externalLink',
    'icon',
    'iconPlacement',
    'isLightMode',
    'openInNewTab',
    'IsChevron2Arrow',
    'image',
    'actionContent',
    'sectionId',
    'internalLink',
    'asset',
  ],
  Link: [
    'text',
    'externalLink',
    'isLightMode',
    'openInNewTab',
    'isLightMode',
    'openInNewTab',
    'asset',
    'actionContent',
  ],
  GenericLink: [
    'text',
    'externalLink',
    'isLightMode',
    'openInNewTab',
    'isLightMode',
    'openInNewTab',
    'asset',
    'actionContent',
  ],
  Submit: [
    'text',
    'icon',
    'iconPlacement',
    'IsChevron2Arrow',
    'isLightMode',
    'actionContent',
  ],
}
